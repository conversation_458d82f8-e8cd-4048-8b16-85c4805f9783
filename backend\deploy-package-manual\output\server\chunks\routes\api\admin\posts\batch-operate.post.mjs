import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, r as readBody, f as createError, q as query } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const batchOperate_post = defineEventHandler(async (event) => {
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const body = await readBody(event);
    const { ids, action } = body;
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u8BF7\u9009\u62E9\u8981\u64CD\u4F5C\u7684\u63A8\u6587"
      });
    }
    const validActions = ["publish", "unpublish", "delete", "archive"];
    if (!validActions.includes(action)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u64CD\u4F5C\u7C7B\u578B"
      });
    }
    const validIds = ids.filter((id) => !isNaN(Number(id)));
    if (validIds.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u63A8\u6587ID"
      });
    }
    const placeholders = validIds.map(() => "?").join(",");
    const existingPosts = await query(
      `SELECT id, title FROM posts WHERE id IN (${placeholders})`,
      validIds
    );
    if (existingPosts.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u672A\u627E\u5230\u8981\u64CD\u4F5C\u7684\u63A8\u6587"
      });
    }
    await query("START TRANSACTION");
    try {
      let affectedCount = 0;
      const actionText = {
        publish: "\u53D1\u5E03",
        unpublish: "\u4E0B\u7EBF",
        delete: "\u5220\u9664",
        archive: "\u5F52\u6863"
      }[action];
      if (action === "delete") {
        for (const postId of validIds) {
          const associatedTags = await query(
            "SELECT tag_id FROM post_tags WHERE post_id = ?",
            [postId]
          );
          for (const tag of associatedTags) {
            await query(
              "UPDATE post_tag_definitions SET usage_count = GREATEST(usage_count - 1, 0) WHERE id = ?",
              [tag.tag_id]
            );
          }
          await query("DELETE FROM post_tags WHERE post_id = ?", [postId]);
        }
        const deleteResult = await query(
          `DELETE FROM posts WHERE id IN (${placeholders})`,
          validIds
        );
        affectedCount = deleteResult.affectedRows;
      } else {
        let newStatus;
        let isOnline;
        let publishDate = null;
        switch (action) {
          case "publish":
            newStatus = "published";
            isOnline = 1;
            publishDate = (/* @__PURE__ */ new Date()).toISOString().slice(0, 19).replace("T", " ");
            break;
          case "unpublish":
            newStatus = "draft";
            isOnline = 0;
            break;
          case "archive":
            newStatus = "archived";
            isOnline = 0;
            break;
        }
        const updateResult = await query(
          `UPDATE posts
           SET status = ?, is_online = ?, publish_date = ?, updated_at = NOW()
           WHERE id IN (${placeholders})`,
          [newStatus, isOnline, publishDate, ...validIds]
        );
        affectedCount = updateResult.affectedRows;
      }
      await query("COMMIT");
      const postTitles = existingPosts.map((post) => post.title).join(", ");
      return {
        success: true,
        message: `\u6279\u91CF${actionText}\u6210\u529F\uFF0C\u5171\u5904\u7406 ${affectedCount} \u6761\u63A8\u6587`,
        data: {
          action,
          affectedCount,
          processedIds: validIds
        }
      };
    } catch (error) {
      await query("ROLLBACK");
      throw error;
    }
  } catch (error) {
    console.error("\u6279\u91CF\u64CD\u4F5C\u63A8\u6587\u5931\u8D25:", error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.message || "\u6279\u91CF\u64CD\u4F5C\u63A8\u6587\u5931\u8D25"
    });
  }
});

export { batchOperate_post as default };
//# sourceMappingURL=batch-operate.post.mjs.map
