import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, r as readBody, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const contact_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u4FEE\u6539\u7CFB\u7EDF\u8BBE\u7F6E"
      });
    }
    const body = await readBody(event);
    const {
      onlineQrCode,
      paymentQrCode,
      contactText,
      contactAddress,
      contactEmail,
      contactPhone
    } = body;
    if (contactEmail && contactEmail.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(contactEmail.trim())) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u90AE\u7BB1\u683C\u5F0F\u4E0D\u6B63\u786E"
        });
      }
    }
    if (contactPhone && contactPhone.trim()) {
      const phoneRegex = /^1[3-9]\d{9}$|^0\d{2,3}[-\s]?\d{7,8}$|^400[-\s]?\d{3}[-\s]?\d{4}$/;
      const cleanPhone = contactPhone.trim().replace(/\s/g, "");
      if (!phoneRegex.test(contactPhone.trim()) && !phoneRegex.test(cleanPhone)) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u7535\u8BDD\u53F7\u7801\u683C\u5F0F\u4E0D\u6B63\u786E\uFF0C\u652F\u6301\u624B\u673A\u53F7\u3001\u56FA\u8BDD\u3001400\u7535\u8BDD\u7B49\u683C\u5F0F"
        });
      }
    }
    const contactSettings = {
      onlineQrCode: onlineQrCode || "",
      paymentQrCode: paymentQrCode || "",
      contactText: contactText ? contactText.trim() : "",
      contactAddress: contactAddress ? contactAddress.trim() : "",
      contactEmail: contactEmail ? contactEmail.trim() : "",
      contactPhone: contactPhone ? contactPhone.trim() : ""
    };
    const settingsJson = JSON.stringify(contactSettings);
    const existingSettings = await query(
      "SELECT id FROM system_settings WHERE setting_key = ?",
      ["contact_settings"]
    );
    if (existingSettings.length > 0) {
      await query(
        "UPDATE system_settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?",
        [settingsJson, "contact_settings"]
      );
    } else {
      await query(
        "INSERT INTO system_settings (setting_key, setting_value, created_at, updated_at) VALUES (?, ?, NOW(), NOW())",
        ["contact_settings", settingsJson]
      );
    }
    await logAuditAction({
      action: "ADMIN_UPDATE_CONTACT_SETTINGS",
      description: "\u7BA1\u7406\u5458\u66F4\u65B0\u8054\u7CFB\u65B9\u5F0F\u8BBE\u7F6E",
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        updatedSettings: {
          ...contactSettings,
          // 不记录敏感信息的完整内容
          contactEmail: contactEmail ? "***@***" : "",
          contactPhone: contactPhone ? "***" : ""
        }
      }
    });
    logger.info("\u7BA1\u7406\u5458\u66F4\u65B0\u8054\u7CFB\u65B9\u5F0F\u8BBE\u7F6E\u6210\u529F", {
      adminId: admin.id,
      adminUsername: admin.username,
      hasOnlineQrCode: !!contactSettings.onlineQrCode,
      hasPaymentQrCode: !!contactSettings.paymentQrCode,
      hasContactText: !!contactSettings.contactText,
      hasContactAddress: !!contactSettings.contactAddress,
      hasContactEmail: !!contactSettings.contactEmail,
      hasContactPhone: !!contactSettings.contactPhone
    });
    return {
      success: true,
      message: "\u8054\u7CFB\u65B9\u5F0F\u8BBE\u7F6E\u66F4\u65B0\u6210\u529F",
      data: contactSettings
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u8054\u7CFB\u65B9\u5F0F\u8BBE\u7F6E\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { contact_post as default };
//# sourceMappingURL=contact.post.mjs.map
