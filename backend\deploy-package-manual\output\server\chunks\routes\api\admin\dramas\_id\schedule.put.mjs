import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, f as createError, r as readBody, q as query } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const schedule_put = defineEventHandler(async (event) => {
  try {
    const dramaId = getRouterParam(event, "id");
    if (!dramaId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u77ED\u5267ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const id = parseInt(dramaId);
    if (isNaN(id) || id <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u77ED\u5267ID"
      });
    }
    const body = await readBody(event);
    const { schedule } = body;
    if (!schedule) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5236\u4F5C\u6392\u671F\u6570\u636E\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const dramaExists = await query(
      "SELECT id FROM drama_series WHERE id = ?",
      [id]
    );
    if (dramaExists.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u77ED\u5267\u4E0D\u5B58\u5728"
      });
    }
    const existingSchedule = await query(
      "SELECT id FROM drama_production_schedule WHERE drama_id = ?",
      [id]
    );
    if (existingSchedule.length > 0) {
      await query(`
        UPDATE drama_production_schedule 
        SET 
          schedule_pre_production = ?,
          schedule_filming = ?,
          schedule_post_production = ?,
          expected_release_date = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE drama_id = ?
      `, [
        schedule.schedulePreProduction || null,
        schedule.scheduleFilming || null,
        schedule.schedulePostProduction || null,
        schedule.expectedReleaseDate || null,
        id
      ]);
    } else {
      await query(`
        INSERT INTO drama_production_schedule (
          drama_id,
          schedule_pre_production,
          schedule_filming,
          schedule_post_production,
          expected_release_date
        ) VALUES (?, ?, ?, ?, ?)
      `, [
        id,
        schedule.schedulePreProduction || null,
        schedule.scheduleFilming || null,
        schedule.schedulePostProduction || null,
        schedule.expectedReleaseDate || null
      ]);
    }
    return {
      success: true,
      message: "\u5236\u4F5C\u6392\u671F\u66F4\u65B0\u6210\u529F",
      data: {
        dramaId: id,
        schedule: {
          schedulePreProduction: schedule.schedulePreProduction,
          scheduleFilming: schedule.scheduleFilming,
          schedulePostProduction: schedule.schedulePostProduction,
          expectedReleaseDate: schedule.expectedReleaseDate
        }
      }
    };
  } catch (error) {
    console.error("\u66F4\u65B0\u5236\u4F5C\u6392\u671F\u5931\u8D25:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { schedule_put as default };
//# sourceMappingURL=schedule.put.mjs.map
