import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, j as getR<PERSON>er<PERSON>aram, r as readBody, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../_/nitro.mjs';
import { c as validateId } from '../../../../../_/validators.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const status_put = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.USER_EDIT);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u4FEE\u6539\u7528\u6237\u72B6\u6001"
      });
    }
    const userId = validateId(getRouterParam(event, "id"));
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u7528\u6237ID"
      });
    }
    const body = await readBody(event);
    const { status } = body;
    if (status !== "active" && status !== "inactive") {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u72B6\u6001\u503C\uFF0C\u53EA\u80FD\u662F active \u6216 inactive"
      });
    }
    const users = await query(
      "SELECT id, username, status FROM users WHERE id = ?",
      [userId]
    );
    if (users.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u7528\u6237\u4E0D\u5B58\u5728"
      });
    }
    const user = users[0];
    const statusValue = status === "active" ? 1 : 0;
    if (user.status === statusValue) {
      return {
        success: true,
        message: "\u7528\u6237\u72B6\u6001\u65E0\u9700\u66F4\u6539"
      };
    }
    await query(
      "UPDATE users SET status = ?, updated_at = NOW() WHERE id = ?",
      [statusValue, userId]
    );
    await logAuditAction({
      action: "ADMIN_UPDATE_USER_STATUS",
      description: `\u7BA1\u7406\u5458\u4FEE\u6539\u7528\u6237\u72B6\u6001: ${user.username} -> ${status}`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        targetUserId: userId,
        targetUsername: user.username,
        oldStatus: user.status === 1 ? "active" : "inactive",
        newStatus: status
      }
    });
    logger.info("\u7BA1\u7406\u5458\u4FEE\u6539\u7528\u6237\u72B6\u6001\u6210\u529F", {
      adminId: admin.id,
      adminUsername: admin.username,
      targetUserId: userId,
      targetUsername: user.username,
      newStatus: status
    });
    return {
      success: true,
      message: "\u7528\u6237\u72B6\u6001\u4FEE\u6539\u6210\u529F"
    };
  } catch (error) {
    logger.error("\u4FEE\u6539\u7528\u6237\u72B6\u6001\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      userId: getRouterParam(event, "id"),
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { status_put as default };
//# sourceMappingURL=status.put.mjs.map
