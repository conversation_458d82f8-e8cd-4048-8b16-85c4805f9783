import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, g as getQuery, q as query } from '../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const query$1 = getQuery(event);
    const {
      page = 1,
      pageSize = 10,
      search = "",
      roleType = "",
      status = ""
    } = query$1;
    console.log("\u827A\u4EBA\u7BA1\u7406\u641C\u7D22\u53C2\u6570:", {
      page,
      pageSize,
      search,
      roleType,
      status,
      originalQuery: query$1
    });
    const pageNum = Math.max(1, parseInt(page) || 1);
    const pageSizeNum = Math.min(100, Math.max(1, parseInt(pageSize) || 10));
    const offset = (pageNum - 1) * pageSizeNum;
    let whereClause = "WHERE 1=1";
    const params = [];
    if (search) {
      whereClause += " AND (name LIKE ? OR bio LIKE ? OR tags LIKE ?)";
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern, searchPattern);
    }
    if (roleType) {
      whereClause += " AND role_type = ?";
      params.push(roleType);
    }
    if (status) {
      if (status === "active") {
        whereClause += " AND is_active = 1";
      } else if (status === "inactive") {
        whereClause += " AND is_active = 0";
      }
    }
    const countResult = await query(
      `SELECT COUNT(*) as total FROM actors ${whereClause}`,
      params
    );
    const total = ((_a = countResult[0]) == null ? void 0 : _a.total) || 0;
    const artists = await query(
      `SELECT id, name, avatar_url, bio, tags, role_type, sort_order, is_active, created_at, updated_at
       FROM actors 
       ${whereClause}
       ORDER BY sort_order ASC, created_at DESC
       LIMIT ? OFFSET ?`,
      [...params, pageSizeNum, offset]
    );
    const formattedArtists = artists.map((artist) => ({
      id: artist.id,
      name: artist.name,
      avatarUrl: artist.avatar_url,
      bio: artist.bio,
      tags: artist.tags,
      roleType: artist.role_type,
      sortOrder: artist.sort_order,
      status: artist.is_active === 1 ? "active" : "inactive",
      createdAt: artist.created_at,
      updatedAt: artist.updated_at
    }));
    const totalPages = Math.ceil(total / pageSizeNum);
    console.log("\u8FD4\u56DE\u827A\u4EBA\u5217\u8868\u6570\u636E:", { total, count: formattedArtists.length });
    return {
      success: true,
      data: {
        list: formattedArtists,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total,
          totalPages
        }
      }
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u827A\u4EBA\u5217\u8868\u5931\u8D25:", error);
    return {
      success: false,
      error: error.message,
      data: {
        list: [],
        pagination: {
          page: 1,
          pageSize: 10,
          total: 0,
          totalPages: 0
        }
      }
    };
  }
});

export { index_get as default };
//# sourceMappingURL=index.get10.mjs.map
