import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON> } from '../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _____ = defineEventHandler((event) => {
  var _a;
  if ((_a = event.node.req.url) == null ? void 0 : _a.startsWith("/api/")) {
    return;
  }
  return `
<h1>\u5267\u6295\u6295\u540E\u7AEF\u670D\u52A1</h1>
<h2>API\u670D\u52A1\u6B63\u5728\u8FD0\u884C</h2>
<ul>
<li><a href="/api/health">/api/health</a> - \u5065\u5EB7\u68C0\u67E5</li>
<li><a href="/api/public/config">/api/public/config</a> - \u516C\u5171\u914D\u7F6E</li>
<li><a href="/api/public/banners">/api/public/banners</a> - \u516C\u5F00\u6A2A\u5E45</li>
<li><a href="/api/auth/login">/api/auth/login</a> - \u7528\u6237\u767B\u5F55</li>
<li><a href="/api/auth/admin/login">/api/auth/admin/login</a> - \u7BA1\u7406\u5458\u767B\u5F55</li>
</ul>
<p>\u66F4\u591AAPI\u8BF7\u53C2\u8003\u6587\u6863</p>
`;
});

export { _____ as default };
//# sourceMappingURL=_..._.mjs.map
