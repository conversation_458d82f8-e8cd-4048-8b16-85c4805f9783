import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, j as getRouter<PERSON>aram, r as readBody, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import { b as validateFundData } from '../../../../_/validators.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__put = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const fundId = getRouterParam(event, "id");
    if (!fundId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u57FA\u91D1ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const body = await readBody(event);
    const validation = validateFundData(body, true);
    if (!validation.isValid) {
      const errorMessages = Object.values(validation.errors).join(", ");
      throw createError({
        statusCode: 400,
        statusMessage: errorMessages
      });
    }
    const existingFund = await query(
      "SELECT id, code FROM funds WHERE id = ?",
      [fundId]
    );
    if (existingFund.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u57FA\u91D1\u4E0D\u5B58\u5728"
      });
    }
    const {
      code,
      title,
      description,
      type,
      risk,
      min_investment,
      period,
      target_size,
      raised_amount,
      expected_return,
      min_holding_period,
      risk_description,
      establish_date,
      exit_date,
      manager,
      trustee,
      redemption_policy,
      investment_strategy,
      is_published
    } = body;
    if (code && code !== existingFund[0].code) {
      const codeConflict = await query(
        "SELECT id FROM funds WHERE code = ? AND id != ?",
        [code, fundId]
      );
      if (codeConflict.length > 0) {
        throw createError({
          statusCode: 409,
          statusMessage: "\u57FA\u91D1\u4EE3\u7801\u5DF2\u5B58\u5728"
        });
      }
    }
    const updateFields = [];
    const updateValues = [];
    if (code !== void 0) {
      updateFields.push("code = ?");
      updateValues.push(code);
    }
    if (title !== void 0) {
      updateFields.push("title = ?");
      updateValues.push(title);
    }
    if (description !== void 0) {
      updateFields.push("description = ?");
      updateValues.push(description);
    }
    if (type !== void 0) {
      updateFields.push("type = ?");
      updateValues.push(type);
    }
    if (risk !== void 0) {
      updateFields.push("risk = ?");
      updateValues.push(risk);
    }
    if (min_investment !== void 0) {
      updateFields.push("min_investment = ?");
      updateValues.push(min_investment);
    }
    if (period !== void 0) {
      updateFields.push("period = ?");
      updateValues.push(period);
    }
    if (target_size !== void 0) {
      updateFields.push("target_size = ?");
      updateValues.push(target_size);
    }
    if (raised_amount !== void 0) {
      updateFields.push("raised_amount = ?");
      updateValues.push(raised_amount);
    }
    if (expected_return !== void 0) {
      updateFields.push("expected_return = ?");
      updateValues.push(expected_return);
    }
    if (min_holding_period !== void 0) {
      updateFields.push("min_holding_period = ?");
      updateValues.push(min_holding_period);
    }
    if (risk_description !== void 0) {
      updateFields.push("risk_description = ?");
      updateValues.push(risk_description);
    }
    if (establish_date !== void 0) {
      updateFields.push("establish_date = ?");
      updateValues.push(establish_date);
    }
    if (exit_date !== void 0) {
      updateFields.push("exit_date = ?");
      updateValues.push(exit_date);
    }
    if (manager !== void 0) {
      updateFields.push("manager = ?");
      updateValues.push(manager);
    }
    if (trustee !== void 0) {
      updateFields.push("trustee = ?");
      updateValues.push(trustee);
    }
    if (redemption_policy !== void 0) {
      updateFields.push("redemption_policy = ?");
      updateValues.push(redemption_policy);
    }
    if (investment_strategy !== void 0) {
      updateFields.push("investment_strategy = ?");
      updateValues.push(investment_strategy);
    }
    if (is_published !== void 0) {
      updateFields.push("is_published = ?");
      updateValues.push(is_published ? 1 : 0);
    }
    if (updateFields.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6CA1\u6709\u9700\u8981\u66F4\u65B0\u7684\u5B57\u6BB5"
      });
    }
    updateFields.push("updated_at = NOW()");
    updateValues.push(fundId);
    await query(
      `UPDATE funds SET ${updateFields.join(", ")} WHERE id = ?`,
      updateValues
    );
    await logAuditAction({
      action: "ADMIN_UPDATE_FUND",
      description: `\u7BA1\u7406\u5458\u66F4\u65B0\u57FA\u91D1: ${title || existingFund[0].title} (ID: ${fundId})`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        fundId: parseInt(fundId),
        updatedFields: Object.keys(body),
        ...body
      }
    });
    logger.info("\u7BA1\u7406\u5458\u66F4\u65B0\u57FA\u91D1\u6210\u529F", {
      adminId: admin.id,
      adminUsername: admin.username,
      fundId,
      updatedFields: Object.keys(body)
    });
    return {
      success: true,
      message: "\u57FA\u91D1\u66F4\u65B0\u6210\u529F"
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u57FA\u91D1\u5931\u8D25", {
      error: error.message,
      fundId: getRouterParam(event, "id"),
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { _id__put as default };
//# sourceMappingURL=_id_.put.mjs.map
