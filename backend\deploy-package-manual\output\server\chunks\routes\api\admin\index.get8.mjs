import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, g as getQuery, q as query, o as logAdminAction, l as logger, e as getClientIP, f as createError } from '../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_get = defineEventHandler(async (event) => {
  var _a, _b;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const query_params = getQuery(event);
    const {
      page = 1,
      pageSize = 20,
      status,
      category,
      search,
      author,
      featured,
      startDate,
      endDate,
      orderBy = "created_at",
      orderDirection = "DESC"
    } = query_params;
    const pageNum = Math.max(1, parseInt(page) || 1);
    const pageSizeNum = Math.min(100, Math.max(1, parseInt(pageSize) || 20));
    const offset = (pageNum - 1) * pageSizeNum;
    let whereConditions = [];
    let queryParams = [];
    if (status && ["draft", "pending", "published", "archived"].includes(status)) {
      whereConditions.push("n.status = ?");
      queryParams.push(status);
    }
    if (category && !isNaN(parseInt(category))) {
      whereConditions.push("n.category_id = ?");
      queryParams.push(parseInt(category));
    }
    if (author && typeof author === "string" && author.trim()) {
      whereConditions.push("n.author LIKE ?");
      queryParams.push(`%${author.trim()}%`);
    }
    if (featured === "true") {
      whereConditions.push("n.is_featured = 1");
    } else if (featured === "false") {
      whereConditions.push("n.is_featured = 0");
    }
    if (search && typeof search === "string" && search.trim()) {
      whereConditions.push("(n.title LIKE ? OR n.summary LIKE ? OR n.content LIKE ?)");
      const searchTerm = `%${search.trim()}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm);
    }
    if (startDate && typeof startDate === "string") {
      whereConditions.push("DATE(n.created_at) >= ?");
      queryParams.push(startDate);
    }
    if (endDate && typeof endDate === "string") {
      whereConditions.push("DATE(n.created_at) <= ?");
      queryParams.push(endDate);
    }
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(" AND ")}` : "";
    const allowedOrderBy = ["created_at", "updated_at", "publish_date", "view_count", "title", "status"];
    const orderByField = allowedOrderBy.includes(orderBy) ? orderBy : "created_at";
    const orderDir = orderDirection === "ASC" ? "ASC" : "DESC";
    const newsQuery = `
      SELECT
        n.id,
        n.title,
        n.summary,
        n.content,
        n.cover_image_url,
        n.author,
        n.source_url,
        n.status,
        n.is_featured,
        n.view_count,
        n.publish_date,
        n.created_at,
        n.updated_at,
        nc.id as category_id,
        nc.name as category_name,
        nc.slug as category_slug
      FROM news n
      LEFT JOIN news_categories nc ON n.category_id = nc.id
      ${whereClause}
      ORDER BY n.${orderByField} ${orderDir}
      LIMIT ${pageSizeNum} OFFSET ${offset}
    `;
    const countQuery = `
      SELECT COUNT(*) as total
      FROM news n
      LEFT JOIN news_categories nc ON n.category_id = nc.id
      ${whereClause}
    `;
    const [newsList, countResult] = await Promise.all([
      query(newsQuery, queryParams),
      query(countQuery, queryParams)
    ]);
    const total = ((_a = countResult[0]) == null ? void 0 : _a.total) || 0;
    const totalPages = Math.ceil(total / pageSizeNum);
    const newsIds = newsList.map((news) => news.id);
    let newsTagsMap = {};
    if (newsIds.length > 0) {
      const tagsQuery = `
        SELECT
          ntr.news_id,
          nt.id as tag_id,
          nt.name as tag_name
        FROM news_tag_relations ntr
        JOIN news_tags nt ON ntr.tag_id = nt.id
        WHERE ntr.news_id IN (${newsIds.map(() => "?").join(",")})
        ORDER BY nt.name
      `;
      const tagResults = await query(tagsQuery, newsIds);
      tagResults.forEach((tag) => {
        if (!newsTagsMap[tag.news_id]) {
          newsTagsMap[tag.news_id] = [];
        }
        newsTagsMap[tag.news_id].push({
          id: tag.tag_id,
          name: tag.tag_name
        });
      });
    }
    const formattedNews = newsList.map((news) => ({
      id: news.id,
      title: news.title,
      summary: news.summary,
      content: news.content,
      coverImage: news.cover_image_url,
      author: news.author,
      sourceUrl: news.source_url,
      status: news.status,
      isFeatured: news.is_featured === 1,
      viewCount: news.view_count,
      publishDate: news.publish_date,
      createdAt: news.created_at,
      updatedAt: news.updated_at,
      category: news.category_id ? {
        id: news.category_id,
        name: news.category_name,
        slug: news.category_slug
      } : null,
      tags: newsTagsMap[news.id] || []
    }));
    await logAdminAction(admin.id, "news:list", "\u67E5\u770B\u65B0\u95FB\u5217\u8868", {
      filters: { status, category, search, author, featured },
      pagination: { page: pageNum, pageSize: pageSizeNum }
    });
    return {
      success: true,
      data: {
        list: formattedNews,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total,
          totalPages
        }
      }
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u65B0\u95FB\u5217\u8868\u5931\u8D25", {
      error: error.message,
      stack: error.stack,
      adminId: (_b = event.context.admin) == null ? void 0 : _b.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u83B7\u53D6\u65B0\u95FB\u5217\u8868\u5931\u8D25"
    });
  }
});

export { index_get as default };
//# sourceMappingURL=index.get8.mjs.map
