{"version": 3, "file": "permissions.post.mjs", "sources": ["../../../../../../../../api/admin/roles/[id]/permissions.post.ts"], "sourcesContent": null, "names": [], "mappings": ";;;;;;;;;;;;;;AAIA,yBAAA,kBAAA,CAAA,OAAA,KAAA,KAAA;;AACA,EAAA,IAAA;AAEA,IAAA,MAAA,YAAA,GAAA,uBAAA,KAAA,CAAA;AACA,IAAA,IAAA,CAAA,YAAA,EAAA;AACA,MAAA,MAAA,WAAA,CAAA;AAAA,QACA,UAAA,EAAA,GAAA;AAAA,QACA,aAAA,EAAA;AAAA,OACA,CAAA;AAAA,IACA;AAGA,IAAA,MAAA,gBAAA,MAAA,oBAAA,CAAA,YAAA,CAAA,EAAA,EAAA,YAAA,WAAA,CAAA;AACA,IAAA,IAAA,CAAA,aAAA,EAAA;AACA,MAAA,MAAA,WAAA,CAAA;AAAA,QACA,UAAA,EAAA,GAAA;AAAA,QACA,aAAA,EAAA;AAAA,OACA,CAAA;AAAA,IACA;AAGA,IAAA,MAAA,MAAA,GAAA,cAAA,CAAA,KAAA,EAAA,IAAA,CAAA;AACA,IAAA,IAAA,CAAA,MAAA,EAAA;AACA,MAAA,MAAA,WAAA,CAAA;AAAA,QACA,UAAA,EAAA,GAAA;AAAA,QACA,aAAA,EAAA;AAAA,OACA,CAAA;AAAA,IACA;AAGA,IAAA,MAAA,IAAA,GAAA,MAAA,QAAA,CAAA,KAAA,CAAA;AACA,IAAA,MAAA,EAAA,WAAA,GAAA,EAAA,EAAA,GAAA,IAAA;AAGA,IAAA,MAAA,YAAA,GAAA,MAAA,KAAA,CAAA;AAAA;AAAA,IAAA,CAAA,EAEA,CAAA,MAAA,CAAA,CAAA;AAEA,IAAA,IAAA,YAAA,CAAA,WAAA,CAAA,EAAA;AACA,MAAA,MAAA,WAAA,CAAA;AAAA,QACA,UAAA,EAAA,GAAA;AAAA,QACA,aAAA,EAAA;AAAA,OACA,CAAA;AAAA,IACA;AAEA,IAAA,MAAA,IAAA,GAAA,aAAA,CAAA,CAAA;AAGA,IAAA,IAAA,WAAA,CAAA,SAAA,CAAA,EAAA;AACA,MAAA,MAAA,gBAAA,GAAA,MAAA,KAAA,CAAA;AAAA,0DAAA,EACA,YAAA,GAAA,CAAA,MAAA,GAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;AAAA,MAAA,CAAA,EACA,WAAA,CAAA;AAEA,MAAA,MAAA,aAAA,gBAAA,CAAA,GAAA,CAAA,CAAA,CAAA,KAAA,EAAA,IAAA,CAAA;AACA,MAAA,MAAA,YAAA,GAAA,YAAA,MAAA,CAAA,CAAA,SAAA,CAAA,UAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA;AAEA,MAAA,IAAA,YAAA,CAAA,SAAA,CAAA,EAAA;AACA,QAAA,MAAA,WAAA,CAAA;AAAA,UACA,UAAA,EAAA,GAAA;AAAA,UACA,aAAA,EAAA,CAAA,4CAAA,EAAA,YAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;AAAA,SACA,CAAA;AAAA,MACA;AAAA,IACA;AAGA,IAAA,MAAA,UAAA,GAAA,MAAA,OAAA,EAAA,CAAA,aAAA,EAAA;AACA,IAAA,MAAA,WAAA,gBAAA,EAAA;AAEA,IAAA,IAAA;AAEA,MAAA,MAAA,WAAA,OAAA,CAAA;AAAA;AAAA,MAAA,CAAA,EAEA,CAAA,MAAA,CAAA,CAAA;AAGA,MAAA,IAAA,WAAA,CAAA,SAAA,CAAA,EAAA;AACA,QAAA,MAAA,gBAAA,GAAA,YAAA,GAAA,CAAA,CAAA,mBAAA,CAAA,MAAA,EAAA,cAAA,CAAA,CAAA;AACA,QAAA,MAAA,WAAA,KAAA,CAAA;AAAA;AAAA;AAAA,QAAA,CAAA,EAGA,CAAA,gBAAA,CAAA,CAAA;AAAA,MACA;AAEA,MAAA,MAAA,WAAA,MAAA,EAAA;AAEA,MAAA,MAAA,CAAA,KAAA,kDAAA,EAAA;AAAA,QACA,SAAA,YAAA,CAAA,EAAA;AAAA,QACA,MAAA;AAAA,QACA,UAAA,IAAA,CAAA,IAAA;AAAA,QACA,UAAA,IAAA,CAAA,IAAA;AAAA,QACA,iBAAA,WAAA,CAAA,MAAA;AAAA,QACA,WAAA;AAAA,QACA,EAAA,EAAA,YAAA,KAAA;AAAA,OACA,CAAA;AAEA,MAAA,OAAA;AAAA,QACA,OAAA,EAAA,IAAA;AAAA,QACA,IAAA,EAAA;AAAA,UACA,MAAA,EAAA,SAAA,MAAA,CAAA;AAAA,UACA;AAAA;AACA,OACA;AAAA,IAEA,SAAA,KAAA,EAAA;AACA,MAAA,MAAA,WAAA,QAAA,EAAA;AACA,MAAA,MAAA,KAAA;AAAA,IACA,CAAA,SAAA;AACA,MAAA,UAAA,CAAA,OAAA,EAAA;AAAA,IACA;AAAA,EAEA,SAAA,KAAA,EAAA;AACA,IAAA,MAAA,CAAA,MAAA,kDAAA,EAAA;AAAA,MACA,OAAA,KAAA,CAAA,OAAA;AAAA,MACA,MAAA,EAAA,cAAA,CAAA,KAAA,EAAA,IAAA,CAAA;AAAA,MACA,OAAA,EAAA,CAAA,EAAA,GAAA,KAAA,CAAA,OAAA,CAAA,KAAA,KAAA,IAAA,GAAA,MAAA,GAAA,EAAA,CAAA,EAAA;AAAA,MACA,EAAA,EAAA,YAAA,KAAA;AAAA,KACA,CAAA;AAEA,IAAA,IAAA,MAAA,UAAA,EAAA;AACA,MAAA,MAAA,KAAA;AAAA,IACA;AAEA,IAAA,MAAA,WAAA,CAAA;AAAA,MACA,UAAA,EAAA,GAAA;AAAA,MACA,aAAA,EAAA;AAAA,KACA,CAAA;AAAA,EACA;AACA,CAAA,CAAA;;;;"}