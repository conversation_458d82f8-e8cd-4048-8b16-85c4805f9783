{"name": "compatx", "version": "0.2.0", "description": "🌴 Gradual feature flags.", "repository": "unjs/compatx", "license": "MIT", "sideEffects": false, "type": "module", "exports": {".": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "types": "./dist/index.d.mts", "files": ["dist"], "scripts": {"build": "unbuild", "dev": "vitest dev", "lint": "eslint --cache . && prettier -c src test", "lint:fix": "automd && eslint --cache . --fix && prettier -c src test -w", "prepack": "pnpm build", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "test": "pnpm lint && pnpm test:types && vitest run --coverage", "test:types": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>"}, "devDependencies": {"@types/node": "^22.14.0", "@vitest/coverage-v8": "^3.1.1", "automd": "^0.4.0", "changelogen": "^0.6.1", "eslint": "^9.24.0", "eslint-config-unjs": "^0.4.2", "jiti": "^2.4.2", "prettier": "^3.5.3", "typescript": "^5.8.3", "unbuild": "^3.5.0", "vitest": "^3.1.1"}, "packageManager": "pnpm@10.7.1"}