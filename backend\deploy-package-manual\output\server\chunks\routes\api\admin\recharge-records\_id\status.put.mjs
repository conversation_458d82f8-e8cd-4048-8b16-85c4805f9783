import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, x as verifyAdminToken, f as createError, j as getRouter<PERSON>ara<PERSON>, r as readBody, q as query } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const status_put = defineEventHandler(async (event) => {
  try {
    const authResult = await verifyAdminToken(event);
    if (!authResult.success) {
      throw createError({
        statusCode: 401,
        statusMessage: authResult.message
      });
    }
    const recordId = getRouterParam(event, "id");
    if (!recordId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u7F3A\u5C11\u8BB0\u5F55ID"
      });
    }
    const body = await readBody(event);
    const { status } = body;
    const validStatuses = ["pending", "completed", "failed", "cancelled"];
    if (!status || !validStatuses.includes(status)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u72B6\u6001\u503C"
      });
    }
    const checkQuery = `
      SELECT id, status, user_id, amount, balance_after
      FROM user_asset_transactions 
      WHERE id = ? AND transaction_type = 'shells_in' AND related_type = 'recharge'
    `;
    const existingRecord = await query(checkQuery, [recordId]);
    if (existingRecord.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u5145\u503C\u8BB0\u5F55\u4E0D\u5B58\u5728"
      });
    }
    const record = existingRecord[0];
    if (record.status === status) {
      return {
        success: true,
        message: "\u72B6\u6001\u66F4\u65B0\u6210\u529F"
      };
    }
    const updateQuery = `
      UPDATE user_asset_transactions 
      SET status = ?, updated_at = NOW()
      WHERE id = ?
    `;
    await query(updateQuery, [status, recordId]);
    if (status === "completed" && record.status !== "completed") {
      const userAssetQuery = `
        SELECT shells_balance 
        FROM user_assets 
        WHERE user_id = ?
      `;
      const userAsset = await query(userAssetQuery, [record.user_id]);
      if (userAsset.length > 0) {
        const currentBalance = userAsset[0].shells_balance;
        if (currentBalance < record.balance_after) {
          const balanceDiff = record.balance_after - currentBalance;
          await query(`
            UPDATE user_assets 
            SET shells_balance = shells_balance + ?, updated_at = NOW()
            WHERE user_id = ?
          `, [balanceDiff, record.user_id]);
        }
      }
    }
    if (record.status === "completed" && status !== "completed") {
      const userAssetQuery = `
        SELECT shells_balance 
        FROM user_assets 
        WHERE user_id = ?
      `;
      const userAsset = await query(userAssetQuery, [record.user_id]);
      if (userAsset.length > 0) {
        const currentBalance = userAsset[0].shells_balance;
        if (currentBalance >= record.amount) {
          await query(`
            UPDATE user_assets 
            SET shells_balance = shells_balance - ?, updated_at = NOW()
            WHERE user_id = ?
          `, [record.amount, record.user_id]);
        }
      }
    }
    return {
      success: true,
      message: "\u72B6\u6001\u66F4\u65B0\u6210\u529F"
    };
  } catch (error) {
    console.error("\u66F4\u65B0\u5145\u503C\u8BB0\u5F55\u72B6\u6001\u5931\u8D25:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "\u66F4\u65B0\u5145\u503C\u8BB0\u5F55\u72B6\u6001\u5931\u8D25"
    });
  }
});

export { status_put as default };
//# sourceMappingURL=status.put.mjs.map
