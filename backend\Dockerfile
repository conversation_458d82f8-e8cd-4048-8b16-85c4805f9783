# 使用官方Node.js 18 Alpine镜像作为基础镜像
# Alpine版本更小更安全，适合生产环境
FROM node:18-alpine AS base

# 设置工作目录
# 为什么用/app：这是容器内的标准应用目录
WORKDIR /app

# 安装pnpm
# 为什么安装pnpm：项目使用pnpm管理依赖，保持一致性
RUN npm install -g pnpm

# 复制package.json和pnpm-lock.yaml
# 为什么先复制这两个文件：利用Docker层缓存，依赖不变时不重新安装
COPY package.json pnpm-lock.yaml ./

# 安装依赖
# --frozen-lockfile确保使用精确的依赖版本
# --production只安装生产依赖，减小镜像体积
RUN pnpm install --frozen-lockfile --production

# 复制源代码
# 为什么放在依赖安装后：源代码变化频繁，依赖变化少，利用缓存
COPY . .

# 构建应用
# 为什么需要构建：Nitro需要编译TypeScript和优化代码
RUN pnpm build

# 暴露端口
# 为什么是3001：这是应用配置的端口
EXPOSE 3001

# 创建非root用户运行应用
# 为什么：安全最佳实践，避免以root权限运行应用
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nitro -u 1001
USER nitro

# 启动命令
# 为什么用.output/server/index.mjs：这是Nitro构建后的入口文件
CMD ["node", ".output/server/index.mjs"]
