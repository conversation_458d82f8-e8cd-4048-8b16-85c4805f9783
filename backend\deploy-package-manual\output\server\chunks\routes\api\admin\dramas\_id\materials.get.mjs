import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, f as createError, q as query, g as getQuery, l as logger, e as getClientIP } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const materials_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const dramaId = getRouterParam(event, "id");
    if (!dramaId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u77ED\u5267ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const id = parseInt(dramaId);
    if (isNaN(id) || id <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u77ED\u5267ID"
      });
    }
    const dramaCheck = await query(`
      SELECT id FROM drama_series WHERE id = ?
    `, [id]);
    if (dramaCheck.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u77ED\u5267\u4E0D\u5B58\u5728"
      });
    }
    const queryParams = getQuery(event);
    const { type } = queryParams;
    let whereClause = "WHERE drama_id = ?";
    const params = [id];
    if (type && ["image", "video"].includes(type)) {
      whereClause += " AND type = ?";
      params.push(type);
    }
    const materials = await query(`
      SELECT id, drama_id, title, url, thumbnail, type, sort_order, created_at, updated_at
      FROM drama_materials 
      ${whereClause}
      ORDER BY sort_order ASC, created_at ASC
    `, params);
    const formattedMaterials = materials.map((material) => ({
      id: material.id,
      dramaId: material.drama_id,
      title: material.title,
      url: material.url,
      thumbnail: material.thumbnail,
      type: material.type,
      sortOrder: material.sort_order,
      createdAt: material.created_at,
      updatedAt: material.updated_at
    }));
    return {
      success: true,
      data: {
        list: formattedMaterials,
        total: formattedMaterials.length
      }
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u77ED\u5267\u7D20\u6750\u5217\u8868\u5931\u8D25", {
      error: error.message,
      dramaId: getRouterParam(event, "id"),
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { materials_get as default };
//# sourceMappingURL=materials.get.mjs.map
