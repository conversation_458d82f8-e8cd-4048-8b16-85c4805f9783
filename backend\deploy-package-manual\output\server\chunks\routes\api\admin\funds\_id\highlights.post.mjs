import { c as defineE<PERSON><PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, r as readBody, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const highlights_post = defineEventHandler(async (event) => {
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const fundId = getRouterParam(event, "id");
    if (!fundId || isNaN(Number(fundId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u57FA\u91D1ID"
      });
    }
    const body = await readBody(event);
    const { content, sortOrder } = body;
    if (!content || content.trim() === "") {
      throw createError({
        statusCode: 400,
        statusMessage: "\u4EAE\u70B9\u5185\u5BB9\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    if (!sortOrder || isNaN(Number(sortOrder))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6392\u5E8F\u53F7\u5FC5\u987B\u662F\u6709\u6548\u6570\u5B57"
      });
    }
    const fundExists = await query(
      "SELECT id FROM funds WHERE id = ?",
      [fundId]
    );
    if (fundExists.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u57FA\u91D1\u4E0D\u5B58\u5728"
      });
    }
    const result = await query(
      `INSERT INTO fund_highlights (fund_id, content, sort_order, created_at) 
       VALUES (?, ?, ?, NOW())`,
      [fundId, content.trim(), Number(sortOrder)]
    );
    await logAuditAction({
      action: "ADMIN_CREATE_FUND_HIGHLIGHT",
      description: `\u7BA1\u7406\u5458\u521B\u5EFA\u57FA\u91D1\u4EAE\u70B9: \u57FA\u91D1ID=${fundId}`,
      userId: adminPayload.id,
      username: adminPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      metadata: {
        fundId: Number(fundId),
        content: content.trim(),
        sortOrder: Number(sortOrder),
        highlightId: result.insertId
      }
    });
    logger.info("\u57FA\u91D1\u4EAE\u70B9\u521B\u5EFA\u6210\u529F", {
      adminId: adminPayload.id,
      adminUsername: adminPayload.username,
      fundId: Number(fundId),
      highlightId: result.insertId,
      content: content.trim()
    });
    return {
      success: true,
      message: "\u57FA\u91D1\u4EAE\u70B9\u521B\u5EFA\u6210\u529F",
      data: {
        id: result.insertId,
        fundId: Number(fundId),
        content: content.trim(),
        sortOrder: Number(sortOrder),
        createdAt: (/* @__PURE__ */ new Date()).toISOString()
      }
    };
  } catch (error) {
    logger.error("\u521B\u5EFA\u57FA\u91D1\u4EAE\u70B9\u5931\u8D25", {
      error: error.message,
      fundId: getRouterParam(event, "id")
    });
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "\u521B\u5EFA\u57FA\u91D1\u4EAE\u70B9\u5931\u8D25"
    });
  }
});

export { highlights_post as default };
//# sourceMappingURL=highlights.post.mjs.map
