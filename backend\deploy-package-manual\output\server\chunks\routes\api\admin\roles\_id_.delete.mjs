import { c as defineE<PERSON><PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, q as query, k as getPool, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__delete = defineEventHandler(async (event) => {
  var _a;
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(adminPayload.id, PERMISSIONS.ROLE_MANAGE);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u5220\u9664\u89D2\u8272"
      });
    }
    const roleId = getRouterParam(event, "id");
    if (!roleId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u89D2\u8272ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const existingRole = await query(`
      SELECT id, name, code FROM admin_roles WHERE id = ?
    `, [roleId]);
    if (existingRole.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u89D2\u8272\u4E0D\u5B58\u5728"
      });
    }
    const role = existingRole[0];
    const systemRoles = ["super", "admin"];
    if (systemRoles.includes(role.code)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u7CFB\u7EDF\u5185\u7F6E\u89D2\u8272\u4E0D\u5141\u8BB8\u5220\u9664"
      });
    }
    const roleUsage = await query(`
      SELECT COUNT(*) as count FROM admin_role_relations WHERE role_id = ?
    `, [roleId]);
    if (roleUsage[0].count > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u8BE5\u89D2\u8272\u6B63\u5728\u88AB\u4F7F\u7528\uFF0C\u65E0\u6CD5\u5220\u9664"
      });
    }
    const connection = await getPool().getConnection();
    await connection.beginTransaction();
    try {
      await connection.execute(`
        DELETE FROM admin_role_permissions WHERE role_id = ?
      `, [roleId]);
      await connection.execute(`
        DELETE FROM admin_roles WHERE id = ?
      `, [roleId]);
      await connection.commit();
      logger.info("\u5220\u9664\u89D2\u8272\u6210\u529F", {
        adminId: adminPayload.id,
        roleId,
        roleName: role.name,
        roleCode: role.code,
        ip: getClientIP(event)
      });
      return {
        success: true,
        message: "\u89D2\u8272\u5220\u9664\u6210\u529F"
      };
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    logger.error("\u5220\u9664\u89D2\u8272\u5931\u8D25", {
      error: error.message,
      roleId: getRouterParam(event, "id"),
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { _id__delete as default };
//# sourceMappingURL=_id_.delete.mjs.map
