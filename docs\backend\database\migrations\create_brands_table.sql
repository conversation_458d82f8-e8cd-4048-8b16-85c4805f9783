-- 设置字符集
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 创建承制厂牌表
CREATE TABLE IF NOT EXISTS brands (
  id INT AUTO_INCREMENT PRIMARY KEY,
  brand_name VARCHAR(255) NOT NULL COMMENT '厂牌名称',
  brand_logo VARCHAR(500) NOT NULL COMMENT '厂牌图标URL',
  credit_code VARCHAR(50) COMMENT '统一社会信用代码',
  company_name VARCHAR(255) NOT NULL COMMENT '公司名称',
  company_type VARCHAR(100) COMMENT '公司类型',
  registered_capital VARCHAR(100) COMMENT '注册资本',
  legal_representative VARCHAR(100) COMMENT '法定代表人',
  establishment_date DATE COMMENT '成立日期',
  business_term VARCHAR(200) COMMENT '营业期限',
  registered_address TEXT COMMENT '注册地址',
  business_scope TEXT COMMENT '经营范围',
  registration_authority VARCHAR(200) COMMENT '登记机关',
  license_issue_date DATE COMMENT '发照日期',
  business_license_photo VARCHAR(500) NOT NULL COMMENT '营业执照照片URL',
  status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  INDEX idx_brand_name (brand_name(191)),
  INDEX idx_company_name (company_name(191)),
  INDEX idx_credit_code (credit_code),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='承制厂牌表';
