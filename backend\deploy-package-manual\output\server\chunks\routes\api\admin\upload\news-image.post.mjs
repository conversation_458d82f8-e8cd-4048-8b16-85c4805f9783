import { c as defineEvent<PERSON><PERSON><PERSON>, n as readMultipartFormData, f as createError, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import { writeFile } from 'fs/promises';
import { b as getFileExtension, v as validateFileType, f as formatFileSize, g as generateUniqueFilename, a as getUploadPath, d as deleteFile } from '../../../../_/file-utils.mjs';
import { u as uploadToCOS } from '../../../../_/cos-uploader.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';
import 'fs';
import 'path';
import 'crypto';
import 'cos-nodejs-sdk-v5';

const newsImage_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const formData = await readMultipartFormData(event);
    if (!formData || formData.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u672A\u9009\u62E9\u6587\u4EF6"
      });
    }
    const fileData = formData.find((item) => item.name === "file");
    if (!fileData || !fileData.data || !fileData.filename) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6587\u4EF6\u6570\u636E\u65E0\u6548"
      });
    }
    const allowedTypes = [".jpg", ".jpeg", ".png", ".gif", ".webp"];
    const fileExt = getFileExtension(fileData.filename);
    if (!validateFileType(fileData.filename, allowedTypes)) {
      throw createError({
        statusCode: 400,
        statusMessage: `\u65B0\u95FB\u56FE\u7247\u53EA\u652F\u6301\u56FE\u7247\u683C\u5F0F: ${allowedTypes.join(", ")}`
      });
    }
    const maxSize = 5 * 1024 * 1024;
    if (fileData.data.length > maxSize) {
      throw createError({
        statusCode: 400,
        statusMessage: `\u65B0\u95FB\u56FE\u7247\u5927\u5C0F\u8D85\u8FC7\u9650\u5236\uFF0C\u6700\u5927\u5141\u8BB8 ${formatFileSize(maxSize)}`
      });
    }
    const uniqueFilename = generateUniqueFilename(fileData.filename);
    const tempPath = await getUploadPath(uniqueFilename, "temp");
    await writeFile(tempPath, fileData.data);
    try {
      const destPath = `mengtutv/news/${uniqueFilename}`;
      const uploadResult = await uploadToCOS({
        path: tempPath,
        originalname: fileData.filename,
        mimetype: fileData.type || "image/jpeg"
      }, destPath);
      await deleteFile(tempPath);
      await logAuditAction({
        action: "ADMIN_UPLOAD_NEWS_IMAGE",
        description: `\u7BA1\u7406\u5458\u4E0A\u4F20\u65B0\u95FB\u56FE\u7247: ${fileData.filename}`,
        userId: admin.id,
        username: admin.username,
        ip: getClientIP(event),
        userAgent: getHeader(event, "user-agent") || "",
        details: {
          originalName: fileData.filename,
          fileName: uniqueFilename,
          fileSize: fileData.data.length,
          url: uploadResult.url
        }
      });
      logger.info("\u7BA1\u7406\u5458\u4E0A\u4F20\u65B0\u95FB\u56FE\u7247\u6210\u529F", {
        adminId: admin.id,
        adminUsername: admin.username,
        originalName: fileData.filename,
        url: uploadResult.url
      });
      return {
        success: true,
        message: "\u65B0\u95FB\u56FE\u7247\u4E0A\u4F20\u6210\u529F",
        data: {
          url: uploadResult.url,
          filename: uniqueFilename,
          originalName: fileData.filename,
          size: fileData.data.length
        }
      };
    } catch (uploadError) {
      await deleteFile(tempPath);
      logger.error("\u65B0\u95FB\u56FE\u7247\u4E0A\u4F20\u5230COS\u5931\u8D25", {
        error: uploadError.message,
        adminId: admin.id,
        filename: fileData.filename
      });
      throw createError({
        statusCode: 500,
        statusMessage: `\u65B0\u95FB\u56FE\u7247\u4E0A\u4F20\u5931\u8D25: ${uploadError.message}`
      });
    }
  } catch (error) {
    logger.error("\u7BA1\u7406\u5458\u4E0A\u4F20\u65B0\u95FB\u56FE\u7247\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { newsImage_post as default };
//# sourceMappingURL=news-image.post.mjs.map
