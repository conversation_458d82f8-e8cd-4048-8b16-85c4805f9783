import { c as define<PERSON>vent<PERSON><PERSON><PERSON>, r as readBody, K as validationErrorResponse, L as findUserByCredentials, M as useResponseSuccess, q as query, h as logAuditAction, N as extractAuditInfo, l as logger, u as useRuntimeConfig, O as serverErrorResponse } from '../../../_/nitro.mjs';
import { randomBytes } from 'crypto';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const forgotPassword_post = defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { email } = body;
    if (!email) {
      return validationErrorResponse(event, "\u90AE\u7BB1\u4E0D\u80FD\u4E3A\u7A7A");
    }
    const user = await findUserByCredentials(email);
    if (!user) {
      return useResponseSuccess(null, "\u5982\u679C\u8BE5\u90AE\u7BB1\u5DF2\u6CE8\u518C\uFF0C\u91CD\u7F6E\u94FE\u63A5\u5DF2\u53D1\u9001\u5230\u60A8\u7684\u90AE\u7BB1");
    }
    const resetToken = randomBytes(32).toString("hex");
    const resetTokenExpiry = new Date(Date.now() + 36e5);
    await query(
      "UPDATE users SET reset_token = ?, reset_token_expiry = ? WHERE id = ?",
      [resetToken, resetTokenExpiry, user.id]
    );
    await logAuditAction({
      userId: user.id,
      username: user.username,
      userType: user.is_admin ? "admin" : "user",
      action: "PASSWORD_RESET_REQUEST",
      description: "\u8BF7\u6C42\u5BC6\u7801\u91CD\u7F6E",
      ...extractAuditInfo(event)
    });
    logger.info("\u5BC6\u7801\u91CD\u7F6E\u8BF7\u6C42", { userId: user.id, email });
    const config = useRuntimeConfig();
    const resetUrl = `${config.appUrl}/reset-password?token=${resetToken}`;
    if (false) ;
    return useResponseSuccess(null, "\u5982\u679C\u8BE5\u90AE\u7BB1\u5DF2\u6CE8\u518C\uFF0C\u91CD\u7F6E\u94FE\u63A5\u5DF2\u53D1\u9001\u5230\u60A8\u7684\u90AE\u7BB1");
  } catch (error) {
    logger.error("\u5BC6\u7801\u91CD\u7F6E\u8BF7\u6C42\u5931\u8D25", { error: (error == null ? void 0 : error.message) || error });
    await logAuditAction({
      action: "PASSWORD_RESET_ERROR",
      description: `\u5BC6\u7801\u91CD\u7F6E\u7CFB\u7EDF\u9519\u8BEF: ${(error == null ? void 0 : error.message) || error}`,
      ...extractAuditInfo(event)
    });
    return serverErrorResponse(event, "\u5BC6\u7801\u91CD\u7F6E\u8BF7\u6C42\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5");
  }
});

export { forgotPassword_post as default };
//# sourceMappingURL=forgot-password.post.mjs.map
