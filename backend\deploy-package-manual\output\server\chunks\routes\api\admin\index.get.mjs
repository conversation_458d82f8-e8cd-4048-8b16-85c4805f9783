import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, g as getQuery, q as query, l as logger, e as getClientIP } from '../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../_/permission.mjs';
import { v as validatePagination, a as validateStatus } from '../../../_/validators.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_get = defineEventHandler(async (event) => {
  var _a, _b;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u7BA1\u7406\u6F14\u5458"
      });
    }
    const queryParams = getQuery(event);
    const { page, pageSize, offset } = validatePagination(queryParams.page, queryParams.pageSize);
    const {
      status,
      search
    } = queryParams;
    let whereClause = "WHERE 1=1";
    const params = [];
    if (status !== void 0) {
      const statusValue = validateStatus(status);
      if (statusValue !== null) {
        whereClause += " AND is_active = ?";
        params.push(statusValue);
      }
    }
    if (search) {
      whereClause += " AND (name LIKE ? OR bio LIKE ? OR tags LIKE ?)";
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    const countResult = await query(
      `SELECT COUNT(*) as total FROM actors ${whereClause}`,
      params
    );
    const total = ((_a = countResult[0]) == null ? void 0 : _a.total) || 0;
    const actors = await query(
      `SELECT id, name, avatar_url, bio, tags, sort_order, is_active, created_at, updated_at
       FROM actors 
       ${whereClause}
       ORDER BY sort_order ASC, id DESC
       LIMIT ? OFFSET ?`,
      [...params, pageSize, offset]
    );
    const formattedActors = actors.map((actor) => ({
      id: actor.id,
      name: actor.name,
      avatarUrl: actor.avatar_url,
      bio: actor.bio,
      tags: actor.tags,
      sortOrder: actor.sort_order,
      isActive: actor.is_active === 1,
      createdAt: actor.created_at,
      updatedAt: actor.updated_at
    }));
    return {
      success: true,
      data: {
        list: formattedActors,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize)
        }
      }
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u6F14\u5458\u5217\u8868\u5931\u8D25", {
      error: error.message,
      adminId: (_b = event.context.admin) == null ? void 0 : _b.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { index_get as default };
//# sourceMappingURL=index.get.mjs.map
