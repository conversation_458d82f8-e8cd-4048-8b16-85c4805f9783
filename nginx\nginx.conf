events {
    # 工作进程的最大连接数
    # 为什么1024：适合小型应用的连接数
    worker_connections 1024;
}

http {
    # 包含MIME类型定义
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    # 访问日志
    access_log /var/log/nginx/access.log main;
    
    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # 官方网站 - qinghee.com.cn
    server {
        listen 80;
        server_name qinghee.com.cn www.qinghee.com.cn;
        
        location / {
            # 代理到website容器
            proxy_pass http://website:80;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
    
    # 后台管理 - admin.qinghee.com.cn
    server {
        listen 80;
        server_name admin.qinghee.com.cn;
        
        location / {
            # 代理到fundAdmin容器
            proxy_pass http://fundadmin:80;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
    
    # API服务 - api.qinghee.com.cn
    server {
        listen 80;
        server_name api.qinghee.com.cn;
        
        location / {
            # 代理到backend容器
            proxy_pass http://backend:3001;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # API特殊配置
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
    }
}
