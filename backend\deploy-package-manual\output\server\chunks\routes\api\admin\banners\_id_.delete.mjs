import { c as define<PERSON>vent<PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, f as createError, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__delete = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const bannerId = getRouterParam(event, "id");
    if (!bannerId || isNaN(Number(bannerId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u6A2A\u5E45ID"
      });
    }
    const existingBanner = await query(
      "SELECT * FROM banners WHERE id = ?",
      [bannerId]
    );
    if (existingBanner.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u6A2A\u5E45\u4E0D\u5B58\u5728"
      });
    }
    const banner = existingBanner[0];
    await query(
      "DELETE FROM banners WHERE id = ?",
      [bannerId]
    );
    await logAuditAction({
      action: "ADMIN_DELETE_BANNER",
      description: `\u7BA1\u7406\u5458\u5220\u9664\u6A2A\u5E45: ${banner.title}`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        bannerId: Number(bannerId),
        deletedData: banner
      }
    });
    logger.info("\u7BA1\u7406\u5458\u5220\u9664\u6A2A\u5E45\u6210\u529F", {
      adminId: admin.id,
      adminUsername: admin.username,
      bannerId: Number(bannerId),
      title: banner.title
    });
    return {
      success: true,
      message: "\u6A2A\u5E45\u5220\u9664\u6210\u529F",
      data: {
        id: Number(bannerId)
      }
    };
  } catch (error) {
    logger.error("\u5220\u9664\u6A2A\u5E45\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { _id__delete as default };
//# sourceMappingURL=_id_.delete.mjs.map
