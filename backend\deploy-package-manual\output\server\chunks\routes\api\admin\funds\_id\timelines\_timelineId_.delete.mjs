import { c as defineEvent<PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _timelineId__delete = defineEventHandler(async (event) => {
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const fundId = getRouterParam(event, "id");
    const timelineId = getRouterParam(event, "timelineId");
    if (!fundId || isNaN(Number(fundId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u57FA\u91D1ID"
      });
    }
    if (!timelineId || isNaN(Number(timelineId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u65F6\u95F4\u7EBFID"
      });
    }
    const existingTimeline = await query(
      "SELECT * FROM fund_timelines WHERE id = ? AND fund_id = ?",
      [timelineId, fundId]
    );
    if (existingTimeline.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u65F6\u95F4\u7EBF\u4E0D\u5B58\u5728"
      });
    }
    await query(
      "DELETE FROM fund_timelines WHERE id = ? AND fund_id = ?",
      [timelineId, fundId]
    );
    await logAuditAction({
      action: "ADMIN_DELETE_FUND_TIMELINE",
      description: `\u7BA1\u7406\u5458\u5220\u9664\u57FA\u91D1\u65F6\u95F4\u7EBF: \u57FA\u91D1ID=${fundId}, \u65F6\u95F4\u7EBFID=${timelineId}`,
      userId: adminPayload.id,
      username: adminPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      metadata: {
        fundId: Number(fundId),
        timelineId: Number(timelineId),
        deletedStage: existingTimeline[0].stage,
        deletedStartDate: existingTimeline[0].start_date,
        deletedEndDate: existingTimeline[0].end_date,
        deletedIsOpenEnded: existingTimeline[0].is_open_ended
      }
    });
    logger.info("\u57FA\u91D1\u65F6\u95F4\u7EBF\u5220\u9664\u6210\u529F", {
      adminId: adminPayload.id,
      adminUsername: adminPayload.username,
      fundId: Number(fundId),
      timelineId: Number(timelineId),
      deletedStage: existingTimeline[0].stage
    });
    return {
      success: true,
      message: "\u57FA\u91D1\u65F6\u95F4\u7EBF\u5220\u9664\u6210\u529F",
      data: {
        id: Number(timelineId),
        fundId: Number(fundId)
      }
    };
  } catch (error) {
    logger.error("\u5220\u9664\u57FA\u91D1\u65F6\u95F4\u7EBF\u5931\u8D25", {
      error: error.message,
      fundId: getRouterParam(event, "id"),
      timelineId: getRouterParam(event, "timelineId")
    });
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "\u5220\u9664\u57FA\u91D1\u65F6\u95F4\u7EBF\u5931\u8D25"
    });
  }
});

export { _timelineId__delete as default };
//# sourceMappingURL=_timelineId_.delete.mjs.map
