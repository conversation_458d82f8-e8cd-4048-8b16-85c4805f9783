-- 迁移短剧标签从名称到ID的脚本
-- 执行前请备份数据库！

-- 1. 首先确保drama_tags表存在
SOURCE create_drama_tags_table.sql;

-- 2. 创建临时存储过程来处理标签迁移
DELIMITER $$

CREATE PROCEDURE MigrateDramaTagsToIds()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE drama_id INT;
    DECLARE drama_tags_json TEXT;
    DECLARE new_tags_json TEXT;
    
    -- 声明游标
    DECLARE drama_cursor CURSOR FOR 
        SELECT id, tags FROM drama_series WHERE tags IS NOT NULL AND tags != '';
    
    -- 声明异常处理
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 开始事务
    START TRANSACTION;
    
    -- 打开游标
    OPEN drama_cursor;
    
    -- 循环处理每个短剧
    read_loop: LOOP
        FETCH drama_cursor INTO drama_id, drama_tags_json;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 处理标签转换
        SET new_tags_json = ConvertTagNamesToIds(drama_tags_json);
        
        -- 更新短剧的标签字段
        UPDATE drama_series 
        SET tags = new_tags_json 
        WHERE id = drama_id;
        
    END LOOP;
    
    -- 关闭游标
    CLOSE drama_cursor;
    
    -- 提交事务
    COMMIT;
    
END$$

-- 创建标签名称转ID的函数
CREATE FUNCTION ConvertTagNamesToIds(tags_json TEXT) 
RETURNS TEXT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE tag_name VARCHAR(50);
    DECLARE tag_id INT;
    DECLARE tag_ids TEXT DEFAULT '[';
    DECLARE tag_count INT DEFAULT 0;
    DECLARE i INT DEFAULT 1;
    
    -- 如果输入为空或null，返回空数组
    IF tags_json IS NULL OR tags_json = '' OR tags_json = '[]' THEN
        RETURN '[]';
    END IF;
    
    -- 移除JSON数组的方括号和引号，按逗号分割处理
    SET tags_json = REPLACE(REPLACE(REPLACE(tags_json, '[', ''), ']', ''), '"', '');
    
    -- 如果处理后为空，返回空数组
    IF tags_json = '' THEN
        RETURN '[]';
    END IF;
    
    -- 计算标签数量
    SET tag_count = (CHAR_LENGTH(tags_json) - CHAR_LENGTH(REPLACE(tags_json, ',', '')) + 1);
    
    -- 处理每个标签
    WHILE i <= tag_count DO
        -- 提取标签名称
        IF i = tag_count THEN
            SET tag_name = TRIM(SUBSTRING_INDEX(tags_json, ',', -1));
        ELSE
            SET tag_name = TRIM(SUBSTRING_INDEX(SUBSTRING_INDEX(tags_json, ',', i), ',', -1));
        END IF;
        
        -- 查找对应的标签ID
        SELECT id INTO tag_id FROM drama_tags WHERE name = tag_name LIMIT 1;
        
        -- 如果找到标签ID，添加到结果中
        IF tag_id IS NOT NULL THEN
            IF tag_ids != '[' THEN
                SET tag_ids = CONCAT(tag_ids, ',');
            END IF;
            SET tag_ids = CONCAT(tag_ids, tag_id);
        END IF;
        
        SET i = i + 1;
        SET tag_id = NULL;
    END WHILE;
    
    SET tag_ids = CONCAT(tag_ids, ']');
    
    RETURN tag_ids;
END$$

DELIMITER ;

-- 3. 执行迁移
CALL MigrateDramaTagsToIds();

-- 4. 清理临时存储过程和函数
DROP PROCEDURE IF EXISTS MigrateDramaTagsToIds;
DROP FUNCTION IF EXISTS ConvertTagNamesToIds;

-- 5. 验证迁移结果
SELECT 
    id, 
    title, 
    tags as new_tags_ids,
    (SELECT GROUP_CONCAT(name) FROM drama_tags WHERE FIND_IN_SET(id, REPLACE(REPLACE(tags, '[', ''), ']', ''))) as tag_names
FROM drama_series 
WHERE tags IS NOT NULL AND tags != '' AND tags != '[]'
LIMIT 10;
