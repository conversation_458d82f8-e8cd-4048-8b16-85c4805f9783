@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\ProjectV\ReelShortFund\backend\node_modules\.pnpm\fast-xml-parser@4.2.5\node_modules\fast-xml-parser\src\cli\node_modules;D:\ProjectV\ReelShortFund\backend\node_modules\.pnpm\fast-xml-parser@4.2.5\node_modules\fast-xml-parser\src\node_modules;D:\ProjectV\ReelShortFund\backend\node_modules\.pnpm\fast-xml-parser@4.2.5\node_modules\fast-xml-parser\node_modules;D:\ProjectV\ReelShortFund\backend\node_modules\.pnpm\fast-xml-parser@4.2.5\node_modules;D:\ProjectV\ReelShortFund\backend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\ProjectV\ReelShortFund\backend\node_modules\.pnpm\fast-xml-parser@4.2.5\node_modules\fast-xml-parser\src\cli\node_modules;D:\ProjectV\ReelShortFund\backend\node_modules\.pnpm\fast-xml-parser@4.2.5\node_modules\fast-xml-parser\src\node_modules;D:\ProjectV\ReelShortFund\backend\node_modules\.pnpm\fast-xml-parser@4.2.5\node_modules\fast-xml-parser\node_modules;D:\ProjectV\ReelShortFund\backend\node_modules\.pnpm\fast-xml-parser@4.2.5\node_modules;D:\ProjectV\ReelShortFund\backend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\fast-xml-parser@4.2.5\node_modules\fast-xml-parser\src\cli\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\fast-xml-parser@4.2.5\node_modules\fast-xml-parser\src\cli\cli.js" %*
)
