import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import { c as clearRefreshTokenCookie } from '../../../../_/cookie-utils.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const logout_post = defineEventHandler(async (event) => {
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    clearRefreshTokenCookie(event);
    await logAuditAction({
      action: "ADMIN_LOGOUT",
      description: "\u7BA1\u7406\u5458\u767B\u51FA",
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      userId: adminPayload.id,
      username: adminPayload.username
    });
    logger.info("\u7BA1\u7406\u5458\u767B\u51FA\u6210\u529F", {
      adminId: adminPayload.id,
      username: adminPayload.username,
      ip: getClientIP(event)
    });
    return {
      success: true,
      message: "\u767B\u51FA\u6210\u529F"
    };
  } catch (error) {
    logger.error("\u7BA1\u7406\u5458\u767B\u51FA\u5931\u8D25", {
      error: error.message,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { logout_post as default };
//# sourceMappingURL=logout.post.mjs.map
