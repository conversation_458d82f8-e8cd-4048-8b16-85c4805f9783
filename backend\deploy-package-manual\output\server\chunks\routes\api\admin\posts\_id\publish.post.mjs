import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getR<PERSON>er<PERSON>ara<PERSON>, f as createError, q as query, r as readBody } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const publish_post = defineEventHandler(async (event) => {
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const postId = getRouterParam(event, "id");
    if (!postId || isNaN(Number(postId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u63A8\u6587ID"
      });
    }
    const existingPost = await query(
      "SELECT id, title, status FROM posts WHERE id = ?",
      [postId]
    );
    if (existingPost.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u63A8\u6587\u4E0D\u5B58\u5728"
      });
    }
    const body = await readBody(event);
    const { action, publishDate } = body;
    const validActions = ["publish", "unpublish", "archive"];
    if (!validActions.includes(action)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u64CD\u4F5C\u7C7B\u578B"
      });
    }
    let newStatus;
    let isOnline;
    let newPublishDate = publishDate;
    switch (action) {
      case "publish":
        newStatus = "published";
        isOnline = 1;
        if (!newPublishDate) {
          newPublishDate = (/* @__PURE__ */ new Date()).toISOString().slice(0, 19).replace("T", " ");
        }
        break;
      case "unpublish":
        newStatus = "draft";
        isOnline = 0;
        newPublishDate = null;
        break;
      case "archive":
        newStatus = "archived";
        isOnline = 0;
        break;
    }
    const updateQuery = `
      UPDATE posts
      SET status = ?, is_online = ?, publish_date = ?, updated_at = NOW()
      WHERE id = ?
    `;
    await query(updateQuery, [newStatus, isOnline, newPublishDate, postId]);
    const actionText = {
      publish: "\u53D1\u5E03",
      unpublish: "\u4E0B\u7EBF",
      archive: "\u5F52\u6863"
    }[action];
    return {
      success: true,
      message: `\u63A8\u6587${actionText}\u6210\u529F`,
      data: {
        id: postId,
        action,
        status: newStatus,
        isOnline: Boolean(isOnline)
      }
    };
  } catch (error) {
    console.error("\u64CD\u4F5C\u63A8\u6587\u5931\u8D25:", error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.message || "\u64CD\u4F5C\u63A8\u6587\u5931\u8D25"
    });
  }
});

export { publish_post as default };
//# sourceMappingURL=publish.post.mjs.map
