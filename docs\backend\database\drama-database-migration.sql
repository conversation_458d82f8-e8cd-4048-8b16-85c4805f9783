-- 短剧募资信息数据库改造SQL
-- 根据字段梳理文档进行数据库结构调整
-- 执行前请备份数据库！

-- ============================================================================
-- 1. 短剧基础信息表 (drama_series) 改造
-- ============================================================================

-- 首先备份现有数据（如果备份表不存在）
DROP TABLE IF EXISTS drama_series_backup;
CREATE TABLE drama_series_backup AS SELECT * FROM drama_series;

-- 删除不需要的字段（先检查字段是否存在）
-- 删除 cover_svg 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND COLUMN_NAME = 'cover_svg') > 0,
    'ALTER TABLE drama_series DROP COLUMN cover_svg',
    'SELECT "cover_svg column does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除 cover_color 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND COLUMN_NAME = 'cover_color') > 0,
    'ALTER TABLE drama_series DROP COLUMN cover_color',
    'SELECT "cover_color column does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除 long_description 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND COLUMN_NAME = 'long_description') > 0,
    'ALTER TABLE drama_series DROP COLUMN long_description',
    'SELECT "long_description column does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除 single_episode_cost 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND COLUMN_NAME = 'single_episode_cost') > 0,
    'ALTER TABLE drama_series DROP COLUMN single_episode_cost',
    'SELECT "single_episode_cost column does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除 remaining_days 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND COLUMN_NAME = 'remaining_days') > 0,
    'ALTER TABLE drama_series DROP COLUMN remaining_days',
    'SELECT "remaining_days column does not exist" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
ALTER TABLE drama_series ADD COLUMN funding_end_date DATETIME COMMENT '募资结束时间';

-- 修改 tags 字段注释，说明需要从标签配置中选择
ALTER TABLE drama_series MODIFY COLUMN tags TEXT COMMENT '短剧标签，多个标签用逗号分隔，需要从标签配置中选择';

-- 修改 target_platform 字段注释，说明需要从平台配置中选择
ALTER TABLE drama_series MODIFY COLUMN target_platform VARCHAR(255) COMMENT '目标播放平台，需要从平台配置中选择';

-- 新增制作团队相关字段
ALTER TABLE drama_series ADD COLUMN production_company VARCHAR(255) COMMENT '出品公司';
ALTER TABLE drama_series ADD COLUMN co_production_company VARCHAR(255) COMMENT '联合出品公司';
ALTER TABLE drama_series ADD COLUMN executive_producer VARCHAR(100) COMMENT '出品人';
ALTER TABLE drama_series ADD COLUMN co_executive_producer VARCHAR(100) COMMENT '联合出品人';
ALTER TABLE drama_series ADD COLUMN chief_producer VARCHAR(100) COMMENT '总制片人';
ALTER TABLE drama_series ADD COLUMN co_producer VARCHAR(100) COMMENT '联合制片人';
ALTER TABLE drama_series ADD COLUMN supervisor VARCHAR(100) COMMENT '监制';
ALTER TABLE drama_series ADD COLUMN coordinator VARCHAR(100) COMMENT '统筹';

-- 修改制作进度字段，支持起止时间
ALTER TABLE drama_series MODIFY COLUMN schedule_pre_production VARCHAR(500) COMMENT '前期制作筹备时间安排，包含起止时间';
ALTER TABLE drama_series MODIFY COLUMN schedule_filming VARCHAR(500) COMMENT '拍摄时间安排，包含起止时间';
ALTER TABLE drama_series MODIFY COLUMN schedule_post_production VARCHAR(500) COMMENT '后期制作时间安排，包含起止时间';
ALTER TABLE drama_series MODIFY COLUMN expected_release_date VARCHAR(500) COMMENT '预期上线日期，包含起止时间';

-- 新增募资相关字段
ALTER TABLE drama_series ADD COLUMN funding_share DECIMAL(5,2) COMMENT '募资份额百分比';

-- 确保所有必要字段存在
-- 添加 funding_goal 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND COLUMN_NAME = 'funding_goal') = 0,
    'ALTER TABLE drama_series ADD COLUMN funding_goal DECIMAL(15,2) COMMENT "募资目标金额（元）"',
    'SELECT "funding_goal column already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 current_funding 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND COLUMN_NAME = 'current_funding') = 0,
    'ALTER TABLE drama_series ADD COLUMN current_funding DECIMAL(15,2) DEFAULT 0 COMMENT "当前已募资金额（元）"',
    'SELECT "current_funding column already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 min_investment 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND COLUMN_NAME = 'min_investment') = 0,
    'ALTER TABLE drama_series ADD COLUMN min_investment DECIMAL(15,2) COMMENT "最小投资金额（元）"',
    'SELECT "min_investment column already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 expected_return 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND COLUMN_NAME = 'expected_return') = 0,
    'ALTER TABLE drama_series ADD COLUMN expected_return DECIMAL(5,2) COMMENT "预期收益率"',
    'SELECT "expected_return column already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 roi 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND COLUMN_NAME = 'roi') = 0,
    'ALTER TABLE drama_series ADD COLUMN roi DECIMAL(5,2) COMMENT "投资回报率"',
    'SELECT "roi column already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 确保状态字段正确
ALTER TABLE drama_series MODIFY COLUMN status ENUM('draft', 'published', 'ended', 'archived') DEFAULT 'draft' COMMENT '项目状态：draft(草稿)、published(已发布)、ended(已结束)、archived(已归档)';

-- 确保其他字段存在
-- 添加 risk_management 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND COLUMN_NAME = 'risk_management') = 0,
    'ALTER TABLE drama_series ADD COLUMN risk_management TEXT COMMENT "风险管理措施（绑定到风险提示，查看企业信息）"',
    'SELECT "risk_management column already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 confirmed_resources 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND COLUMN_NAME = 'confirmed_resources') = 0,
    'ALTER TABLE drama_series ADD COLUMN confirmed_resources TEXT COMMENT "已确认资源"',
    'SELECT "confirmed_resources column already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 investment_tiers 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND COLUMN_NAME = 'investment_tiers') = 0,
    'ALTER TABLE drama_series ADD COLUMN investment_tiers JSON COMMENT "投资档位信息"',
    'SELECT "investment_tiers column already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 is_online 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND COLUMN_NAME = 'is_online') = 0,
    'ALTER TABLE drama_series ADD COLUMN is_online TINYINT(1) DEFAULT 1 COMMENT "是否上线显示：1(上线)、0(下线)"',
    'SELECT "is_online column already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 creator_id 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND COLUMN_NAME = 'creator_id') = 0,
    'ALTER TABLE drama_series ADD COLUMN creator_id BIGINT UNSIGNED COMMENT "创建者用户ID"',
    'SELECT "creator_id column already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 created_at 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND COLUMN_NAME = 'created_at') = 0,
    'ALTER TABLE drama_series ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间"',
    'SELECT "created_at column already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加 updated_at 字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND COLUMN_NAME = 'updated_at') = 0,
    'ALTER TABLE drama_series ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "更新时间"',
    'SELECT "updated_at column already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引优化查询性能
-- 创建 status 索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND INDEX_NAME = 'idx_drama_series_status') = 0,
    'CREATE INDEX idx_drama_series_status ON drama_series(status)',
    'SELECT "idx_drama_series_status index already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 创建 is_online 索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND INDEX_NAME = 'idx_drama_series_is_online') = 0,
    'CREATE INDEX idx_drama_series_is_online ON drama_series(is_online)',
    'SELECT "idx_drama_series_is_online index already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 创建 creator_id 索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND INDEX_NAME = 'idx_drama_series_creator_id') = 0,
    'CREATE INDEX idx_drama_series_creator_id ON drama_series(creator_id)',
    'SELECT "idx_drama_series_creator_id index already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 创建 funding_end_date 索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME = 'drama_series' AND INDEX_NAME = 'idx_drama_series_funding_end_date') = 0,
    'CREATE INDEX idx_drama_series_funding_end_date ON drama_series(funding_end_date)',
    'SELECT "idx_drama_series_funding_end_date index already exists" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ============================================================================
-- 2. 投资权益表 (drama_investment_tiers) - 新建表
-- ============================================================================

CREATE TABLE IF NOT EXISTS drama_investment_tiers (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '投资档位ID',
    drama_id INT(11) NOT NULL COMMENT '关联的短剧ID',
    tier_name VARCHAR(100) NOT NULL COMMENT '档位名称',
    min_amount DECIMAL(15,2) NOT NULL COMMENT '最小投资金额',
    max_amount DECIMAL(15,2) COMMENT '最大投资金额',
    benefits TEXT COMMENT '投资权益描述',
    return_rate DECIMAL(5,2) COMMENT '预期收益率',
    limited_quantity INT COMMENT '限量份数，NULL表示不限量',
    sold_quantity INT DEFAULT 0 COMMENT '已售份数',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_drama_investment_tiers_drama_id (drama_id),
    INDEX idx_drama_investment_tiers_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短剧投资档位表';

-- ============================================================================
-- 3. 数据迁移和清理
-- ============================================================================

-- 更新现有数据的状态字段（如果有不符合新枚举的值）
UPDATE drama_series SET status = 'draft' WHERE status NOT IN ('draft', 'published', 'ended', 'archived');

-- 设置默认的募资结束时间（当前时间+30天，可根据实际需求调整）
UPDATE drama_series 
SET funding_end_date = DATE_ADD(NOW(), INTERVAL 30 DAY) 
WHERE funding_end_date IS NULL AND status IN ('draft', 'published');

-- ============================================================================
-- 4. 验证改造结果
-- ============================================================================

-- 查看表结构
DESCRIBE drama_series;
DESCRIBE drama_investment_tiers;

-- 查看数据统计
SELECT 
    COUNT(*) as total_dramas,
    COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_count,
    COUNT(CASE WHEN status = 'published' THEN 1 END) as published_count,
    COUNT(CASE WHEN status = 'ended' THEN 1 END) as ended_count,
    COUNT(CASE WHEN status = 'archived' THEN 1 END) as archived_count
FROM drama_series;

-- 检查是否有空的必要字段
SELECT id, title, 
    CASE WHEN funding_goal IS NULL THEN 'funding_goal为空' END as missing_funding_goal,
    CASE WHEN funding_end_date IS NULL THEN 'funding_end_date为空' END as missing_funding_end_date
FROM drama_series 
WHERE funding_goal IS NULL OR funding_end_date IS NULL;

COMMIT;
