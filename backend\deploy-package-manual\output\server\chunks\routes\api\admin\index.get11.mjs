import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, g as getQuery, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../_/nitro.mjs';
import { v as validatePagination, a as validateStatus } from '../../../_/validators.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_get = defineEventHandler(async (event) => {
  var _a, _b;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const query$1 = getQuery(event);
    const { page, pageSize, offset } = validatePagination(query$1.page, query$1.pageSize);
    const search = query$1.search || "";
    const status = validateStatus(query$1.status);
    const userType = query$1.userType || "";
    const startTime = query$1.startTime || "";
    const endTime = query$1.endTime || "";
    let whereClause = "WHERE 1=1";
    const params = [];
    if (search) {
      whereClause += " AND (username LIKE ? OR email LIKE ? OR phone LIKE ? OR real_name LIKE ? OR company_name LIKE ?)";
      params.push(`%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`);
    }
    if (status !== null) {
      whereClause += " AND status = ?";
      params.push(status);
    }
    if (userType && ["investor", "producer", "fund_manager"].includes(userType)) {
      whereClause += " AND user_type = ?";
      params.push(userType);
    }
    if (startTime) {
      whereClause += " AND DATE(created_at) >= ?";
      params.push(startTime);
    }
    if (endTime) {
      whereClause += " AND DATE(created_at) <= ?";
      params.push(endTime);
    }
    const countResult = await query(
      `SELECT COUNT(*) as total FROM users ${whereClause}`,
      params
    );
    const total = ((_a = countResult[0]) == null ? void 0 : _a.total) || 0;
    const users = await query(
      `SELECT id, username, email, phone, user_type, real_name, company_name,
              id_card, business_license, status, created_at, updated_at, last_login_at
       FROM users
       ${whereClause}
       ORDER BY created_at DESC
       LIMIT ? OFFSET ?`,
      [...params, pageSize, offset]
    );
    const formattedUsers = users.map((user) => ({
      id: user.id,
      username: user.username,
      email: user.email,
      phone: user.phone || null,
      avatar: user.avatar,
      userType: user.user_type,
      realName: user.real_name,
      companyName: user.company_name,
      idCard: user.id_card,
      businessLicense: user.business_license,
      status: user.status,
      registeredAt: user.created_at,
      updatedAt: user.updated_at,
      lastLoginAt: user.last_login_at
    }));
    await logAuditAction({
      action: "ADMIN_VIEW_USERS",
      description: "\u7BA1\u7406\u5458\u67E5\u770B\u7528\u6237\u5217\u8868",
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: { page, pageSize, search, status, total }
    });
    return {
      success: true,
      data: {
        list: formattedUsers,
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u7528\u6237\u5217\u8868\u5931\u8D25", {
      error: error.message,
      adminId: (_b = event.context.admin) == null ? void 0 : _b.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { index_get as default };
//# sourceMappingURL=index.get11.mjs.map
