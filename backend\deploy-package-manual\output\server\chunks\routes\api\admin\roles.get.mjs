import { c as defineEvent<PERSON>and<PERSON>, v as verifyAdminAccessToken, f as createError, g as getQuery, q as query, l as logger, e as getClientIP } from '../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const roles_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(adminPayload.id, PERMISSIONS.ROLE_MANAGE);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u67E5\u770B\u89D2\u8272\u5217\u8868"
      });
    }
    const query_params = getQuery(event);
    const {
      page = 1,
      pageSize = 10,
      name,
      code,
      status,
      startTime,
      endTime
    } = query_params;
    let whereConditions = [];
    let queryParams = [];
    if (name) {
      whereConditions.push("name LIKE ?");
      queryParams.push(`%${name}%`);
    }
    if (code) {
      whereConditions.push("code LIKE ?");
      queryParams.push(`%${code}%`);
    }
    if (status !== void 0 && status !== "") {
      whereConditions.push("status = ?");
      queryParams.push(status);
    }
    if (startTime) {
      whereConditions.push("created_at >= ?");
      queryParams.push(startTime);
    }
    if (endTime) {
      whereConditions.push("created_at <= ?");
      queryParams.push(endTime);
    }
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(" AND ")}` : "";
    const countResult = await query(`
      SELECT COUNT(*) as total
      FROM admin_roles
      ${whereClause}
    `, queryParams);
    const total = countResult[0].total;
    const offset = (parseInt(page) - 1) * parseInt(pageSize);
    const roles = await query(`
      SELECT
        id,
        name,
        code,
        description,
        status,
        created_at,
        updated_at
      FROM admin_roles
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, parseInt(pageSize), offset]);
    for (const role of roles) {
      const permissions = await query(`
        SELECT permission_code
        FROM admin_role_permissions
        WHERE role_id = ?
      `, [role.id]);
      role.permissions = permissions.map((p) => p.permission_code);
    }
    logger.info("\u83B7\u53D6\u89D2\u8272\u5217\u8868\u6210\u529F", {
      adminId: adminPayload.id,
      roleCount: roles.length,
      total,
      page,
      pageSize,
      ip: getClientIP(event)
    });
    return {
      success: true,
      data: {
        list: roles,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize)
      }
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u89D2\u8272\u5217\u8868\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { roles_get as default };
//# sourceMappingURL=roles.get.mjs.map
