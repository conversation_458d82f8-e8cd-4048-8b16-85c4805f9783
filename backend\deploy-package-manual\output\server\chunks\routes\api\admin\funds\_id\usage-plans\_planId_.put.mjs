import { c as defineEvent<PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, q as query, r as readBody, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _planId__put = defineEventHandler(async (event) => {
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const fundId = getRouterParam(event, "id");
    const planId = getRouterParam(event, "planId");
    if (!fundId || isNaN(Number(fundId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u57FA\u91D1ID"
      });
    }
    if (!planId || isNaN(Number(planId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u8BA1\u5212ID"
      });
    }
    const existingPlan = await query(
      "SELECT * FROM fund_usage_plans WHERE id = ? AND fund_id = ?",
      [planId, fundId]
    );
    if (existingPlan.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u4F7F\u7528\u8BA1\u5212\u4E0D\u5B58\u5728"
      });
    }
    const body = await readBody(event);
    const { purpose, amount, percentage, sortOrder } = body;
    if (!purpose || !purpose.trim()) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u7528\u9014\u4E3A\u5FC5\u586B\u9879"
      });
    }
    if (!percentage || isNaN(Number(percentage))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6BD4\u4F8B\u4E3A\u5FC5\u586B\u9879\u4E14\u5FC5\u987B\u4E3A\u6570\u5B57"
      });
    }
    await query(
      "UPDATE fund_usage_plans SET purpose = ?, amount = ?, percentage = ?, description = ?, sort_order = ? WHERE id = ? AND fund_id = ?",
      [purpose.trim(), amount || null, percentage ? Number(percentage) : null, purpose.trim(), Number(sortOrder) || 1, planId, fundId]
    );
    await logAuditAction({
      action: "ADMIN_UPDATE_FUND_USAGE_PLAN",
      description: `\u7BA1\u7406\u5458\u66F4\u65B0\u57FA\u91D1\u8D44\u91D1\u4F7F\u7528\u8BA1\u5212: \u57FA\u91D1ID=${fundId}, \u8BA1\u5212ID=${planId}`,
      userId: adminPayload.id,
      username: adminPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      metadata: {
        fundId: Number(fundId),
        planId: Number(planId),
        oldPurpose: existingPlan[0].purpose,
        newPurpose: purpose.trim(),
        oldAmount: existingPlan[0].amount,
        newAmount: amount || null,
        oldPercentage: existingPlan[0].percentage,
        newPercentage: percentage || null,
        sortOrder: Number(sortOrder) || 1
      }
    });
    logger.info("\u57FA\u91D1\u8D44\u91D1\u4F7F\u7528\u8BA1\u5212\u66F4\u65B0\u6210\u529F", {
      adminId: adminPayload.id,
      adminUsername: adminPayload.username,
      fundId: Number(fundId),
      planId: Number(planId),
      purpose: purpose.trim()
    });
    return {
      success: true,
      message: "\u57FA\u91D1\u8D44\u91D1\u4F7F\u7528\u8BA1\u5212\u66F4\u65B0\u6210\u529F",
      data: {
        id: Number(planId),
        fundId: Number(fundId),
        purpose: purpose.trim(),
        amount: amount || null,
        percentage: percentage || null,
        sortOrder: Number(sortOrder) || 1,
        updatedAt: (/* @__PURE__ */ new Date()).toISOString()
      }
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u57FA\u91D1\u8D44\u91D1\u4F7F\u7528\u8BA1\u5212\u5931\u8D25", {
      error: error.message,
      fundId: getRouterParam(event, "id"),
      planId: getRouterParam(event, "planId")
    });
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "\u66F4\u65B0\u57FA\u91D1\u8D44\u91D1\u4F7F\u7528\u8BA1\u5212\u5931\u8D25"
    });
  }
});

export { _planId__put as default };
//# sourceMappingURL=_planId_.put.mjs.map
