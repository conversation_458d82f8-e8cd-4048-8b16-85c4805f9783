# Compress Commons

Compress Commons is a library that defines a common interface for working with archive formats within node.

[![NPM](https://nodei.co/npm/compress-commons.png)](https://nodei.co/npm/compress-commons/)

## Install

```bash
npm install compress-commons --save
```

You can also use `npm install https://github.com/archiverjs/node-compress-commons/archive/master.tar.gz` to test upcoming versions.

## Things of Interest

- [Changelog](https://github.com/archiverjs/node-compress-commons/releases)
- [Contributing](https://github.com/archiverjs/node-compress-commons/blob/master/CONTRIBUTING.md)
- [MIT License](https://github.com/archiverjs/node-compress-commons/blob/master/LICENSE-MIT)

## Credits

Concept inspired by [Apache Commons Compress](http://commons.apache.org/proper/commons-compress/)&trade;.

Some logic derived from [Apache Commons Compress](http://commons.apache.org/proper/commons-compress/)&trade; and [OpenJDK 7](http://openjdk.java.net/).