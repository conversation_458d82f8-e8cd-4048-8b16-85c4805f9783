import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, j as getRouter<PERSON>aram, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import { c as validateId } from '../../../../_/validators.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__delete = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.USER_DELETE);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u5220\u9664\u7528\u6237"
      });
    }
    const userId = validateId(getRouterParam(event, "id"));
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u7528\u6237ID"
      });
    }
    const users = await query(
      "SELECT id, username, email, user_type FROM users WHERE id = ?",
      [userId]
    );
    if (users.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u7528\u6237\u4E0D\u5B58\u5728"
      });
    }
    const user = users[0];
    await query("DELETE FROM users WHERE id = ?", [userId]);
    await logAuditAction({
      action: "ADMIN_DELETE_USER",
      description: `\u7BA1\u7406\u5458\u5220\u9664\u7528\u6237: ${user.username} (${user.user_type})`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        deletedUserId: userId,
        deletedUsername: user.username,
        deletedUserType: user.user_type,
        deletedEmail: user.email
      }
    });
    logger.info("\u7BA1\u7406\u5458\u5220\u9664\u7528\u6237\u6210\u529F", {
      adminId: admin.id,
      deletedUserId: userId,
      deletedUsername: user.username,
      deletedUserType: user.user_type,
      ip: getClientIP(event)
    });
    return {
      success: true,
      message: "\u7528\u6237\u5220\u9664\u6210\u529F"
    };
  } catch (error) {
    logger.error("\u5220\u9664\u7528\u6237\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      userId: getRouterParam(event, "id"),
      ip: getClientIP(event)
    });
    throw error;
  }
});

export { _id__delete as default };
//# sourceMappingURL=_id_.delete.mjs.map
