import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, q as query, f as createError } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const selector_get = defineEventHandler(async (event) => {
  try {
    const actors = await query(`
      SELECT id, name, avatar_url
      FROM actors
      WHERE is_active = 1
      ORDER BY name ASC
    `);
    const formattedActors = actors.map((actor) => ({
      id: actor.id,
      label: actor.name,
      value: actor.id,
      name: actor.name,
      avatarUrl: actor.avatar_url
    }));
    return {
      success: true,
      message: "\u83B7\u53D6\u6F14\u5458\u9009\u62E9\u5668\u6570\u636E\u6210\u529F",
      data: {
        result: formattedActors
      }
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u6F14\u5458\u9009\u62E9\u5668\u6570\u636E\u5931\u8D25:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "\u83B7\u53D6\u6F14\u5458\u9009\u62E9\u5668\u6570\u636E\u5931\u8D25"
    });
  }
});

export { selector_get as default };
//# sourceMappingURL=selector.get.mjs.map
