# 剧投投后端服务 - 生产环境配置

# 服务器配置
PORT=3001
NODE_ENV=production

# 数据库配置 - 生产环境
DB_HOST=localhost
DB_PORT=3306
DB_USER=mengtu
DB_PASSWORD=cimu2025...
DB_NAME=mengtu

# JWT配置 - 生产环境强密钥
JWT_SECRET=mengtutv_jwt_secret_production_key_2024_secure_baota
JWT_EXPIRES_IN=24h
JWT_ADMIN_EXPIRES_IN=12h
ACCESS_TOKEN_SECRET=mengtutv_access_token_secret_production_2024_baota
REFRESH_TOKEN_SECRET=mengtutv_refresh_token_secret_production_2024_baota

# CORS配置 - 生产域名
CORS_ALLOWED_ORIGINS=https://qinghee.com.cn,https://www.qinghee.com.cn,https://admin.qinghee.com.cn,https://api.qinghee.com.cn

# 文件上传配置 - 修改为正确的服务器路径
UPLOAD_DIR=/www/wwwroot/api.qinghee.com.cn/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf,video/mp4

# 日志配置 - 修改为正确的服务器路径
LOG_LEVEL=warn
LOG_DIR=/www/wwwroot/api.qinghee.com.cn/logs

# 应用配置 - 生产域名
APP_URL=https://www.qinghee.com.cn
API_URL=https://api.qinghee.com.cn/api

# 云存储配置
OSS_PROVIDER=cos
COS_SECRET_ID=your_cos_secret_id
COS_SECRET_KEY=your_cos_secret_key
COS_REGION=ap-shanghai
COS_BUCKET=mengtu-production
COS_DIRECTORY=mengtutv



# # 数据库配置 - 本地开发环境
# DB_HOST=*********
# DB_PORT=3306
# DB_USER=root
# DB_PASSWORD=123456
# DB_NAME=mengtu