# 剧投投后端服务 - 生产环境配置

# 服务器配置
PORT=3001
NODE_ENV=production

# 数据库配置 - 生产环境
DB_HOST=localhost
DB_PORT=3306
DB_USER=reelshort_user
DB_PASSWORD=your_production_password_here
DB_NAME=reelshortfund

# JWT配置 - 生产环境（请修改为强密钥）
JWT_SECRET=reelshort_production_jwt_secret_key_2024_very_long_and_secure
JWT_EXPIRES_IN=24h
JWT_ADMIN_EXPIRES_IN=12h
ACCESS_TOKEN_SECRET=reelshort_access_token_secret_production_2024
REFRESH_TOKEN_SECRET=reelshort_refresh_token_secret_production_2024

# CORS配置 - 生产域名
CORS_ALLOWED_ORIGINS=https://qinghee.com.cn,https://www.qinghee.com.cn,https://admin.qinghee.com.cn,https://api.qinghee.com.cn

# 文件上传配置
UPLOAD_DIR=/www/wwwroot/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,video/mp4

# 日志配置
LOG_LEVEL=error
LOG_DIR=/www/wwwroot/logs

# 邮件配置（请填写真实的SMTP信息）
SMTP_HOST=smtp.qinghee.com.cn
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_smtp_password_here
SMTP_FROM=<EMAIL>

# 应用配置 - 生产域名
APP_URL=https://qinghee.com.cn
API_URL=https://api.qinghee.com.cn

# 云存储配置
OSS_PROVIDER=local

# 腾讯云COS配置（如果使用）
COS_SECRET_ID=your_cos_secret_id
COS_SECRET_KEY=your_cos_secret_key
COS_REGION=ap-guangzhou
COS_BUCKET=qinghee-bucket
COS_DIRECTORY=reelshort

# 阿里云OSS配置（备用）
ALI_OSS_REGION=
ALI_OSS_ACCESS_KEY_ID=
ALI_OSS_ACCESS_KEY_SECRET=
ALI_OSS_BUCKET=
