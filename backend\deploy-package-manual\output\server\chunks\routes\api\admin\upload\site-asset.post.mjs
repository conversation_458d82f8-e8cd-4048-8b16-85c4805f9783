import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, n as readMultipartFormData, l as logger, h as logAuditAction, i as getHeader, e as getClientIP } from '../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import { i as isObjectStorageAvailable, u as uploadToObjectStorage } from '../../../../_/cos-http-uploader.mjs';
import { b as getFileExtension, v as validateFileType, g as generateUniqueFilename, a as getUploadPath, d as deleteFile } from '../../../../_/file-utils.mjs';
import { writeFile } from 'fs/promises';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';
import 'path';
import 'fs';
import 'crypto';

const siteAsset_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u4E0A\u4F20\u7F51\u7AD9\u8D44\u6E90"
      });
    }
    const isStorageAvailable = await isObjectStorageAvailable();
    if (!isStorageAvailable) {
      throw createError({
        statusCode: 503,
        statusMessage: "\u5BF9\u8C61\u5B58\u50A8\u670D\u52A1\u4E0D\u53EF\u7528\uFF0C\u8BF7\u68C0\u67E5\u7CFB\u7EDF\u8BBE\u7F6E"
      });
    }
    const formData = await readMultipartFormData(event);
    if (!formData || formData.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u672A\u9009\u62E9\u6587\u4EF6"
      });
    }
    const fileData = formData.find((item) => item.name === "file");
    if (!fileData || !fileData.data || !fileData.filename) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6587\u4EF6\u6570\u636E\u65E0\u6548"
      });
    }
    const assetTypeData = formData.find((item) => item.name === "assetType");
    const assetType = (assetTypeData == null ? void 0 : assetTypeData.data) ? new TextDecoder().decode(assetTypeData.data) : "logo";
    const validAssetTypes = ["logo", "favicon", "icon", "qrcode"];
    if (!validAssetTypes.includes(assetType)) {
      throw createError({
        statusCode: 400,
        statusMessage: `\u4E0D\u652F\u6301\u7684\u8D44\u6E90\u7C7B\u578B: ${assetType}\u3002\u652F\u6301\u7684\u7C7B\u578B: ${validAssetTypes.join(", ")}`
      });
    }
    let allowedTypes;
    switch (assetType) {
      case "favicon":
        allowedTypes = [".ico", ".png", ".svg"];
        break;
      case "icon":
        allowedTypes = [".png", ".svg", ".jpg", ".jpeg"];
        break;
      case "qrcode":
        allowedTypes = [".png", ".jpg", ".jpeg"];
        break;
      case "logo":
      default:
        allowedTypes = [".png", ".svg", ".jpg", ".jpeg", ".gif"];
        break;
    }
    const fileExt = getFileExtension(fileData.filename);
    if (!validateFileType(fileData.filename, allowedTypes)) {
      throw createError({
        statusCode: 400,
        statusMessage: `${assetType}\u4E0D\u652F\u6301\u7684\u6587\u4EF6\u7C7B\u578B: ${fileExt}\u3002\u652F\u6301\u7684\u7C7B\u578B: ${allowedTypes.join(", ")}`
      });
    }
    const maxSize = 2 * 1024 * 1024;
    if (fileData.data.length > maxSize) {
      throw createError({
        statusCode: 400,
        statusMessage: `\u6587\u4EF6\u5927\u5C0F\u8D85\u8FC7\u9650\u5236\uFF0C\u6700\u5927\u5141\u8BB8 ${Math.round(maxSize / 1024 / 1024)}MB`
      });
    }
    const uniqueFilename = generateUniqueFilename(fileData.filename);
    const tempPath = await getUploadPath(uniqueFilename, "temp");
    await writeFile(tempPath, fileData.data);
    logger.info("\u5F00\u59CB\u4E0A\u4F20\u7F51\u7AD9\u8D44\u6E90", {
      assetType,
      originalName: fileData.filename,
      fileName: uniqueFilename,
      fileSize: fileData.data.length,
      fileType: fileExt,
      adminId: admin.id
    });
    try {
      const destPath = `site-assets/${assetType}/${uniqueFilename}`;
      logger.info("\u7F51\u7AD9\u8D44\u6E90\u4E0A\u4F20\u8DEF\u5F84\u4FE1\u606F", {
        assetType,
        uniqueFilename,
        destPath,
        originalName: fileData.filename
      });
      const uploadResult = await uploadToObjectStorage({
        path: tempPath,
        originalname: fileData.filename,
        mimetype: fileData.type || "image/png"
      }, destPath);
      await deleteFile(tempPath);
      await logAuditAction({
        action: "ADMIN_UPLOAD_SITE_ASSET",
        description: `\u7BA1\u7406\u5458\u4E0A\u4F20\u7F51\u7AD9${assetType}: ${fileData.filename}`,
        userId: admin.id,
        username: admin.username,
        ip: getClientIP(event),
        userAgent: getHeader(event, "user-agent") || "",
        details: {
          assetType,
          originalName: fileData.filename,
          fileName: uniqueFilename,
          fileSize: fileData.data.length,
          fileType: fileExt,
          url: uploadResult.url,
          provider: uploadResult.provider
        }
      });
      logger.info("\u7F51\u7AD9\u8D44\u6E90\u4E0A\u4F20\u6210\u529F", {
        assetType,
        originalName: fileData.filename,
        url: uploadResult.url,
        provider: uploadResult.provider,
        adminId: admin.id
      });
      return {
        success: true,
        data: {
          url: uploadResult.url,
          filename: uniqueFilename,
          originalname: fileData.filename,
          mimetype: fileData.type || "image/png",
          size: fileData.data.length,
          assetType,
          provider: uploadResult.provider,
          key: uploadResult.key
        }
      };
    } catch (uploadError) {
      await deleteFile(tempPath);
      logger.error("\u7F51\u7AD9\u8D44\u6E90\u4E0A\u4F20\u5931\u8D25", {
        error: uploadError.message,
        assetType,
        originalName: fileData.filename,
        adminId: admin.id
      });
      throw createError({
        statusCode: 500,
        statusMessage: `\u7F51\u7AD9\u8D44\u6E90\u4E0A\u4F20\u5931\u8D25: ${uploadError.message}`
      });
    }
  } catch (error) {
    logger.error("\u7F51\u7AD9\u8D44\u6E90\u4E0A\u4F20\u63A5\u53E3\u9519\u8BEF", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u7F51\u7AD9\u8D44\u6E90\u4E0A\u4F20\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5"
    });
  }
});

export { siteAsset_post as default };
//# sourceMappingURL=site-asset.post.mjs.map
