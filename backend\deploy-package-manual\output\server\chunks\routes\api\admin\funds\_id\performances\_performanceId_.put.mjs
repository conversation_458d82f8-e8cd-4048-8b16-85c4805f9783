import { c as defineEvent<PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, q as query, r as readBody, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _performanceId__put = defineEventHandler(async (event) => {
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const fundId = getRouterParam(event, "id");
    const performanceId = getRouterParam(event, "performanceId");
    if (!fundId || isNaN(Number(fundId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u57FA\u91D1ID"
      });
    }
    if (!performanceId || isNaN(Number(performanceId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u4E1A\u7EE9ID"
      });
    }
    const existingPerformance = await query(
      "SELECT * FROM fund_performances WHERE id = ? AND fund_id = ?",
      [performanceId, fundId]
    );
    if (existingPerformance.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u4E1A\u7EE9\u8BB0\u5F55\u4E0D\u5B58\u5728"
      });
    }
    const body = await readBody(event);
    const { year, return: returnValue, average } = body;
    if (!year || !returnValue) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5E74\u4EFD\u548C\u6536\u76CA\u7387\u4E3A\u5FC5\u586B\u9879"
      });
    }
    if (isNaN(Number(year)) || Number(year) < 1900 || Number(year) > 2100) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5E74\u4EFD\u683C\u5F0F\u65E0\u6548"
      });
    }
    await query(
      "UPDATE fund_performances SET year = ?, `return` = ?, average = ? WHERE id = ? AND fund_id = ?",
      [year, returnValue, average || null, performanceId, fundId]
    );
    await logAuditAction({
      action: "ADMIN_UPDATE_FUND_PERFORMANCE",
      description: `\u7BA1\u7406\u5458\u66F4\u65B0\u57FA\u91D1\u5386\u53F2\u4E1A\u7EE9: \u57FA\u91D1ID=${fundId}, \u4E1A\u7EE9ID=${performanceId}`,
      userId: adminPayload.id,
      username: adminPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      metadata: {
        fundId: Number(fundId),
        performanceId: Number(performanceId),
        oldYear: existingPerformance[0].year,
        newYear: year,
        oldReturn: existingPerformance[0].return,
        newReturn: returnValue,
        oldAverage: existingPerformance[0].average,
        newAverage: average || null
      }
    });
    logger.info("\u57FA\u91D1\u5386\u53F2\u4E1A\u7EE9\u66F4\u65B0\u6210\u529F", {
      adminId: adminPayload.id,
      adminUsername: adminPayload.username,
      fundId: Number(fundId),
      performanceId: Number(performanceId),
      year
    });
    return {
      success: true,
      message: "\u57FA\u91D1\u5386\u53F2\u4E1A\u7EE9\u66F4\u65B0\u6210\u529F",
      data: {
        id: Number(performanceId),
        fundId: Number(fundId),
        year,
        return: returnValue,
        average: average || null,
        updatedAt: (/* @__PURE__ */ new Date()).toISOString()
      }
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u57FA\u91D1\u5386\u53F2\u4E1A\u7EE9\u5931\u8D25", {
      error: error.message,
      fundId: getRouterParam(event, "id"),
      performanceId: getRouterParam(event, "performanceId")
    });
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "\u66F4\u65B0\u57FA\u91D1\u5386\u53F2\u4E1A\u7EE9\u5931\u8D25"
    });
  }
});

export { _performanceId__put as default };
//# sourceMappingURL=_performanceId_.put.mjs.map
