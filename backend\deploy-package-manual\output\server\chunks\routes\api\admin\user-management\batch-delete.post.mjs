import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, r as readBody, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const batchDelete_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.USER_DELETE);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u5220\u9664\u7528\u6237"
      });
    }
    const body = await readBody(event);
    const { ids } = body;
    if (!Array.isArray(ids) || ids.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u8BF7\u63D0\u4F9B\u8981\u5220\u9664\u7684\u7528\u6237ID\u5217\u8868"
      });
    }
    const validIds = ids.filter((id) => Number.isInteger(id) && id > 0);
    if (validIds.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u7528\u6237ID\u5217\u8868"
      });
    }
    const placeholders = validIds.map(() => "?").join(",");
    const users = await query(
      `SELECT id, username, email, user_type FROM users WHERE id IN (${placeholders})`,
      validIds
    );
    if (users.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u672A\u627E\u5230\u8981\u5220\u9664\u7684\u7528\u6237"
      });
    }
    await query(
      `DELETE FROM users WHERE id IN (${placeholders})`,
      validIds
    );
    await logAuditAction({
      action: "ADMIN_BATCH_DELETE_USERS",
      description: `\u7BA1\u7406\u5458\u6279\u91CF\u5220\u9664\u7528\u6237: ${users.map((u) => u.username).join(", ")}`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        deletedUserIds: validIds,
        deletedUsers: users.map((u) => ({
          id: u.id,
          username: u.username,
          userType: u.user_type
        }))
      }
    });
    logger.info("\u7BA1\u7406\u5458\u6279\u91CF\u5220\u9664\u7528\u6237\u6210\u529F", {
      adminId: admin.id,
      deletedUserIds: validIds,
      deletedCount: users.length,
      ip: getClientIP(event)
    });
    return {
      success: true,
      message: `\u6210\u529F\u5220\u9664 ${users.length} \u4E2A\u7528\u6237`,
      data: {
        deletedCount: users.length,
        deletedUsers: users.map((u) => u.username)
      }
    };
  } catch (error) {
    logger.error("\u6279\u91CF\u5220\u9664\u7528\u6237\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    throw error;
  }
});

export { batchDelete_post as default };
//# sourceMappingURL=batch-delete.post.mjs.map
