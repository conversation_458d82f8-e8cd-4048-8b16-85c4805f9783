import { c as defineEvent<PERSON><PERSON><PERSON>, f as createError, q as query, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const stats_get = defineEventHandler(async (event) => {
  var _a, _b, _c, _d, _e;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u67E5\u770B\u5BA1\u8BA1\u65E5\u5FD7\u7EDF\u8BA1"
      });
    }
    const [
      totalStats,
      todayStats,
      weekStats,
      monthStats,
      actionStats,
      userTypeStats,
      recentFailedLogins
    ] = await Promise.all([
      // 总记录数
      query("SELECT COUNT(*) as total FROM audit_log"),
      // 今日记录数
      query(`SELECT COUNT(*) as today FROM audit_log 
             WHERE DATE(created_at) = CURDATE()`),
      // 本周记录数
      query(`SELECT COUNT(*) as week FROM audit_log 
             WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)`),
      // 本月记录数
      query(`SELECT COUNT(*) as month FROM audit_log 
             WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)`),
      // 按操作类型统计
      query(`SELECT action, COUNT(*) as count FROM audit_log 
             WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
             GROUP BY action 
             ORDER BY count DESC 
             LIMIT 10`),
      // 按用户类型统计
      query(`SELECT user_type, COUNT(*) as count FROM audit_log 
             WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
             GROUP BY user_type`),
      // 最近的失败登录尝试
      query(`SELECT username, ip, created_at, action FROM audit_log 
             WHERE action IN ('USER_LOGIN_FAILED', 'ADMIN_LOGIN_FAILED')
             AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
             ORDER BY created_at DESC 
             LIMIT 20`)
    ]);
    const dailyActivity = await query(`
      SELECT DATE(created_at) as date, COUNT(*) as count 
      FROM audit_log 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
      GROUP BY DATE(created_at) 
      ORDER BY date ASC
    `);
    const activeUsers = await query(`
      SELECT username, user_type, COUNT(*) as activity_count 
      FROM audit_log 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
      AND username IS NOT NULL
      GROUP BY username, user_type 
      ORDER BY activity_count DESC 
      LIMIT 10
    `);
    return {
      success: true,
      data: {
        overview: {
          total: ((_a = totalStats[0]) == null ? void 0 : _a.total) || 0,
          today: ((_b = todayStats[0]) == null ? void 0 : _b.today) || 0,
          week: ((_c = weekStats[0]) == null ? void 0 : _c.week) || 0,
          month: ((_d = monthStats[0]) == null ? void 0 : _d.month) || 0
        },
        actionStats: actionStats.map((item) => ({
          action: item.action,
          count: item.count
        })),
        userTypeStats: userTypeStats.map((item) => ({
          userType: item.user_type,
          count: item.count
        })),
        dailyActivity: dailyActivity.map((item) => ({
          date: item.date,
          count: item.count
        })),
        activeUsers: activeUsers.map((item) => ({
          username: item.username,
          userType: item.user_type,
          activityCount: item.activity_count
        })),
        recentFailedLogins: recentFailedLogins.map((item) => ({
          username: item.username,
          ip: item.ip,
          createdAt: item.created_at,
          action: item.action
        }))
      }
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u5BA1\u8BA1\u65E5\u5FD7\u7EDF\u8BA1\u5931\u8D25", {
      error: error.message,
      adminId: (_e = event.context.admin) == null ? void 0 : _e.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { stats_get as default };
//# sourceMappingURL=stats.get.mjs.map
