# 文件上传API说明

## 概述

backend/api/admin/upload/ 目录包含了多个文件上传API，支持将文件上传到配置的对象存储服务中。

## API列表

### 1. 通用文件上传 - `/api/admin/upload/common`

**功能**: 通用的文件上传接口，支持多种文件类型
**方法**: POST
**权限**: BASIC_ACCESS

**请求参数**:
- `file`: 要上传的文件（必需）
- `destPath`: 目标路径（可选，如果不提供则自动生成）
- `allowedTypes`: 允许的文件类型，逗号分隔（可选，默认支持常见文件类型）

**支持的文件类型**（默认）:
- 图片: .jpg, .jpeg, .png, .gif, .bmp, .webp
- 文档: .pdf, .doc, .docx, .xls, .xlsx, .ppt, .pptx
- 文本: .txt, .csv, .json, .xml
- 视频: .mp4, .avi, .mov, .wmv, .flv, .mkv
- 音频: .mp3, .wav, .flac, .aac
- 压缩包: .zip, .rar, .7z, .tar, .gz

**文件大小限制**: 50MB

**响应格式**:
```json
{
  "success": true,
  "data": {
    "url": "https://example.com/path/to/file.jpg",
    "filename": "unique_filename.jpg",
    "originalname": "original.jpg",
    "mimetype": "image/jpeg",
    "size": 12345,
    "provider": "tencent",
    "key": "uploads/unique_filename.jpg"
  }
}
```

### 2. 网站资源上传 - `/api/admin/upload/site-asset`

**功能**: 专门用于上传网站资源（Logo、图标等）
**方法**: POST
**权限**: BASIC_ACCESS

**请求参数**:
- `file`: 要上传的文件（必需）
- `assetType`: 资源类型（可选，默认为'logo'）
  - `logo`: 网站Logo
  - `favicon`: 网站图标
  - `icon`: 其他图标

**文件类型限制**:
- Logo: .png, .svg, .jpg, .jpeg, .gif
- Favicon: .ico, .png, .svg
- Icon: .png, .svg, .jpg, .jpeg

**文件大小限制**: 2MB

**目标路径**: `site-assets/{assetType}/filename`

### 3. 其他专用上传API

- `/api/admin/upload/banner-image`: 横幅图片上传
- `/api/admin/upload/actor-avatar`: 演员头像上传
- `/api/admin/upload/drama-cover`: 短剧封面上传
- `/api/admin/upload/drama-material`: 短剧素材上传
- `/api/admin/upload/file`: 通用文件上传（旧版）

## 对象存储配置

所有上传API都依赖于系统设置中的对象存储配置。需要在管理后台的"系统设置 > 对象存储设置"中配置：

- **服务商**: 目前支持腾讯云COS
- **Secret ID**: 访问密钥ID
- **Secret Key**: 访问密钥
- **存储桶**: 存储桶名称
- **地域**: 存储桶所在地域
- **自定义域名**: 可选，用于访问文件的自定义域名
- **目录**: 文件存储的根目录
- **启用状态**: 必须启用才能使用上传功能

## 使用示例

### JavaScript/前端调用示例

```javascript
// 通用文件上传
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('destPath', 'custom/path/filename.jpg'); // 可选
formData.append('allowedTypes', '.jpg,.png,.gif'); // 可选

const response = await fetch('/api/admin/upload/common', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token
  },
  body: formData
});

const result = await response.json();
if (result.success) {
  console.log('上传成功:', result.data.url);
}

// 网站资源上传
const logoFormData = new FormData();
logoFormData.append('file', logoFile);
logoFormData.append('assetType', 'logo');

const logoResponse = await fetch('/api/admin/upload/site-asset', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token
  },
  body: logoFormData
});
```

## 错误处理

所有API都返回标准的错误格式：

```json
{
  "success": false,
  "statusCode": 400,
  "statusMessage": "错误描述"
}
```

常见错误：
- 401: 未授权访问
- 403: 权限不足
- 400: 文件类型不支持、文件大小超限等
- 503: 对象存储服务不可用
- 500: 服务器内部错误

## 安全性

- 所有上传API都需要管理员身份验证
- 文件类型验证防止恶意文件上传
- 文件大小限制防止资源滥用
- 生成唯一文件名防止文件名冲突
- 审计日志记录所有上传操作

## 技术实现

- 使用Nitro框架开发
- 支持multipart/form-data文件上传
- 临时文件处理和清理
- 对象存储配置从数据库动态读取
- 完整的错误处理和日志记录
