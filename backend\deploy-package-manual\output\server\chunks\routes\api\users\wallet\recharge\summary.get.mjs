import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, Z as verifyUserAccessToken, f as createError, q as query } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const summary_get = defineEventHandler(async (event) => {
  try {
    console.log("Recharge summary API called");
    const userPayload = verifyUserAccessToken(event);
    if (!userPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u7528\u6237\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"
      });
    }
    const userId = userPayload.id;
    const summaryQuery = `
      SELECT 
        COUNT(*) as total_count,
        COALESCE(SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END), 0) as total_amount,
        COALESCE(SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END), 0) as completed_count,
        COALESCE(SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END), 0) as pending_count,
        COALESCE(SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END), 0) as failed_count,
        MAX(CASE WHEN status = 'completed' THEN created_at ELSE NULL END) as last_recharge_date
      FROM user_asset_transactions
      WHERE user_id = ? 
        AND transaction_type = 'shells_in' 
        AND related_type = 'recharge'
    `;
    const summaryResult = await query(summaryQuery, [userId]);
    const summary = summaryResult[0];
    const recentQuery = `
      SELECT 
        amount,
        status,
        DATE_FORMAT(created_at, '%Y-%m-%d %H:%i') as created_at
      FROM user_asset_transactions
      WHERE user_id = ? 
        AND transaction_type = 'shells_in' 
        AND related_type = 'recharge'
      ORDER BY created_at DESC
      LIMIT 3
    `;
    const recentRecords = await query(recentQuery, [userId]);
    return {
      success: true,
      data: {
        summary: {
          totalCount: parseInt(summary.total_count) || 0,
          totalAmount: parseFloat(summary.total_amount) || 0,
          completedCount: parseInt(summary.completed_count) || 0,
          pendingCount: parseInt(summary.pending_count) || 0,
          failedCount: parseInt(summary.failed_count) || 0,
          lastRechargeDate: summary.last_recharge_date
        },
        recentRecords: recentRecords.map((record) => ({
          amount: parseFloat(record.amount),
          status: record.status,
          createdAt: record.created_at
        }))
      }
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u5145\u503C\u7EDF\u8BA1\u4FE1\u606F\u5931\u8D25:", error);
    return {
      success: false,
      message: "\u83B7\u53D6\u5145\u503C\u7EDF\u8BA1\u4FE1\u606F\u5931\u8D25",
      error: error.message
    };
  }
});

export { summary_get as default };
//# sourceMappingURL=summary.get.mjs.map
