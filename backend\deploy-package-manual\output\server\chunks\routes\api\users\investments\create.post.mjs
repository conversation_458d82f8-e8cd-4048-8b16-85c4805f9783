import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, Z as verifyUserAccessToken, f as createError, r as readBody, q as query } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const create_post = defineEventHandler(async (event) => {
  try {
    const userPayload = verifyUserAccessToken(event);
    if (!userPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u7528\u6237\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"
      });
    }
    const userId = userPayload.id;
    const body = await readBody(event);
    const { projectId, amount } = body;
    if (!projectId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u9879\u76EEID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    if (!amount || amount <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6295\u8D44\u91D1\u989D\u5FC5\u987B\u5927\u4E8E0"
      });
    }
    let assetRows = await query(`
      SELECT
        shells_balance,
        total_invested_shells
      FROM user_assets
      WHERE user_id = ?
    `, [userId]);
    if (!assetRows || assetRows.length === 0) {
      console.log(`\u7528\u6237 ${userId} \u8D44\u4EA7\u8BB0\u5F55\u4E0D\u5B58\u5728\uFF0C\u521B\u5EFA\u521D\u59CB\u8BB0\u5F55`);
      await query(`
        INSERT INTO user_assets (
          user_id, shells_balance, diamonds_balance,
          total_invested_shells, total_earned_diamonds,
          frozen_shells, frozen_diamonds, created_at, updated_at
        ) VALUES (?, 0, 0, 0, 0, 0, 0, NOW(), NOW())
      `, [userId]);
      assetRows = await query(`
        SELECT
          shells_balance,
          total_invested_shells
        FROM user_assets
        WHERE user_id = ?
      `, [userId]);
    }
    const currentAsset = assetRows[0];
    const currentBalance = parseFloat(currentAsset.shells_balance);
    if (currentBalance < amount) {
      return {
        success: false,
        message: "\u4F59\u989D\u4E0D\u8DB3",
        data: {
          currentBalance,
          requiredAmount: amount,
          shortfall: amount - currentBalance
        }
      };
    }
    const projectQuery = `
      SELECT
        ds.id, ds.title,
        dfi.min_investment, dfi.funding_goal,
        dfi.current_funding, dfi.status, dfi.expected_return
      FROM drama_series ds
      LEFT JOIN drama_funding_info dfi ON ds.id = dfi.drama_id
      WHERE ds.id = ? AND dfi.status = 'published'
    `;
    const projectRows = await query(projectQuery, [projectId]);
    if (!projectRows || projectRows.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u9879\u76EE\u4E0D\u5B58\u5728\u6216\u5DF2\u5173\u95ED"
      });
    }
    const project = projectRows[0];
    let minInvestment = 5e4;
    let maxInvestment = null;
    let fundingGoal = 0;
    let currentFunding = 0;
    try {
      minInvestment = project.min_investment ? parseFloat(project.min_investment) : 5e4;
      maxInvestment = project.max_investment ? parseFloat(project.max_investment) : null;
      fundingGoal = project.funding_goal ? parseFloat(project.funding_goal) : 0;
      currentFunding = project.current_funding ? parseFloat(project.current_funding) : 0;
      if (minInvestment > 5e5 || minInvestment <= 0) {
        console.log(`\u5F02\u5E38\u7684\u6700\u5C0F\u6295\u8D44\u989D ${minInvestment}\uFF0C\u91CD\u7F6E\u4E3A50000`);
        minInvestment = 5e4;
      }
      if (currentFunding >= fundingGoal && fundingGoal > 0) {
        return {
          success: false,
          message: "\u9879\u76EE\u5DF2\u5B8C\u6210\u52DF\u8D44\u76EE\u6807\uFF0C\u6682\u505C\u63A5\u53D7\u65B0\u6295\u8D44",
          data: {
            fundingGoal,
            currentFunding,
            fundingProgress: Math.round(currentFunding / fundingGoal * 100)
          }
        };
      }
    } catch (parseError) {
      console.error("\u6570\u636E\u89E3\u6790\u9519\u8BEF:", parseError);
      minInvestment = 5e4;
    }
    let expectedReturnRate = 15;
    if (project.expected_return) {
      const returnStr = String(project.expected_return);
      const match = returnStr.match(/(\d+(?:\.\d+)?)/);
      if (match) {
        expectedReturnRate = parseFloat(match[1]);
      }
    }
    console.log("\u9879\u76EE\u6570\u636E:", {
      id: project.id,
      title: project.title,
      min_investment: minInvestment,
      max_investment: maxInvestment,
      funding_goal: fundingGoal,
      current_funding: currentFunding,
      expected_return: project.expected_return,
      expected_return_rate: expectedReturnRate
    });
    if (amount < minInvestment) {
      return {
        success: false,
        message: `\u6295\u8D44\u91D1\u989D\u4E0D\u80FD\u5C11\u4E8E ${minInvestment} \u8D1D\u58F3`,
        data: {
          minInvestment
        }
      };
    }
    if (maxInvestment && amount > maxInvestment) {
      return {
        success: false,
        message: `\u6295\u8D44\u91D1\u989D\u4E0D\u80FD\u8D85\u8FC7 ${maxInvestment} \u8D1D\u58F3`,
        data: {
          maxInvestment
        }
      };
    }
    const remainingAmount = fundingGoal - currentFunding;
    if (amount > remainingAmount) {
      return {
        success: false,
        message: `\u9879\u76EE\u5269\u4F59\u52DF\u8D44\u989D\u5EA6\u4E0D\u8DB3\uFF0C\u4EC5\u5269 ${remainingAmount} \u8D1D\u58F3`,
        data: {
          remainingAmount
        }
      };
    }
    const investmentNo = `INV${Date.now()}${Math.random().toString(36).substr(2, 6).toUpperCase()}`;
    const newBalance = currentBalance - amount;
    try {
      console.log("\u5F00\u59CB\u6295\u8D44\u4E8B\u52A1\u5904\u7406...");
      console.log("\u66F4\u65B0\u7528\u6237\u8D44\u4EA7...");
      await query(`
        UPDATE user_assets
        SET
          shells_balance = ?,
          total_invested_shells = total_invested_shells + ?,
          updated_at = NOW()
        WHERE user_id = ?
      `, [newBalance, amount, userId]);
      console.log("\u521B\u5EFA\u6295\u8D44\u8BB0\u5F55...");
      const investmentResult = await query(`
        INSERT INTO user_investments (
          user_id, project_id, project_name, investment_amount,
          expected_return_rate, expected_return_amount,
          investment_date, start_date, end_date,
          project_status, investment_status, progress,
          created_at
        ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 12 MONTH), ?, ?, ?, NOW())
      `, [
        userId,
        projectId,
        project.title,
        amount,
        expectedReturnRate,
        Math.round(amount * expectedReturnRate / 100),
        "active",
        "active",
        0
      ]);
      console.log("\u6295\u8D44\u8BB0\u5F55\u521B\u5EFA\u6210\u529F\uFF0CID:", investmentResult.insertId);
      console.log("\u63D2\u5165\u4EA4\u6613\u8BB0\u5F55...");
      await query(`
        INSERT INTO user_asset_transactions (
          user_id, transaction_type, amount, balance_before, balance_after,
          related_type, related_id, description, transaction_no, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `, [
        userId,
        "shells_out",
        amount,
        currentBalance,
        newBalance,
        "investment",
        investmentResult.insertId,
        `\u6295\u8D44\u9879\u76EE\uFF1A${project.title}`,
        investmentNo,
        "completed"
      ]);
      console.log("\u66F4\u65B0\u9879\u76EE\u52DF\u8D44\u91D1\u989D...");
      await query(`
        UPDATE drama_funding_info
        SET
          current_funding = current_funding + ?
        WHERE drama_id = ?
      `, [amount, projectId]);
      console.log("\u6295\u8D44\u64CD\u4F5C\u5B8C\u6210\u6210\u529F");
      return {
        success: true,
        data: {
          investmentId: investmentResult.insertId,
          investmentNo,
          projectName: project.title,
          amount,
          newBalance,
          expectedReturn: Math.round(amount * expectedReturnRate / 100),
          message: "\u6295\u8D44\u6210\u529F"
        }
      };
    } catch (error) {
      console.error("\u6295\u8D44\u64CD\u4F5C\u5931\u8D25:", error);
      throw error;
    }
  } catch (error) {
    console.error("\u6295\u8D44\u5931\u8D25:", error);
    return {
      success: false,
      message: "\u6295\u8D44\u5931\u8D25",
      error: error.message
    };
  }
});

export { create_post as default };
//# sourceMappingURL=create.post.mjs.map
