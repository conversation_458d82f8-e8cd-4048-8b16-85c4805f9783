import axios from 'axios'
import { getApiBaseUrl } from '../utils/environmentConfig'
import type { ApiResponse } from '../types'

// API基础URL
const BASE_URL = getApiBaseUrl()

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 协议内容接口
export interface Agreement {
  id: number
  title: string
  content: string
  identifier: string
  status: 'published' | 'draft'
  created_at: string
  updated_at: string
}

/**
 * 根据标识符获取协议内容
 * @param identifier 协议标识符 (user-agreement 或 privacy-policy)
 */
export function getAgreementByIdentifier(identifier: string) {
  return api.get<ApiResponse<Agreement>>(`/public/posts/agreement/${identifier}`)
}

/**
 * 获取用户协议
 */
export function getUserAgreement() {
  return getAgreementByIdentifier('user-agreement')
}

/**
 * 获取隐私政策
 */
export function getPrivacyPolicy() {
  return getAgreementByIdentifier('privacy-policy')
}
