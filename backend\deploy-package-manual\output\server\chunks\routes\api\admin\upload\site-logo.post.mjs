import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, n as readMultipartFormData, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import { b as getFileExtension, v as validateFileType, f as formatFileSize, g as generateUniqueFilename, a as getUploadPath, d as deleteFile } from '../../../../_/file-utils.mjs';
import { u as uploadToCOS } from '../../../../_/cos-uploader.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';
import 'fs';
import 'path';
import 'crypto';
import 'cos-nodejs-sdk-v5';
import 'fs/promises';

const siteLogo_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u4E0A\u4F20\u7F51\u7AD9Logo"
      });
    }
    const formData = await readMultipartFormData(event);
    if (!formData || formData.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u672A\u9009\u62E9\u6587\u4EF6"
      });
    }
    const fileData = formData.find((item) => item.name === "file");
    if (!fileData || !fileData.data || !fileData.filename) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6587\u4EF6\u6570\u636E\u65E0\u6548"
      });
    }
    const allowedTypes = [".jpg", ".jpeg", ".png", ".gif"];
    const fileExt = getFileExtension(fileData.filename);
    if (!validateFileType(fileData.filename, allowedTypes)) {
      throw createError({
        statusCode: 400,
        statusMessage: `Logo\u53EA\u652F\u6301\u56FE\u7247\u683C\u5F0F: ${allowedTypes.join(", ")}`
      });
    }
    const maxSize = 2 * 1024 * 1024;
    if (fileData.data.length > maxSize) {
      throw createError({
        statusCode: 400,
        statusMessage: `Logo\u6587\u4EF6\u5927\u5C0F\u8D85\u8FC7\u9650\u5236\uFF0C\u6700\u5927\u5141\u8BB8 ${formatFileSize(maxSize)}`
      });
    }
    const uniqueFilename = generateUniqueFilename(fileData.filename);
    const tempPath = await getUploadPath(uniqueFilename, "temp");
    await $fetch.raw(tempPath, {
      method: "PUT",
      body: fileData.data
    });
    try {
      const destPath = `mengtutv/logo/${uniqueFilename}`;
      const uploadResult = await uploadToCOS({
        path: tempPath,
        originalname: fileData.filename,
        mimetype: fileData.type || "image/jpeg"
      }, destPath);
      await deleteFile(tempPath);
      const existingSettings = await query(
        "SELECT id FROM system_settings WHERE setting_key = ?",
        ["site_logo"]
      );
      if (existingSettings.length > 0) {
        await query(
          "UPDATE system_settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?",
          [uploadResult.url, "site_logo"]
        );
      } else {
        await query(
          "INSERT INTO system_settings (setting_key, setting_value, created_at, updated_at) VALUES (?, ?, NOW(), NOW())",
          ["site_logo", uploadResult.url]
        );
      }
      await logAuditAction({
        action: "ADMIN_UPLOAD_SITE_LOGO",
        description: `\u7BA1\u7406\u5458\u4E0A\u4F20\u7F51\u7AD9Logo: ${fileData.filename}`,
        userId: admin.id,
        username: admin.username,
        ip: getClientIP(event),
        userAgent: getHeader(event, "user-agent") || "",
        details: {
          originalName: fileData.filename,
          fileName: uniqueFilename,
          fileSize: fileData.data.length,
          url: uploadResult.url
        }
      });
      logger.info("\u7BA1\u7406\u5458\u4E0A\u4F20\u7F51\u7AD9Logo\u6210\u529F", {
        adminId: admin.id,
        adminUsername: admin.username,
        originalName: fileData.filename,
        url: uploadResult.url
      });
      return {
        success: true,
        message: "Logo\u4E0A\u4F20\u6210\u529F",
        data: {
          url: uploadResult.url
        }
      };
    } catch (uploadError) {
      await deleteFile(tempPath);
      logger.error("Logo\u4E0A\u4F20\u5230COS\u5931\u8D25", {
        error: uploadError.message,
        adminId: admin.id,
        filename: fileData.filename
      });
      throw createError({
        statusCode: 500,
        statusMessage: `Logo\u4E0A\u4F20\u5931\u8D25: ${uploadError.message}`
      });
    }
  } catch (error) {
    logger.error("\u7BA1\u7406\u5458\u4E0A\u4F20\u7F51\u7AD9Logo\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { siteLogo_post as default };
//# sourceMappingURL=site-logo.post.mjs.map
