import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as get<PERSON><PERSON>er<PERSON><PERSON><PERSON>, f as createError, p as executeTransaction, w as queryInTransaction } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const confirmPayment_put = defineEventHandler(async (event) => {
  try {
    console.log("\u7BA1\u7406\u5458\u786E\u8BA4\u652F\u4ED8API\u88AB\u8C03\u7528");
    const recordId = getRouterParam(event, "id");
    if (!recordId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5145\u503C\u8BB0\u5F55ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    console.log(`\u786E\u8BA4\u652F\u4ED8\u5145\u503C\u8BB0\u5F55: ${recordId}`);
    const result = await executeTransaction(async (connection) => {
      const checkQuery = `
        SELECT
          id, user_id, amount, balance_before, status, description
        FROM user_asset_transactions
        WHERE id = ? AND transaction_type = 'shells_in' AND related_type = 'recharge'
      `;
      const existingRecords = await queryInTransaction(connection, checkQuery, [recordId]);
      if (existingRecords.length === 0) {
        throw createError({
          statusCode: 404,
          statusMessage: "\u5145\u503C\u8BB0\u5F55\u4E0D\u5B58\u5728"
        });
      }
      const record = existingRecords[0];
      console.log("\u5145\u503C\u8BB0\u5F55\u8BE6\u60C5:", record);
      if (record.status !== "pending") {
        throw createError({
          statusCode: 400,
          statusMessage: `\u5145\u503C\u8BB0\u5F55\u72B6\u6001\u4E3A${record.status}\uFF0C\u65E0\u6CD5\u786E\u8BA4\u652F\u4ED8`
        });
      }
      const userId = record.user_id;
      const amount = parseFloat(record.amount);
      const balanceBefore = parseFloat(record.balance_before);
      const assetQuery = `
        SELECT shells_balance, total_invested_shells
        FROM user_assets
        WHERE user_id = ?
      `;
      const assetRows = await queryInTransaction(connection, assetQuery, [userId]);
      if (assetRows.length === 0) {
        throw createError({
          statusCode: 404,
          statusMessage: "\u7528\u6237\u8D44\u4EA7\u4FE1\u606F\u4E0D\u5B58\u5728"
        });
      }
      const currentAsset = assetRows[0];
      const currentBalance = parseFloat(currentAsset.shells_balance);
      const newBalance = currentBalance + amount;
      const newTotalInvested = parseFloat(currentAsset.total_invested_shells) + amount;
      console.log(`\u7528\u6237${userId}\u4F59\u989D\u53D8\u5316: ${currentBalance} -> ${newBalance}`);
      await queryInTransaction(connection, `
        UPDATE user_assets
        SET
          shells_balance = ?,
          total_invested_shells = ?,
          updated_at = NOW()
        WHERE user_id = ?
      `, [newBalance, newTotalInvested, userId]);
      await queryInTransaction(connection, `
        UPDATE user_asset_transactions
        SET
          status = 'completed',
          balance_after = ?,
          updated_at = NOW()
        WHERE id = ?
      `, [newBalance, recordId]);
      console.log(`\u5145\u503C\u8BB0\u5F55 ${recordId} \u786E\u8BA4\u652F\u4ED8\u6210\u529F`);
      return {
        recordId,
        userId,
        amount,
        newBalance
      };
    });
    return {
      success: true,
      message: "\u652F\u4ED8\u786E\u8BA4\u6210\u529F",
      data: result
    };
  } catch (error) {
    console.error("\u786E\u8BA4\u652F\u4ED8\u5931\u8D25:", error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "\u786E\u8BA4\u652F\u4ED8\u5931\u8D25"
    });
  }
});

export { confirmPayment_put as default };
//# sourceMappingURL=confirm-payment.put.mjs.map
