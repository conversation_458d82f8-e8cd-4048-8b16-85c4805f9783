import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, Z as verifyUserAccessToken, f as createError, r as readBody, U as findUserById, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../_/nitro.mjs';
import { e as validateUsername, f as validateEmail, g as validatePhone } from '../../../_/validators.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const profile_put = defineEventHandler(async (event) => {
  var _a;
  try {
    const userPayload = verifyUserAccessToken(event);
    if (!userPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u7528\u6237\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"
      });
    }
    const body = await readBody(event);
    const { username, email, phone, avatar } = body;
    const user = await findUserById(userPayload.id);
    if (!user) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u7528\u6237\u4E0D\u5B58\u5728"
      });
    }
    if (user.status !== 1) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u7528\u6237\u8D26\u6237\u5DF2\u88AB\u7981\u7528"
      });
    }
    if (username) {
      const usernameValidation = validateUsername(username);
      if (!usernameValidation.valid) {
        throw createError({
          statusCode: 400,
          statusMessage: usernameValidation.message
        });
      }
    }
    if (email && !validateEmail(email)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u90AE\u7BB1\u683C\u5F0F\u4E0D\u6B63\u786E"
      });
    }
    if (phone && !validatePhone(phone)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u624B\u673A\u53F7\u683C\u5F0F\u4E0D\u6B63\u786E"
      });
    }
    if (username && username !== user.username) {
      const existingUser = await query(
        "SELECT id FROM users WHERE username = ? AND id != ?",
        [username, userPayload.id]
      );
      if (existingUser.length > 0) {
        throw createError({
          statusCode: 409,
          statusMessage: "\u7528\u6237\u540D\u5DF2\u88AB\u5176\u4ED6\u7528\u6237\u4F7F\u7528"
        });
      }
    }
    if (email && email !== user.email) {
      const existingEmail = await query(
        "SELECT id FROM users WHERE email = ? AND id != ?",
        [email, userPayload.id]
      );
      if (existingEmail.length > 0) {
        throw createError({
          statusCode: 409,
          statusMessage: "\u90AE\u7BB1\u5DF2\u88AB\u5176\u4ED6\u7528\u6237\u4F7F\u7528"
        });
      }
    }
    if (phone && phone !== user.phone) {
      const existingPhone = await query(
        "SELECT id FROM users WHERE phone = ? AND id != ?",
        [phone, userPayload.id]
      );
      if (existingPhone.length > 0) {
        throw createError({
          statusCode: 409,
          statusMessage: "\u624B\u673A\u53F7\u5DF2\u88AB\u5176\u4ED6\u7528\u6237\u4F7F\u7528"
        });
      }
    }
    const updateFields = [];
    const updateParams = [];
    if (username !== void 0) {
      updateFields.push("username = ?");
      updateParams.push(username);
    }
    if (email !== void 0) {
      updateFields.push("email = ?");
      updateParams.push(email);
    }
    if (phone !== void 0) {
      updateFields.push("phone = ?");
      updateParams.push(phone || null);
    }
    if (avatar !== void 0) {
      updateFields.push("avatar = ?");
      updateParams.push(avatar || null);
    }
    if (updateFields.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6CA1\u6709\u63D0\u4F9B\u8981\u66F4\u65B0\u7684\u5B57\u6BB5"
      });
    }
    updateFields.push("updated_at = NOW()");
    updateParams.push(userPayload.id);
    await query(
      `UPDATE users SET ${updateFields.join(", ")} WHERE id = ?`,
      updateParams
    );
    await logAuditAction({
      action: "USER_UPDATE_PROFILE",
      description: "\u7528\u6237\u66F4\u65B0\u4E2A\u4EBA\u4FE1\u606F",
      userId: userPayload.id,
      username: userPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        updatedFields: { username, email, phone, avatar }
      }
    });
    logger.info("\u7528\u6237\u66F4\u65B0\u4E2A\u4EBA\u4FE1\u606F\u6210\u529F", {
      userId: userPayload.id,
      username: userPayload.username,
      updatedFields: { username, email, phone, avatar }
    });
    return {
      success: true,
      message: "\u4E2A\u4EBA\u4FE1\u606F\u66F4\u65B0\u6210\u529F"
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u7528\u6237\u4E2A\u4EBA\u4FE1\u606F\u5931\u8D25", {
      error: error.message,
      userId: (_a = event.context.user) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { profile_put as default };
//# sourceMappingURL=profile.put.mjs.map
