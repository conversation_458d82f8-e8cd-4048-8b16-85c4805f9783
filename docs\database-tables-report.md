# 数据库表统计报告

## 📊 总体统计

- **数据库名称**: mengtu
- **导出时间**: 2025-08-05 16:45:34
- **数据表总数**: **46张表**
- **字符集**: utf8mb4_unicode_ci

## 📋 完整表清单（按字母排序）

### 🎭 演员相关 (1张表)
1. **actors** - 演员信息表

### 👨‍💼 管理员系统 (7张表)
2. **admin_menus** - 管理员菜单表
3. **admin_permissions** - 管理员权限表
4. **admin_role_permissions** - 角色权限关联表
5. **admin_role_relations** - 角色关系表
6. **admin_roles** - 管理员角色表
7. **admins** - 管理员表
8. **audit_log** - 审计日志表
9. **audit_logs** - 审计日志表（可能重复）

### 🎪 轮播图和品牌 (2张表)
10. **banners** - 轮播图表
11. **brands** - 品牌表

### 🎬 短剧相关 (11张表)
12. **drama_additional_info** - 短剧附加信息表
13. **drama_documents** - 短剧文档表
14. **drama_funding_info** - 短剧募资信息表
15. **drama_investment_tiers** - 短剧投资层级表
16. **drama_materials** - 短剧素材表
17. **drama_platforms** - 短剧平台关联表
18. **drama_production_schedule** - 短剧制作进度表
19. **drama_production_team** - 短剧制作团队表
20. **drama_series** - 短剧系列表
21. **drama_tags** - 短剧标签关联表

### 💰 基金相关 (10张表)
22. **fund_documents** - 基金文档表
23. **fund_faqs** - 基金常见问题表
24. **fund_fees** - 基金费用表
25. **fund_highlights** - 基金亮点表
26. **fund_performances** - 基金业绩表
27. **fund_success_cases** - 基金成功案例表
28. **fund_timelines** - 基金时间线表
29. **fund_usage_plan_descriptions** - 基金使用计划描述表
30. **fund_usage_plans** - 基金使用计划表
31. **funds** - 基金主表

### 📰 新闻相关 (6张表)
32. **news** - 新闻主表
33. **news_categories** - 新闻分类表
34. **news_read_logs** - 新闻阅读日志表
35. **news_seo** - 新闻SEO表
36. **news_tag_relations** - 新闻标签关联表
37. **news_tags** - 新闻标签表

### 📝 帖子和系统 (3张表)
38. **posts** - 帖子表
39. **system_settings** - 系统设置表
40. **token_blacklist** - 令牌黑名单表

### 👤 用户相关 (6张表)
41. **user_asset_transactions** - 用户资产交易表
42. **user_assets** - 用户资产表
43. **user_investments** - 用户投资表
44. **user_notifications** - 用户通知表
45. **user_returns** - 用户收益表
46. **users** - 用户主表

## 🔍 表分类统计

| 分类 | 表数量 | 占比 |
|------|--------|------|
| 用户相关 | 6张 | 13.0% |
| 短剧相关 | 11张 | 23.9% |
| 基金相关 | 10张 | 21.7% |
| 管理员系统 | 7张 | 15.2% |
| 新闻相关 | 6张 | 13.0% |
| 演员相关 | 1张 | 2.2% |
| 其他系统 | 5张 | 10.9% |
| **总计** | **46张** | **100%** |

## ⚠️ 注意事项

1. **可能的重复表**:
   - `audit_log` 和 `audit_logs` 可能是重复的审计日志表

2. **核心业务表**:
   - `users` - 用户主表
   - `funds` - 基金主表
   - `drama_series` - 短剧主表
   - `actors` - 演员主表

3. **关联表较多**:
   - 短剧相关有11张表，说明业务逻辑较复杂
   - 基金相关有10张表，功能较为完善

## 📝 对比建议

请检查您当前数据库中的表是否与此列表一致：

```sql
-- 查看当前数据库所有表
SHOW TABLES;

-- 统计表数量
SELECT COUNT(*) as table_count FROM information_schema.tables 
WHERE table_schema = 'mengtu';
```

如果发现差异，请告知具体的缺失或多余的表名。
