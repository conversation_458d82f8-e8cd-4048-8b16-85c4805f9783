import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, f as createError, q as query, o as logAdminAction, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__get = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const newsId = getRouterParam(event, "id");
    if (!newsId || isNaN(parseInt(newsId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u65B0\u95FBID"
      });
    }
    const newsIdNum = parseInt(newsId);
    const newsQuery = `
      SELECT 
        n.id,
        n.title,
        n.summary,
        n.content,
        n.cover_image_url,
        n.author,
        n.source_url,
        n.status,
        n.view_count,
        n.is_featured,
        n.publish_date,
        n.created_at,
        n.updated_at,
        nc.id as category_id,
        nc.name as category_name,
        nc.slug as category_slug,
        nc.description as category_description
      FROM news n
      LEFT JOIN news_categories nc ON n.category_id = nc.id
      WHERE n.id = ?
    `;
    const newsResult = await query(newsQuery, [newsIdNum]);
    if (!newsResult || newsResult.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u65B0\u95FB\u4E0D\u5B58\u5728"
      });
    }
    const news = newsResult[0];
    const tagsQuery = `
      SELECT nt.id, nt.name
      FROM news_tags nt
      INNER JOIN news_tag_relations ntr ON nt.id = ntr.tag_id
      WHERE ntr.news_id = ?
      ORDER BY nt.name
    `;
    const tags = await query(tagsQuery, [newsIdNum]);
    const formattedNews = {
      id: news.id,
      title: news.title,
      summary: news.summary,
      content: news.content,
      coverImage: news.cover_image_url,
      author: news.author,
      sourceUrl: news.source_url,
      status: news.status,
      viewCount: news.view_count,
      isFeatured: Boolean(news.is_featured),
      publishDate: news.publish_date,
      createdAt: news.created_at,
      updatedAt: news.updated_at,
      category: news.category_id ? {
        id: news.category_id,
        name: news.category_name,
        slug: news.category_slug,
        description: news.category_description
      } : null,
      tags: tags.map((tag) => ({
        id: tag.id,
        name: tag.name
      }))
    };
    await logAdminAction(admin.id, "news:view", "\u67E5\u770B\u65B0\u95FB\u8BE6\u60C5", {
      newsId: newsIdNum,
      title: news.title
    });
    return {
      success: true,
      data: formattedNews
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u65B0\u95FB\u8BE6\u60C5\u5931\u8D25", {
      error: error.message,
      stack: error.stack,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u83B7\u53D6\u65B0\u95FB\u8BE6\u60C5\u5931\u8D25"
    });
  }
});

export { _id__get as default };
//# sourceMappingURL=_id_.get.mjs.map
