/**
 * 测试新用户充值功能
 * 验证新用户注册后能否正常进行充值操作
 */

import { query } from '../utils/database'
import { hashPassword } from '../utils/auth'

async function testNewUserRecharge() {
  console.log('开始测试新用户充值功能...\n')

  try {
    // 1. 创建测试用户
    const testUsername = `test_user_${Date.now()}`
    const testEmail = `${testUsername}@test.com`
    const testPassword = 'test123456'
    
    console.log(`=== 创建测试用户 ===`)
    console.log(`用户名: ${testUsername}`)
    console.log(`邮箱: ${testEmail}`)

    // 加密密码
    const hashedPassword = await hashPassword(testPassword)

    // 插入用户
    const userResult = await query(
      `INSERT INTO users (username, email, password_hash, user_type, status, created_at, updated_at)
       VALUES (?, ?, ?, 'investor', 1, NOW(), NOW())`,
      [testUsername, testEmail, hashedPassword]
    )

    const userId = (userResult as any).insertId
    console.log(`✅ 用户创建成功，ID: ${userId}`)

    // 2. 检查是否自动创建了资产记录
    console.log(`\n=== 检查用户资产记录 ===`)
    const assetRows = await query(
      'SELECT * FROM user_assets WHERE user_id = ?',
      [userId]
    )

    if (assetRows.length > 0) {
      console.log(`✅ 用户资产记录已存在`)
      console.log(`贝壳余额: ${assetRows[0].shells_balance}`)
      console.log(`钻石余额: ${assetRows[0].diamonds_balance}`)
    } else {
      console.log(`❌ 用户资产记录不存在，需要手动创建`)
      
      // 手动创建资产记录
      await query(`
        INSERT INTO user_assets (
          user_id, shells_balance, diamonds_balance, 
          total_invested_shells, total_earned_diamonds,
          frozen_shells, frozen_diamonds, created_at, updated_at
        ) VALUES (?, 0, 0, 0, 0, 0, 0, NOW(), NOW())
      `, [userId])
      
      console.log(`✅ 手动创建用户资产记录成功`)
    }

    // 3. 模拟充值操作
    console.log(`\n=== 模拟充值操作 ===`)
    const rechargeAmount = 100000 // 充值10万贝壳

    // 获取充值前余额
    const beforeAsset = await query(
      'SELECT shells_balance FROM user_assets WHERE user_id = ?',
      [userId]
    )
    const balanceBefore = parseFloat(beforeAsset[0].shells_balance)
    console.log(`充值前余额: ${balanceBefore}`)

    // 生成交易流水号
    const transactionNo = `TXN${Date.now()}${Math.random().toString(36).substr(2, 6).toUpperCase()}`

    // 创建充值交易记录
    await query(`
      INSERT INTO user_asset_transactions (
        user_id, transaction_type, amount, balance_before, balance_after,
        related_type, description, transaction_no, status, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `, [
      userId,
      'shells_in',
      rechargeAmount,
      balanceBefore,
      balanceBefore + rechargeAmount,
      'recharge',
      `测试充值 ${rechargeAmount} 贝壳`,
      transactionNo,
      'completed'
    ])

    // 更新用户资产
    await query(`
      UPDATE user_assets 
      SET 
        shells_balance = shells_balance + ?,
        total_invested_shells = total_invested_shells + ?,
        updated_at = NOW()
      WHERE user_id = ?
    `, [rechargeAmount, rechargeAmount, userId])

    // 获取充值后余额
    const afterAsset = await query(
      'SELECT shells_balance, total_invested_shells FROM user_assets WHERE user_id = ?',
      [userId]
    )
    const balanceAfter = parseFloat(afterAsset[0].shells_balance)
    const totalInvested = parseFloat(afterAsset[0].total_invested_shells)

    console.log(`充值金额: ${rechargeAmount}`)
    console.log(`充值后余额: ${balanceAfter}`)
    console.log(`总投资贝壳: ${totalInvested}`)
    console.log(`交易流水号: ${transactionNo}`)

    // 4. 验证充值结果
    console.log(`\n=== 验证充值结果 ===`)
    if (balanceAfter === balanceBefore + rechargeAmount) {
      console.log(`✅ 充值成功！余额正确更新`)
    } else {
      console.log(`❌ 充值失败！余额更新错误`)
      console.log(`期望余额: ${balanceBefore + rechargeAmount}`)
      console.log(`实际余额: ${balanceAfter}`)
    }

    // 5. 查询充值记录
    console.log(`\n=== 查询充值记录 ===`)
    const rechargeRecords = await query(`
      SELECT 
        transaction_no, amount, status, description,
        DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s') as created_at
      FROM user_asset_transactions 
      WHERE user_id = ? AND transaction_type = 'shells_in' AND related_type = 'recharge'
      ORDER BY created_at DESC
    `, [userId])

    console.log(`充值记录数量: ${rechargeRecords.length}`)
    rechargeRecords.forEach((record, index) => {
      console.log(`记录 ${index + 1}:`)
      console.log(`  交易号: ${record.transaction_no}`)
      console.log(`  金额: ${record.amount}`)
      console.log(`  状态: ${record.status}`)
      console.log(`  描述: ${record.description}`)
      console.log(`  时间: ${record.created_at}`)
    })

    // 6. 清理测试数据
    console.log(`\n=== 清理测试数据 ===`)
    await query('DELETE FROM user_asset_transactions WHERE user_id = ?', [userId])
    await query('DELETE FROM user_assets WHERE user_id = ?', [userId])
    await query('DELETE FROM users WHERE id = ?', [userId])
    console.log(`✅ 测试数据清理完成`)

    console.log(`\n🎉 新用户充值功能测试通过！`)

  } catch (error) {
    console.error('测试过程中发生错误:', error)
    throw error
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testNewUserRecharge().then(() => {
    console.log('测试完成')
    process.exit(0)
  }).catch(error => {
    console.error('测试失败:', error)
    process.exit(1)
  })
}

export { testNewUserRecharge }
