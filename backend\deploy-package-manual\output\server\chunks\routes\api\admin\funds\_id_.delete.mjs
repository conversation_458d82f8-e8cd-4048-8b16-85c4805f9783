import { c as defineEvent<PERSON>and<PERSON>, f as createError, j as getRouterParam, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__delete = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const fundId = getRouterParam(event, "id");
    if (!fundId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u57FA\u91D1ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const existingFund = await query(
      "SELECT id, code, title FROM funds WHERE id = ?",
      [fundId]
    );
    if (existingFund.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u57FA\u91D1\u4E0D\u5B58\u5728"
      });
    }
    const fund = existingFund[0];
    await query("START TRANSACTION");
    try {
      await Promise.all([
        query("DELETE FROM fund_highlights WHERE fund_id = ?", [fundId]),
        query("DELETE FROM fund_documents WHERE fund_id = ?", [fundId]),
        query("DELETE FROM fund_faqs WHERE fund_id = ?", [fundId]),
        query("DELETE FROM fund_timelines WHERE fund_id = ?", [fundId]),
        query("DELETE FROM fund_fees WHERE fund_id = ?", [fundId]),
        query("DELETE FROM fund_performances WHERE fund_id = ?", [fundId]),
        query("DELETE FROM fund_usage_plans WHERE fund_id = ?", [fundId]),
        query("DELETE FROM fund_success_cases WHERE fund_id = ?", [fundId])
      ]);
      await query("DELETE FROM funds WHERE id = ?", [fundId]);
      await query("COMMIT");
      await logAuditAction({
        action: "ADMIN_DELETE_FUND",
        description: `\u7BA1\u7406\u5458\u5220\u9664\u57FA\u91D1: ${fund.title} (${fund.code})`,
        userId: admin.id,
        username: admin.username,
        ip: getClientIP(event),
        userAgent: getHeader(event, "user-agent") || "",
        details: {
          fundId: parseInt(fundId),
          code: fund.code,
          title: fund.title
        }
      });
      logger.info("\u7BA1\u7406\u5458\u5220\u9664\u57FA\u91D1\u6210\u529F", {
        adminId: admin.id,
        adminUsername: admin.username,
        fundId,
        code: fund.code,
        title: fund.title
      });
      return {
        success: true,
        message: "\u57FA\u91D1\u5220\u9664\u6210\u529F"
      };
    } catch (error) {
      await query("ROLLBACK");
      throw error;
    }
  } catch (error) {
    logger.error("\u5220\u9664\u57FA\u91D1\u5931\u8D25", {
      error: error.message,
      fundId: getRouterParam(event, "id"),
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { _id__delete as default };
//# sourceMappingURL=_id_.delete.mjs.map
