import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, g as getQuery, q as query, l as logger, e as getClientIP } from '../../../_/nitro.mjs';
import { v as validatePagination } from '../../../_/validators.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_get = defineEventHandler(async (event) => {
  var _a, _b;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const queryParams = getQuery(event);
    const { page, pageSize, offset } = validatePagination(queryParams.page, queryParams.pageSize);
    const {
      search,
      type,
      risk,
      minInvestment,
      period,
      status,
      is_published
    } = queryParams;
    let whereClause = "WHERE 1=1";
    const params = [];
    if (search) {
      whereClause += " AND (title LIKE ? OR code LIKE ?)";
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern);
      console.log("\u641C\u7D22\u53C2\u6570:", search, "\u641C\u7D22\u6A21\u5F0F:", searchPattern);
    }
    if (type) {
      whereClause += " AND type = ?";
      params.push(type);
    }
    if (risk) {
      whereClause += " AND risk = ?";
      params.push(risk);
    }
    if (minInvestment) {
      whereClause += " AND min_investment >= ?";
      params.push(parseInt(minInvestment));
    }
    if (period) {
      whereClause += " AND period = ?";
      params.push(period);
    }
    if (is_published !== void 0 && is_published !== null && is_published !== "") {
      whereClause += " AND is_published = ?";
      params.push(Number(is_published));
    } else if (status === "published") {
      whereClause += " AND is_published = 1";
    } else if (status === "draft") {
      whereClause += " AND is_published = 0";
    }
    const countResult = await query(
      `SELECT COUNT(*) as total FROM funds ${whereClause}`,
      params
    );
    const total = ((_a = countResult[0]) == null ? void 0 : _a.total) || 0;
    const funds = await query(
      `SELECT id, code, title, description, type, risk, min_investment, period,
              target_size, raised_amount, expected_return, min_holding_period,
              is_published, created_at, updated_at
       FROM funds 
       ${whereClause}
       ORDER BY created_at DESC
       LIMIT ? OFFSET ?`,
      [...params, pageSize, offset]
    );
    const formattedFunds = funds.map((fund) => ({
      id: fund.id,
      code: fund.code,
      title: fund.title,
      description: fund.description,
      type: fund.type,
      risk: fund.risk,
      min_investment: fund.min_investment,
      period: fund.period,
      target_size: fund.target_size,
      raised_amount: fund.raised_amount,
      expected_return: fund.expected_return,
      min_holding_period: fund.min_holding_period,
      risk_description: fund.risk_description,
      establish_date: fund.establish_date,
      exit_date: fund.exit_date,
      manager: fund.manager,
      trustee: fund.trustee,
      redemption_policy: fund.redemption_policy,
      investment_strategy: fund.investment_strategy,
      is_published: fund.is_published,
      created_at: fund.created_at,
      updated_at: fund.updated_at
    }));
    return {
      success: true,
      data: {
        list: formattedFunds,
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u57FA\u91D1\u5217\u8868\u5931\u8D25", {
      error: error.message,
      adminId: (_b = event.context.admin) == null ? void 0 : _b.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { index_get as default };
//# sourceMappingURL=index.get6.mjs.map
