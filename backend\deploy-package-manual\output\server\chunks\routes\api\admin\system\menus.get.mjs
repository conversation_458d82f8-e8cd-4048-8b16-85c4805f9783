import { c as defineEvent<PERSON>and<PERSON>, v as verifyAdminAccessToken, f as createError, q as query, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const menus_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(adminPayload.id, PERMISSIONS.SYSTEM_MENU_LIST);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u67E5\u770B\u83DC\u5355\u5217\u8868"
      });
    }
    const menus = await query(`
      SELECT
        id,
        pid,
        name,
        path,
        component,
        type,
        status,
        auth_code as authCode,
        icon,
        meta,
        sort_order as sortOrder,
        created_at,
        updated_at
      FROM admin_menus
      ORDER BY sort_order ASC, created_at ASC
    `);
    const treeData = buildMenuTree(menus);
    logger.info("\u83B7\u53D6\u7CFB\u7EDF\u83DC\u5355\u5217\u8868\u6210\u529F", {
      adminId: adminPayload.id,
      menuCount: menus.length,
      treeCount: treeData.length,
      ip: getClientIP(event)
    });
    console.log("\u{1F50D} \u540E\u7AEF\u83DC\u5355\u6570\u636E\u8C03\u8BD5:");
    console.log("\u{1F4CA} \u539F\u59CB\u83DC\u5355\u6570\u636E\u6570\u91CF:", menus.length);
    console.log("\u{1F333} \u6811\u5F62\u7ED3\u6784\u6570\u91CF:", treeData.length);
    console.log("\u{1F4DD} \u524D3\u4E2A\u539F\u59CB\u83DC\u5355\u9879:", menus.slice(0, 3));
    console.log("\u{1F332} \u524D2\u4E2A\u6811\u5F62\u8282\u70B9:", treeData.slice(0, 2));
    const responseData = {
      success: true,
      data: {
        list: treeData,
        total: menus.length
      }
    };
    console.log("\u{1F4E4} \u6700\u7EC8\u8FD4\u56DE\u6570\u636E\u7ED3\u6784:", {
      success: responseData.success,
      dataType: typeof responseData.data,
      listLength: responseData.data.list.length,
      total: responseData.data.total
    });
    return responseData;
  } catch (error) {
    logger.error("\u83B7\u53D6\u7CFB\u7EDF\u83DC\u5355\u5217\u8868\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});
function buildMenuTree(menus, parentId = null) {
  const tree = [];
  for (const menu of menus) {
    if (menu.pid === parentId) {
      const children = buildMenuTree(menus, menu.id);
      if (children.length > 0) {
        menu.children = children;
      }
      if (menu.meta && typeof menu.meta === "string") {
        try {
          menu.meta = JSON.parse(menu.meta);
        } catch {
          menu.meta = {};
        }
      }
      tree.push(menu);
    }
  }
  return tree;
}

export { menus_get as default };
//# sourceMappingURL=menus.get.mjs.map
