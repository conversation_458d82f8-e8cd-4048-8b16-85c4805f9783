import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, g as getQuery, q as query, f as createError } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_get = defineEventHandler(async (event) => {
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const query$1 = getQuery(event);
    const page = Number(query$1.page) || 1;
    const pageSize = Number(query$1.pageSize) || 10;
    const keyword = query$1.keyword || "";
    let whereClause = "";
    let queryParams = [];
    if (keyword) {
      whereClause = "WHERE name LIKE ?";
      queryParams.push(`%${keyword}%`);
    }
    const countSql = `SELECT COUNT(*) as total FROM drama_tags ${whereClause}`;
    const countResult = await query(countSql, queryParams);
    const total = countResult[0].total;
    const offset = (page - 1) * pageSize;
    const dataSql = `
      SELECT
        id,
        name,
        font_color as fontColor,
        background_color as backgroundColor,
        click_count as clickCount,
        operator,
        operated_at as operatedAt,
        created_at as createdAt,
        updated_at as updatedAt
      FROM drama_tags
      ${whereClause}
      ORDER BY operated_at DESC, id DESC
      LIMIT ? OFFSET ?
    `;
    const dataParams = [...queryParams, pageSize, offset];
    const tags = await query(dataSql, dataParams);
    const formattedTags = tags.map((tag) => ({
      ...tag,
      operatedAt: tag.operatedAt ? new Date(tag.operatedAt).toISOString() : null,
      createdAt: tag.createdAt ? new Date(tag.createdAt).toISOString() : null,
      updatedAt: tag.updatedAt ? new Date(tag.updatedAt).toISOString() : null
    }));
    return {
      success: true,
      message: "\u83B7\u53D6\u6807\u7B7E\u5217\u8868\u6210\u529F",
      data: {
        result: formattedTags,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u6807\u7B7E\u5217\u8868\u5931\u8D25:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "\u83B7\u53D6\u6807\u7B7E\u5217\u8868\u5931\u8D25"
    });
  }
});

export { index_get as default };
//# sourceMappingURL=index.get2.mjs.map
