function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
function validatePhone(phone) {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
}
function validatePassword(password) {
  if (password.length < 8) {
    return { valid: false, message: "\u5BC6\u7801\u957F\u5EA6\u81F3\u5C118\u4F4D" };
  }
  if (!/[a-z]/.test(password)) {
    return { valid: false, message: "\u5BC6\u7801\u5FC5\u987B\u5305\u542B\u5C0F\u5199\u5B57\u6BCD" };
  }
  if (!/[A-Z]/.test(password)) {
    return { valid: false, message: "\u5BC6\u7801\u5FC5\u987B\u5305\u542B\u5927\u5199\u5B57\u6BCD" };
  }
  if (!/\d/.test(password)) {
    return { valid: false, message: "\u5BC6\u7801\u5FC5\u987B\u5305\u542B\u6570\u5B57" };
  }
  return { valid: true, message: "\u5BC6\u7801\u5F3A\u5EA6\u7B26\u5408\u8981\u6C42" };
}
function validateUsername(username) {
  if (!username || username.length < 3) {
    return { valid: false, message: "\u7528\u6237\u540D\u957F\u5EA6\u81F3\u5C113\u4F4D" };
  }
  if (username.length > 50) {
    return { valid: false, message: "\u7528\u6237\u540D\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC750\u4F4D" };
  }
  if (!/^[a-zA-Z0-9_]+$/.test(username)) {
    return { valid: false, message: "\u7528\u6237\u540D\u53EA\u80FD\u5305\u542B\u5B57\u6BCD\u3001\u6570\u5B57\u548C\u4E0B\u5212\u7EBF" };
  }
  return { valid: true, message: "\u7528\u6237\u540D\u683C\u5F0F\u6B63\u786E" };
}
function validateFundData(data, isUpdate = false) {
  const errors = {};
  if (!isUpdate) {
    if (!data.code) errors.code = "\u57FA\u91D1\u4EE3\u7801\u662F\u5FC5\u987B\u7684";
    if (!data.title) errors.title = "\u57FA\u91D1\u540D\u79F0\u662F\u5FC5\u987B\u7684";
    if (!data.type) errors.type = "\u57FA\u91D1\u7C7B\u578B\u662F\u5FC5\u987B\u7684";
    if (!data.risk) errors.risk = "\u98CE\u9669\u7B49\u7EA7\u662F\u5FC5\u987B\u7684";
    if (!data.minInvestment) errors.minInvestment = "\u6700\u4F4E\u6295\u8D44\u91D1\u989D\u662F\u5FC5\u987B\u7684";
    if (!data.period) errors.period = "\u5C01\u95ED\u671F\u662F\u5FC5\u987B\u7684";
    if (!data.targetSize) errors.targetSize = "\u76EE\u6807\u89C4\u6A21\u662F\u5FC5\u987B\u7684";
    if (!data.expectedReturn) errors.expectedReturn = "\u9884\u671F\u6536\u76CA\u7387\u662F\u5FC5\u987B\u7684";
    if (!data.minHoldingPeriod) errors.minHoldingPeriod = "\u6700\u4F4E\u6301\u6709\u671F\u662F\u5FC5\u987B\u7684";
  }
  if (data.code !== void 0 && (!data.code || data.code.length > 32)) {
    errors.code = "\u57FA\u91D1\u4EE3\u7801\u4E0D\u80FD\u4E3A\u7A7A\u4E14\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC732\u4E2A\u5B57\u7B26";
  }
  if (data.title !== void 0 && (!data.title || data.title.length > 100)) {
    errors.title = "\u57FA\u91D1\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A\u4E14\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7100\u4E2A\u5B57\u7B26";
  }
  if (data.type !== void 0 && !["equity", "debt", "mixed"].includes(data.type)) {
    errors.type = "\u57FA\u91D1\u7C7B\u578B\u5FC5\u987B\u662Fequity\u3001debt\u6216mixed\u4E4B\u4E00";
  }
  if (data.risk !== void 0 && !["R1", "R2", "R3", "R4", "R5"].includes(data.risk)) {
    errors.risk = "\u98CE\u9669\u7B49\u7EA7\u5FC5\u987B\u662FR1\u3001R2\u3001R3\u3001R4\u6216R5\u4E4B\u4E00";
  }
  if (data.period !== void 0 && !["short", "medium", "long"].includes(data.period)) {
    errors.period = "\u5C01\u95ED\u671F\u5FC5\u987B\u662Fshort\u3001medium\u6216long\u4E4B\u4E00";
  }
  if (data.minInvestment !== void 0) {
    const minInvestment = parseInt(data.minInvestment);
    if (isNaN(minInvestment) || minInvestment <= 0) {
      errors.minInvestment = "\u6700\u4F4E\u6295\u8D44\u91D1\u989D\u5FC5\u987B\u662F\u6B63\u6574\u6570";
    }
  }
  if (data.targetSize !== void 0) {
    const targetSize = parseInt(data.targetSize);
    if (isNaN(targetSize) || targetSize <= 0) {
      errors.targetSize = "\u76EE\u6807\u89C4\u6A21\u5FC5\u987B\u662F\u6B63\u6574\u6570";
    }
  }
  if (data.expectedReturn !== void 0) {
    const expectedReturn = parseFloat(data.expectedReturn);
    if (isNaN(expectedReturn) || expectedReturn < 0 || expectedReturn > 100) {
      errors.expectedReturn = "\u9884\u671F\u6536\u76CA\u7387\u5FC5\u987B\u662F0-100\u4E4B\u95F4\u7684\u6570\u5B57";
    }
  }
  if (data.minHoldingPeriod !== void 0) {
    const minHoldingPeriod = parseInt(data.minHoldingPeriod);
    if (isNaN(minHoldingPeriod) || minHoldingPeriod <= 0) {
      errors.minHoldingPeriod = "\u6700\u4F4E\u6301\u6709\u671F\u5FC5\u987B\u662F\u6B63\u6574\u6570";
    }
  }
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}
function validatePagination(page, pageSize) {
  const validPage = Math.max(1, parseInt(String(page)) || 1);
  const validPageSize = Math.min(100, Math.max(1, parseInt(String(pageSize)) || 10));
  const offset = (validPage - 1) * validPageSize;
  return {
    page: validPage,
    pageSize: validPageSize,
    offset
  };
}
function validateId(id) {
  const numId = parseInt(String(id));
  return isNaN(numId) || numId <= 0 ? null : numId;
}
function validateStatus(status) {
  const numStatus = parseInt(String(status));
  return [0, 1].includes(numStatus) ? numStatus : null;
}

export { validateStatus as a, validateFundData as b, validateId as c, validatePassword as d, validateUsername as e, validateEmail as f, validatePhone as g, validatePagination as v };
//# sourceMappingURL=validators.mjs.map
