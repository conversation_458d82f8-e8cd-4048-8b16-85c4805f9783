-- 添加演艺经纪菜单
USE mengtu;

-- 插入一级菜单：演艺经纪
INSERT INTO admin_menus (pid, name, path, component, type, status, auth_code, icon, meta, sort_order) 
VALUES (NULL, '演艺经纪', '/talent', NULL, 'catalog', 1, 'AC_200000', 'lucide:users', '{"title":"演艺经纪","icon":"lucide:users"}', 200);

-- 获取刚插入的一级菜单ID
SET @talent_menu_id = LAST_INSERT_ID();

-- 插入二级菜单：艺人管理
INSERT INTO admin_menus (pid, name, path, component, type, status, auth_code, icon, meta, sort_order) 
VALUES (@talent_menu_id, '艺人管理', '/talent/artist', 'talent/artist/list', 'menu', 1, 'AC_200100', 'lucide:user-check', '{"title":"艺人管理","icon":"lucide:user-check"}', 201);

-- 查看插入结果
SELECT id, pid, name, path, component, type, status, auth_code, icon, sort_order 
FROM admin_menus 
WHERE name IN ('演艺经纪', '艺人管理') 
ORDER BY sort_order;
