import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, g as getQuery, q as query, o as logAdminAction, l as logger, e as getClientIP, f as createError } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_get = defineEventHandler(async (event) => {
  var _a, _b;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const query_params = getQuery(event);
    const {
      page = 1,
      pageSize = 20,
      search
    } = query_params;
    const pageNum = Math.max(1, parseInt(page) || 1);
    const pageSizeNum = Math.min(100, Math.max(1, parseInt(pageSize) || 20));
    const offset = (pageNum - 1) * pageSizeNum;
    let whereConditions = [];
    let queryParams = [];
    if (search && typeof search === "string" && search.trim()) {
      whereConditions.push("nt.name LIKE ?");
      queryParams.push(`%${search.trim()}%`);
    }
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(" AND ")}` : "";
    const countQuery = `
      SELECT COUNT(*) as total
      FROM news_tags nt
      ${whereClause}
    `;
    const countResult = await query(countQuery, queryParams);
    const total = ((_a = countResult[0]) == null ? void 0 : _a.total) || 0;
    const totalPages = Math.ceil(total / pageSizeNum);
    const tagsQuery = `
      SELECT 
        nt.id,
        nt.name,
        nt.created_at,
        nt.updated_at,
        COUNT(ntr.news_id) as usage_count
      FROM news_tags nt
      LEFT JOIN news_tag_relations ntr ON nt.id = ntr.tag_id
      ${whereClause}
      GROUP BY nt.id, nt.name, nt.created_at, nt.updated_at
      ORDER BY usage_count DESC, nt.created_at DESC
      LIMIT ? OFFSET ?
    `;
    const tags = await query(tagsQuery, [...queryParams, pageSizeNum, offset]);
    const formattedTags = tags.map((tag) => ({
      id: tag.id,
      name: tag.name,
      usageCount: tag.usage_count || 0,
      createdAt: tag.created_at,
      updatedAt: tag.updated_at
    }));
    await logAdminAction(admin.id, "news:tags:list", "\u67E5\u770B\u6807\u7B7E\u5217\u8868", {
      filters: { search },
      pagination: { page: pageNum, pageSize: pageSizeNum }
    });
    return {
      success: true,
      data: {
        list: formattedTags,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total,
          totalPages
        }
      }
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u6807\u7B7E\u5217\u8868\u5931\u8D25", {
      error: error.message,
      stack: error.stack,
      adminId: (_b = event.context.admin) == null ? void 0 : _b.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u83B7\u53D6\u6807\u7B7E\u5217\u8868\u5931\u8D25"
    });
  }
});

export { index_get as default };
//# sourceMappingURL=index.get.mjs.map
