# 构建阶段
FROM node:18-alpine AS builder

WORKDIR /app

# 安装pnpm
RUN npm install -g pnpm

# 进入具体的应用目录
# 为什么：fundAdmin是monorepo结构，需要进入具体应用
WORKDIR /app/apps/web-antd

# 复制根目录的package.json（如果有workspace配置）
COPY package.json pnpm-lock.yaml ../../

# 复制应用的package.json
COPY apps/web-antd/package.json ./

# 回到根目录安装依赖
WORKDIR /app
RUN pnpm install --frozen-lockfile

# 复制所有源代码
COPY . .

# 进入应用目录构建
WORKDIR /app/apps/web-antd

# 构建生产版本
# 为什么用build：Vue应用需要编译打包
RUN pnpm build

# 生产阶段
FROM nginx:alpine AS production

# 复制构建好的文件
# dist是Vue应用的默认构建输出目录
COPY --from=builder /app/apps/web-antd/dist /usr/share/nginx/html

# 复制Nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
