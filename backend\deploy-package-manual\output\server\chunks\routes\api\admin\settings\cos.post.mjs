import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, r as readBody, q as query, l as logger, h as logAuditAction, i as getHeader, e as getClientIP } from '../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const cos_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u4FEE\u6539\u5BF9\u8C61\u5B58\u50A8\u8BBE\u7F6E"
      });
    }
    const body = await readBody(event);
    const { provider, secretId, secretKey, bucket, region, domain, directory, isEnabled, isLoaded } = body;
    if (!provider || !bucket || !region) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u8BF7\u63D0\u4F9B\u670D\u52A1\u5546\u3001\u5B58\u50A8\u6876\u540D\u79F0\u548C\u5730\u57DF"
      });
    }
    const validProviders = ["tencent", "aliyun", "qiniu"];
    if (!validProviders.includes(provider)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u4E0D\u652F\u6301\u7684\u670D\u52A1\u5546"
      });
    }
    let existingSettings = {};
    try {
      const result = await query(
        "SELECT setting_value FROM system_settings WHERE setting_key = ?",
        ["cos_settings"]
      );
      if (result.length > 0 && result[0].setting_value) {
        existingSettings = JSON.parse(result[0].setting_value);
      }
    } catch (error) {
      logger.warn("\u83B7\u53D6\u73B0\u6709\u5BF9\u8C61\u5B58\u50A8\u8BBE\u7F6E\u5931\u8D25", { error: error.message });
    }
    const cosSettings = {
      provider: provider.trim(),
      secretId: secretId && secretId !== "******" ? secretId : existingSettings.secretId || "",
      secretKey: secretKey && secretKey !== "******" ? secretKey : existingSettings.secretKey || "",
      bucket: bucket.trim(),
      region: region.trim(),
      domain: domain ? domain.trim() : "",
      directory: directory ? directory.trim() : existingSettings.directory || "uploads",
      isEnabled: typeof isEnabled === "boolean" ? isEnabled : existingSettings.isEnabled !== false,
      isLoaded: typeof isLoaded === "boolean" ? isLoaded : existingSettings.isLoaded !== false
    };
    const settingsJson = JSON.stringify(cosSettings);
    const existingSettingsResult = await query(
      "SELECT id FROM system_settings WHERE setting_key = ?",
      ["cos_settings"]
    );
    if (existingSettingsResult.length > 0) {
      await query(
        "UPDATE system_settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?",
        [settingsJson, "cos_settings"]
      );
    } else {
      await query(
        "INSERT INTO system_settings (setting_key, setting_value, created_at, updated_at) VALUES (?, ?, NOW(), NOW())",
        ["cos_settings", settingsJson]
      );
    }
    await logAuditAction({
      action: "ADMIN_UPDATE_COS_SETTINGS",
      description: "\u7BA1\u7406\u5458\u66F4\u65B0\u5BF9\u8C61\u5B58\u50A8\u8BBE\u7F6E",
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: { provider, bucket, region, domain }
    });
    return {
      success: true,
      message: "\u5BF9\u8C61\u5B58\u50A8\u8BBE\u7F6E\u66F4\u65B0\u6210\u529F"
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u5BF9\u8C61\u5B58\u50A8\u8BBE\u7F6E\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { cos_post as default };
//# sourceMappingURL=cos.post.mjs.map
