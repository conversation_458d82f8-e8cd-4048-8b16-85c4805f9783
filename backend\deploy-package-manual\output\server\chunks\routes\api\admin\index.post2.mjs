import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, r as readBody, f as createError, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_post = defineEventHandler(async (event) => {
  var _a, _b;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const body = await readBody(event);
    const {
      title,
      subtitle,
      image_url,
      link_url,
      background_color,
      text_color,
      is_active,
      open_in_new_tab,
      sort_order
    } = body;
    if (!title) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6A2A\u5E45\u6807\u9898\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    if (!image_url) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6A2A\u5E45\u56FE\u7247\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const isActiveValue = is_active ? 1 : 0;
    const openInNewTabValue = open_in_new_tab ? 1 : 0;
    const sortOrderValue = sort_order ? parseInt(sort_order) : 0;
    let finalSortOrder = sortOrderValue;
    if (finalSortOrder === 0) {
      const maxSortResult = await query("SELECT MAX(sort_order) as max_sort FROM banners");
      finalSortOrder = (((_a = maxSortResult[0]) == null ? void 0 : _a.max_sort) || 0) + 1;
    }
    const result = await query(
      `INSERT INTO banners (title, subtitle, image_url, link_url, background_color, 
                           text_color, is_active, open_in_new_tab, sort_order, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [
        title.trim(),
        (subtitle == null ? void 0 : subtitle.trim()) || null,
        image_url.trim(),
        (link_url == null ? void 0 : link_url.trim()) || null,
        background_color || "#ffffff",
        text_color || "#000000",
        isActiveValue,
        openInNewTabValue,
        finalSortOrder
      ]
    );
    const newBannerId = result.insertId;
    await logAuditAction({
      action: "ADMIN_CREATE_BANNER",
      description: `\u7BA1\u7406\u5458\u521B\u5EFA\u6A2A\u5E45: ${title}`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        bannerId: newBannerId,
        title,
        subtitle,
        imageUrl: image_url,
        linkUrl: link_url,
        isActive: isActiveValue === 1,
        sortOrder: finalSortOrder
      }
    });
    logger.info("\u7BA1\u7406\u5458\u521B\u5EFA\u6A2A\u5E45\u6210\u529F", {
      adminId: admin.id,
      adminUsername: admin.username,
      bannerId: newBannerId,
      title
    });
    return {
      success: true,
      message: "\u6A2A\u5E45\u521B\u5EFA\u6210\u529F",
      data: {
        id: newBannerId
      }
    };
  } catch (error) {
    logger.error("\u521B\u5EFA\u6A2A\u5E45\u5931\u8D25", {
      error: error.message,
      adminId: (_b = event.context.admin) == null ? void 0 : _b.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { index_post as default };
//# sourceMappingURL=index.post2.mjs.map
