import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, r as readBody, f as createError, q as query, o as logAdminAction, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const body = await readBody(event);
    const { name } = body;
    if (!name || typeof name !== "string" || name.trim().length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6807\u7B7E\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const trimmedName = name.trim();
    if (trimmedName.length > 50) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6807\u7B7E\u540D\u79F0\u4E0D\u80FD\u8D85\u8FC750\u4E2A\u5B57\u7B26"
      });
    }
    const existingTag = await query(
      "SELECT id FROM news_tags WHERE name = ?",
      [trimmedName]
    );
    if (existingTag.length > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6807\u7B7E\u540D\u79F0\u5DF2\u5B58\u5728"
      });
    }
    const insertResult = await query(
      `INSERT INTO news_tags (name, created_at, updated_at) 
       VALUES (?, NOW(), NOW())`,
      [trimmedName]
    );
    const tagId = insertResult.insertId;
    const newTag = await query(
      "SELECT id, name, created_at, updated_at FROM news_tags WHERE id = ?",
      [tagId]
    );
    const tag = newTag[0];
    await logAdminAction(admin.id, "news:tags:create", "\u521B\u5EFA\u65B0\u95FB\u6807\u7B7E", {
      tagId,
      tagName: trimmedName
    });
    return {
      success: true,
      message: "\u6807\u7B7E\u521B\u5EFA\u6210\u529F",
      data: {
        id: tag.id,
        name: tag.name,
        usageCount: 0,
        createdAt: tag.created_at,
        updatedAt: tag.updated_at
      }
    };
  } catch (error) {
    logger.error("\u521B\u5EFA\u6807\u7B7E\u5931\u8D25", {
      error: error.message,
      stack: error.stack,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u521B\u5EFA\u6807\u7B7E\u5931\u8D25"
    });
  }
});

export { index_post as default };
//# sourceMappingURL=index.post.mjs.map
