{"name": "cookie-es", "version": "1.2.2", "repository": "unjs/cookie-es", "license": "MIT", "sideEffects": false, "type": "module", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.cts", "files": ["dist"], "scripts": {"build": "unbuild", "dev": "vitest --coverage", "lint": "eslint --cache . && prettier -c src test", "lint:fix": "automd && eslint --cache . --fix && prettier -c src test -w", "release": "pnpm test && pnpm build && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --coverage"}, "devDependencies": {"@vitest/coverage-v8": "^2.0.3", "automd": "^0.3.8", "changelogen": "^0.5.5", "eslint": "^9.7.0", "eslint-config-unjs": "^0.3.2", "prettier": "^3.3.3", "typescript": "^5.5.3", "unbuild": "^2.0.0", "vitest": "^2.0.3"}, "packageManager": "pnpm@9.5.0"}