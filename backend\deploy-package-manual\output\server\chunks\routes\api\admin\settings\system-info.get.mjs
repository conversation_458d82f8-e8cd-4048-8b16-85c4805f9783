import { c as defineEvent<PERSON>and<PERSON>, f as createError, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const systemInfo_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u8BBF\u95EE\u7CFB\u7EDF\u4FE1\u606F"
      });
    }
    const systemInfo = {
      // 运行环境信息
      os: process.platform,
      nodeVersion: process.version,
      processId: process.pid,
      uptime: process.uptime(),
      // 内存使用情况
      memoryUsage: process.memoryUsage(),
      // CPU使用情况
      cpuUsage: process.cpuUsage(),
      // 环境变量
      environment: "production",
      // 时间信息
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      // 系统版本
      version: "1.0.0"
    };
    await logAuditAction({
      action: "ADMIN_VIEW_SYSTEM_INFO",
      description: "\u7BA1\u7406\u5458\u67E5\u770B\u7CFB\u7EDF\u4FE1\u606F",
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: { action: "view_system_info" }
    });
    return {
      success: true,
      data: systemInfo
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u7CFB\u7EDF\u4FE1\u606F\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { systemInfo_get as default };
//# sourceMappingURL=system-info.get.mjs.map
