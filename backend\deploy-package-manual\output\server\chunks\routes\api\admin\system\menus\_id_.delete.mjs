import { c as defineE<PERSON><PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, q as query, l as logger, e as getClientIP } from '../../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__delete = defineEventHandler(async (event) => {
  var _a;
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(adminPayload.id, PERMISSIONS.SYSTEM_MENU_DELETE);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u5220\u9664\u83DC\u5355"
      });
    }
    const menuId = getRouterParam(event, "id");
    if (!menuId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u83DC\u5355ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const existingMenu = await query(`
      SELECT id, name FROM admin_menus WHERE id = ?
    `, [menuId]);
    if (existingMenu.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u83DC\u5355\u4E0D\u5B58\u5728"
      });
    }
    const childMenus = await query(`
      SELECT id FROM admin_menus WHERE pid = ?
    `, [menuId]);
    if (childMenus.length > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u8BE5\u83DC\u5355\u4E0B\u8FD8\u6709\u5B50\u83DC\u5355\uFF0C\u65E0\u6CD5\u5220\u9664"
      });
    }
    await query(`
      DELETE FROM admin_menus WHERE id = ?
    `, [menuId]);
    logger.info("\u5220\u9664\u83DC\u5355\u6210\u529F", {
      adminId: adminPayload.id,
      menuId,
      menuName: existingMenu[0].name,
      ip: getClientIP(event)
    });
    return {
      success: true,
      data: {
        message: "\u83DC\u5355\u5220\u9664\u6210\u529F"
      }
    };
  } catch (error) {
    logger.error("\u5220\u9664\u83DC\u5355\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { _id__delete as default };
//# sourceMappingURL=_id_.delete.mjs.map
