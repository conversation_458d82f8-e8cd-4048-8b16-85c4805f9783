-- 为用户ID 6插入个人面板数据
-- 设置正确的字符集
SET NAMES utf8 COLLATE utf8_unicode_ci;
SET CHARACTER SET utf8;

-- 数据说明：
-- 总投资贝壳: 1,500,000
-- 已消耗贝壳: 1,200,000 (500,000 + 400,000 + 300,000)
-- 所剩投资贝壳: 300,000 (总投资 - 已消耗)
-- 累计收益钻石: 180,000 (75,000 + 48,000 + 15,000 + 其他收益)
-- 收益率计算: (收益钻石 - 消耗贝壳) / 消耗贝壳 = (180,000 - 1,200,000) / 1,200,000 = -85%

-- 首先确保用户ID 6存在，如果不存在则创建
INSERT IGNORE INTO users (id, username, password_hash, email, user_type, status, created_at, updated_at) 
VALUES (6, '投资者用户6', '$2b$10$example_hash', '<EMAIL>', 'investor', 1, NOW(), NOW());

-- 插入用户资产数据
-- 根据新的计算逻辑：
-- 总投资贝壳: 1500000
-- 已消耗贝壳: 1200000  
-- 所剩投资贝壳 = 总投资贝壳 - 已消耗贝壳 = 1500000 - 1200000 = 300000
-- 累计收益钻石: 180000
-- 收益率 = (已消耗贝壳 - 累计收益钻石) / 已消耗贝壳 = (1200000 - 180000) / 1200000 = 85%
INSERT IGNORE INTO user_assets (
    user_id, shells_balance, diamonds_balance, 
    total_invested_shells, total_earned_diamonds,
    frozen_shells, frozen_diamonds
) VALUES (6, 300000, 180000, 1500000, 180000, 0, 0);

-- 插入投资记录
INSERT IGNORE INTO user_investments (
    user_id, project_id, project_name, investment_amount, investment_date,
    expected_return_rate, expected_return_amount, actual_return_amount,
    project_status, investment_status, start_date, end_date, progress
) VALUES 
(6, 1, '都市情感系列', 500000, '2023-05-20', 18.5, 150000, 75000, 'active', 'active', '2023-05-20', '2024-05-19', 65),
(6, 2, '青春有你系列', 400000, '2023-07-10', 16.0, 120000, 48000, 'active', 'active', '2023-07-10', '2024-07-09', 40),
(6, 3, '奇幻世界系列', 300000, '2023-09-01', 20.0, 90000, 15000, 'active', 'active', '2023-09-01', '2024-08-31', 15);

-- 获取投资记录ID
SET @investment1 = (SELECT id FROM user_investments WHERE user_id = 6 AND project_name = '都市情感系列' LIMIT 1);
SET @investment2 = (SELECT id FROM user_investments WHERE user_id = 6 AND project_name = '青春有你系列' LIMIT 1);
SET @investment3 = (SELECT id FROM user_investments WHERE user_id = 6 AND project_name = '奇幻世界系列' LIMIT 1);

-- 插入收益记录
INSERT IGNORE INTO user_returns (
    user_id, investment_id, return_type, return_amount, return_date,
    return_period, description, status
) VALUES 
-- 都市情感系列收益记录
(6, @investment1, 'monthly', 12500, '2023-06-01', '2023年5月', '都市情感系列5月份收益分红', 'paid'),
(6, @investment1, 'monthly', 12500, '2023-07-01', '2023年6月', '都市情感系列6月份收益分红', 'paid'),
(6, @investment1, 'monthly', 12500, '2023-08-01', '2023年7月', '都市情感系列7月份收益分红', 'paid'),
(6, @investment1, 'monthly', 12500, '2023-09-01', '2023年8月', '都市情感系列8月份收益分红', 'paid'),
(6, @investment1, 'monthly', 12500, '2023-10-01', '2023年9月', '都市情感系列9月份收益分红', 'paid'),
(6, @investment1, 'monthly', 12500, '2023-11-01', '2023年10月', '都市情感系列10月份收益分红', 'paid'),

-- 青春有你系列收益记录
(6, @investment2, 'monthly', 8000, '2023-08-01', '2023年7月', '青春有你系列7月份收益分红', 'paid'),
(6, @investment2, 'monthly', 8000, '2023-09-01', '2023年8月', '青春有你系列8月份收益分红', 'paid'),
(6, @investment2, 'monthly', 8000, '2023-10-01', '2023年9月', '青春有你系列9月份收益分红', 'paid'),
(6, @investment2, 'monthly', 8000, '2023-11-01', '2023年10月', '青春有你系列10月份收益分红', 'paid'),
(6, @investment2, 'monthly', 8000, '2023-12-01', '2023年11月', '青春有你系列11月份收益分红', 'paid'),
(6, @investment2, 'monthly', 8000, '2024-01-01', '2023年12月', '青春有你系列12月份收益分红', 'paid'),

-- 奇幻世界系列收益记录
(6, @investment3, 'monthly', 5000, '2023-10-01', '2023年9月', '奇幻世界系列9月份收益分红', 'paid'),
(6, @investment3, 'monthly', 5000, '2023-11-01', '2023年10月', '奇幻世界系列10月份收益分红', 'paid'),
(6, @investment3, 'monthly', 5000, '2023-12-01', '2023年11月', '奇幻世界系列11月份收益分红', 'paid');

-- 插入通知
INSERT IGNORE INTO user_notifications (
    user_id, type, title, content, is_read, priority
) VALUES 
(6, 'system', '第三季度财务报告已发布', '您可以在项目详情页查看最新的财务报告', 0, 'normal'),
(6, 'project', '都市情感第8集开始拍摄', '项目进展顺利，预计按时完成', 1, 'normal'),
(6, 'return', '9月份收益已发放', '本月共获得25500钻石收益', 1, 'normal'),
(6, 'system', '投资者线上会议将于下周三举行', '请关注邮件通知，准时参加会议', 0, 'high'),
(6, 'investment', '新项目科幻未来开始募资', '预期年化收益率22%，限额投资', 0, 'normal');

-- 插入资产变动记录
INSERT IGNORE INTO user_asset_transactions (
    user_id, transaction_type, amount, balance_before, balance_after,
    related_type, description, transaction_no, status
) VALUES 
-- 初始充值
(6, 'shells_in', 1500000, 0, 1500000, 'recharge', '初始资金充值', 'TXN202305150001', 'completed'),

-- 投资支出
(6, 'shells_out', 500000, 1500000, 1000000, 'investment', '投资都市情感系列', 'TXN202305200001', 'completed'),
(6, 'shells_out', 400000, 1000000, 600000, 'investment', '投资青春有你系列', 'TXN202307100001', 'completed'),
(6, 'shells_out', 300000, 600000, 300000, 'investment', '投资奇幻世界系列', 'TXN202309010001', 'completed'),

-- 收益入账记录（按月）
(6, 'diamonds_in', 12500, 0, 12500, 'return', '都市情感系列5月收益', 'TXN202306010001', 'completed'),
(6, 'diamonds_in', 12500, 12500, 25000, 'return', '都市情感系列6月收益', 'TXN202307010001', 'completed'),
(6, 'diamonds_in', 12500, 25000, 37500, 'return', '都市情感系列7月收益', 'TXN202308010001', 'completed'),
(6, 'diamonds_in', 8000, 37500, 45500, 'return', '青春有你系列7月收益', 'TXN202308010002', 'completed'),
(6, 'diamonds_in', 12500, 45500, 58000, 'return', '都市情感系列8月收益', 'TXN202309010001', 'completed'),
(6, 'diamonds_in', 8000, 58000, 66000, 'return', '青春有你系列8月收益', 'TXN202309010002', 'completed'),
(6, 'diamonds_in', 12500, 66000, 78500, 'return', '都市情感系列9月收益', 'TXN202310010001', 'completed'),
(6, 'diamonds_in', 8000, 78500, 86500, 'return', '青春有你系列9月收益', 'TXN202310010002', 'completed'),
(6, 'diamonds_in', 5000, 86500, 91500, 'return', '奇幻世界系列9月收益', 'TXN202310010003', 'completed'),
(6, 'diamonds_in', 12500, 91500, 104000, 'return', '都市情感系列10月收益', 'TXN202311010001', 'completed'),
(6, 'diamonds_in', 8000, 104000, 112000, 'return', '青春有你系列10月收益', 'TXN202311010002', 'completed'),
(6, 'diamonds_in', 5000, 112000, 117000, 'return', '奇幻世界系列10月收益', 'TXN202311010003', 'completed');
