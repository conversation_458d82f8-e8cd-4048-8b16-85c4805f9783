import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, g as getQuery, q as query, f as createError } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_get = defineEventHandler(async (event) => {
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const query$1 = getQuery(event);
    const page = Number(query$1.page) || 1;
    const pageSize = Number(query$1.pageSize) || 10;
    const keyword = query$1.keyword || "";
    const platformType = query$1.platformType || "";
    const status = query$1.status || "";
    let whereConditions = ["1=1"];
    let queryParams = [];
    if (keyword) {
      whereConditions.push("(dp.platform_name LIKE ? OR b.company_name LIKE ?)");
      queryParams.push(`%${keyword}%`, `%${keyword}%`);
    }
    if (platformType) {
      whereConditions.push("dp.platform_type = ?");
      queryParams.push(platformType);
    }
    if (status) {
      const isActive = status === "active" ? 1 : 0;
      whereConditions.push("dp.is_active = ?");
      queryParams.push(isActive);
    }
    const whereClause = whereConditions.join(" AND ");
    const countQuery = `
      SELECT COUNT(*) as total
      FROM drama_platforms dp
      LEFT JOIN brands b ON dp.company_id = b.id
      WHERE ${whereClause}
    `;
    const countResult = await query(countQuery, queryParams);
    const total = countResult[0].total;
    const offset = (page - 1) * pageSize;
    const dataQuery = `
      SELECT
        dp.id,
        dp.platform_name,
        dp.platform_logo_url,
        b.company_name,
        dp.company_id,
        dp.platform_domain,
        dp.mini_program_name,
        dp.mini_program_link,
        dp.mini_program_qrcode_url,
        dp.platform_type,
        dp.description,
        dp.is_active,
        dp.created_at,
        dp.updated_at
      FROM drama_platforms dp
      LEFT JOIN brands b ON dp.company_id = b.id
      WHERE ${whereClause}
      ORDER BY dp.created_at DESC
      LIMIT ? OFFSET ?
    `;
    const result = await query(dataQuery, [...queryParams, pageSize, offset]);
    const totalPages = Math.ceil(total / pageSize);
    return {
      success: true,
      message: "\u83B7\u53D6\u5E73\u53F0\u5217\u8868\u6210\u529F",
      data: {
        result,
        total,
        page,
        pageSize,
        totalPages
      }
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u5E73\u53F0\u5217\u8868\u5931\u8D25:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "\u83B7\u53D6\u5E73\u53F0\u5217\u8868\u5931\u8D25: " + error.message
    });
  }
});

export { index_get as default };
//# sourceMappingURL=index.get.mjs.map
