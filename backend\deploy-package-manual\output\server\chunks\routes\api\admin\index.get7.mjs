import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, q as query, l as logger, e as getClientIP } from '../../../_/nitro.mjs';
import { g as getAdminPermissionCodes } from '../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const permissions = await getAdminPermissionCodes(admin.id);
    const allMenus = await query(`
      SELECT id, pid, name, path, component, type, status, auth_code, 
             icon, meta, sort_order
      FROM admin_menus 
      WHERE status = 1
      ORDER BY sort_order ASC, id ASC
    `);
    const filteredMenus = allMenus.filter((menu) => {
      if (!menu.auth_code) {
        return true;
      }
      return permissions.includes(menu.auth_code);
    });
    const menuTree = buildMenuTree(filteredMenus);
    return {
      success: true,
      data: menuTree
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u7BA1\u7406\u5458\u83DC\u5355\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});
function buildMenuTree(menus, parentId = null) {
  const tree = [];
  for (const menu of menus) {
    if (menu.pid === parentId) {
      const menuItem = {
        id: menu.id,
        name: menu.name,
        path: menu.path,
        component: menu.component,
        type: menu.type,
        icon: menu.icon,
        meta: menu.meta ? JSON.parse(menu.meta) : {},
        children: buildMenuTree(menus, menu.id)
      };
      if (menuItem.children.length === 0) {
        delete menuItem.children;
      }
      tree.push(menuItem);
    }
  }
  return tree;
}

export { index_get as default };
//# sourceMappingURL=index.get7.mjs.map
