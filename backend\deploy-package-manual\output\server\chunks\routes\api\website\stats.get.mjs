import { c as defineEventHandler, q as query, M as useResponseSuccess, P as useResponseError } from '../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const stats_get = defineEventHandler(async (event) => {
  var _a, _b, _c, _d;
  try {
    const fundStatsResult = await query(`
      SELECT
        COALESCE(SUM(raised_amount), 0) as totalFundRaised
      FROM funds
      WHERE is_published = 1
    `);
    const totalFundRaised = Number(((_a = fundStatsResult[0]) == null ? void 0 : _a.totalFundRaised) || 0);
    const userInvestmentResult = await query(`
      SELECT
        COALESCE(SUM(total_invested_shells), 0) as totalUserInvestment
      FROM user_assets
    `);
    const totalUserInvestment = Number(((_b = userInvestmentResult[0]) == null ? void 0 : _b.totalUserInvestment) || 0);
    const totalRaisedAmount = (totalFundRaised + totalUserInvestment) / 1e4;
    const historicalInvestmentTotal = totalUserInvestment / 1e4;
    const todayInvestmentResult = await query(`
      SELECT
        COALESCE(SUM(investment_amount), 0) as todayInvestment
      FROM user_investments
      WHERE DATE(investment_date) = CURDATE()
    `);
    const todayInvestment = Number(((_c = todayInvestmentResult[0]) == null ? void 0 : _c.todayInvestment) || 0) / 1e4;
    const todayInvestmentRatio = historicalInvestmentTotal > 0 ? parseFloat((todayInvestment / historicalInvestmentTotal * 100).toFixed(1)) : 0;
    const investorCountResult = await query(`
      SELECT COUNT(*) as totalInvestors
      FROM users
      WHERE status = 1
    `);
    const totalInvestors = Number(((_d = investorCountResult[0]) == null ? void 0 : _d.totalInvestors) || 0);
    const usersResult = await query(`
      SELECT username, user_type
      FROM users
      WHERE status = 1
      ORDER BY RAND()
      LIMIT 20
    `);
    const formatUsername = (username) => {
      if (!username) return "";
      const chars = Array.from(username);
      if (chars.length <= 2) {
        return username;
      }
      const first = chars[0];
      const last = chars[chars.length - 1];
      const stars = "*".repeat(chars.length - 2);
      return `${first}${stars}${last}`;
    };
    const userTypeLabels = {
      "investor": "\u6295\u8D44\u8005",
      "producer": "\u627F\u5236\u5382\u724C",
      "fund_manager": "\u57FA\u91D1\u7BA1\u7406\u5458"
    };
    const formattedUsers = usersResult.map((user) => ({
      username: formatUsername(user.username),
      userType: userTypeLabels[user.user_type] || "\u6295\u8D44\u8005"
    }));
    return useResponseSuccess({
      // 已募资金总额（万元）
      totalRaisedAmount: parseFloat(totalRaisedAmount.toFixed(2)),
      // 募资金额增长率（替代过往项目年化收益）
      fundingGrowthRate: 36.4,
      // 今日投资总额（万元）
      todayInvestmentAmount: parseFloat(todayInvestment.toFixed(2)),
      // 今日投资占比
      todayInvestmentRatio,
      // 历史投资总额（万元）
      historicalInvestmentTotal: parseFloat(historicalInvestmentTotal.toFixed(2)),
      // 总投资人数
      totalInvestors,
      // 用户列表（用于实时在线显示）
      users: formattedUsers
    });
  } catch (error) {
    console.error("\u83B7\u53D6\u7F51\u7AD9\u7EDF\u8BA1\u6570\u636E\u5931\u8D25:", error);
    return useResponseError("\u83B7\u53D6\u7EDF\u8BA1\u6570\u636E\u5931\u8D25");
  }
});

export { stats_get as default };
//# sourceMappingURL=stats.get.mjs.map
