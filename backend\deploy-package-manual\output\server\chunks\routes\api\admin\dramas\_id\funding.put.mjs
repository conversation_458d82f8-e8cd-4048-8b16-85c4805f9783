import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, f as createError, r as readBody, q as query } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const funding_put = defineEventHandler(async (event) => {
  try {
    const dramaId = getRouterParam(event, "id");
    if (!dramaId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u77ED\u5267ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const id = parseInt(dramaId);
    if (isNaN(id) || id <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u77ED\u5267ID"
      });
    }
    const body = await readBody(event);
    const { funding } = body;
    if (!funding) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u52DF\u8D44\u4FE1\u606F\u6570\u636E\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const dramaExists = await query(
      "SELECT id FROM drama_series WHERE id = ?",
      [id]
    );
    if (dramaExists.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u77ED\u5267\u4E0D\u5B58\u5728"
      });
    }
    const existingFunding = await query(
      "SELECT id FROM drama_funding_info WHERE drama_id = ?",
      [id]
    );
    if (existingFunding.length > 0) {
      await query(`
        UPDATE drama_funding_info
        SET
          funding_goal = ?,
          current_funding = ?,
          funding_end_date = ?,
          funding_share = ?,
          min_investment = ?,
          roi = ?,
          status = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE drama_id = ?
      `, [
        funding.fundingGoal || null,
        funding.currentFunding || 0,
        funding.fundingEndDate || null,
        funding.fundingShare || null,
        funding.minInvestment || null,
        funding.roi || null,
        funding.status || "draft",
        id
      ]);
    } else {
      await query(`
        INSERT INTO drama_funding_info (
          drama_id,
          funding_goal,
          current_funding,
          funding_end_date,
          funding_share,
          min_investment,
          roi,
          status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        id,
        funding.fundingGoal || null,
        funding.currentFunding || 0,
        funding.fundingEndDate || null,
        funding.fundingShare || null,
        funding.minInvestment || null,
        funding.roi || null,
        funding.status || "draft"
      ]);
    }
    return {
      success: true,
      message: "\u52DF\u8D44\u4FE1\u606F\u66F4\u65B0\u6210\u529F",
      data: {
        dramaId: id,
        funding: {
          fundingGoal: funding.fundingGoal,
          currentFunding: funding.currentFunding,
          fundingEndDate: funding.fundingEndDate,
          fundingShare: funding.fundingShare,
          minInvestment: funding.minInvestment,
          roi: funding.roi,
          status: funding.status
        }
      }
    };
  } catch (error) {
    console.error("\u66F4\u65B0\u52DF\u8D44\u4FE1\u606F\u5931\u8D25:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { funding_put as default };
//# sourceMappingURL=funding.put.mjs.map
