import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, j as getRouter<PERSON>aram, q as query, y as hashPassword, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const resetPassword_put = defineEventHandler(async (event) => {
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const userId = getRouterParam(event, "id");
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u7528\u6237ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const users = await query(
      "SELECT id, username FROM users WHERE id = ?",
      [userId]
    );
    if (users.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u7528\u6237\u4E0D\u5B58\u5728"
      });
    }
    const user = users[0];
    const defaultPassword = "fund123456";
    const hashedPassword = await hashPassword(defaultPassword);
    await query(
      "UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?",
      [hashedPassword, userId]
    );
    await logAuditAction({
      action: "ADMIN_RESET_PASSWORD",
      description: `\u7BA1\u7406\u5458\u91CD\u7F6E\u7528\u6237\u5BC6\u7801: ${user.username}`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || ""
    });
    logger.info("\u7BA1\u7406\u5458\u91CD\u7F6E\u7528\u6237\u5BC6\u7801\u6210\u529F", {
      adminId: admin.id,
      targetUserId: userId,
      targetUsername: user.username,
      ip: getClientIP(event)
    });
    return {
      success: true,
      data: {
        message: "\u5BC6\u7801\u91CD\u7F6E\u6210\u529F",
        defaultPassword
      }
    };
  } catch (error) {
    logger.error("\u91CD\u7F6E\u7528\u6237\u5BC6\u7801\u5931\u8D25", { error: error.message });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u91CD\u7F6E\u5BC6\u7801\u5931\u8D25"
    });
  }
});

export { resetPassword_put as default };
//# sourceMappingURL=reset-password.put.mjs.map
