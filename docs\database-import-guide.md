# 数据库导入指南

## 📊 数据库文件说明

### 文件列表
- **mengtu_backup.sql** - 原始数据库导出文件（数据库名：mengtu）
- **reelshortfund_production.sql** - 生产环境数据库文件（数据库名：reelshortfund）

### 导出信息
- **导出时间**: 2025-08-05 16:45:34
- **MySQL版本**: 5.6.46
- **字符集**: utf8mb4_unicode_ci
- **总行数**: 1680行
- **包含内容**: 完整的表结构和数据

## 🗃️ 数据库表结构

### 主要数据表
1. **actors** - 演员信息表
2. **banners** - 轮播图表
3. **brands** - 品牌表
4. **companies** - 公司表
5. **dramas** - 短剧表
6. **drama_actors** - 短剧演员关联表
7. **drama_companies** - 短剧公司关联表
8. **drama_tags** - 短剧标签关联表
9. **funds** - 基金表
10. **fund_usage_plans** - 基金使用计划表
11. **investment_records** - 投资记录表
12. **news** - 新闻表
13. **platforms** - 平台表
14. **posts** - 帖子表
15. **recharge_records** - 充值记录表
16. **tags** - 标签表
17. **users** - 用户表
18. **user_assets** - 用户资产表

## 🚀 服务器导入步骤

### 方法一：通过宝塔面板导入

1. **登录宝塔面板**
   - 访问：http://你的服务器IP:8888

2. **上传SQL文件**
   - 进入 文件管理
   - 上传 `reelshortfund_production.sql` 到服务器

3. **创建数据库**
   - 进入 数据库 管理
   - 添加数据库：
     - 数据库名：`reelshortfund`
     - 用户名：`reelshort_user`
     - 密码：`设置强密码`

4. **导入数据**
   - 点击数据库后的 "管理" 按钮
   - 进入 phpMyAdmin
   - 点击 "导入" 选项卡
   - 选择上传的SQL文件
   - 点击 "执行"

### 方法二：通过命令行导入

```bash
# 1. 上传文件到服务器
scp docs/reelshortfund_production.sql root@你的服务器IP:/root/

# 2. 登录服务器
ssh root@你的服务器IP

# 3. 创建数据库
mysql -u root -p -e "CREATE DATABASE reelshortfund CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 4. 创建用户
mysql -u root -p -e "CREATE USER 'reelshort_user'@'localhost' IDENTIFIED BY '你的密码';"
mysql -u root -p -e "GRANT ALL PRIVILEGES ON reelshortfund.* TO 'reelshort_user'@'localhost';"
mysql -u root -p -e "FLUSH PRIVILEGES;"

# 5. 导入数据
mysql -u root -p reelshortfund < /root/reelshortfund_production.sql
```

## ✅ 导入验证

### 检查表结构
```sql
-- 查看所有表
SHOW TABLES;

-- 检查用户表结构
DESCRIBE users;

-- 检查数据量
SELECT COUNT(*) FROM users;
SELECT COUNT(*) FROM dramas;
SELECT COUNT(*) FROM actors;
```

### 测试数据连接
```bash
# 测试数据库连接
mysql -h localhost -u reelshort_user -p reelshortfund -e "SELECT COUNT(*) as total_users FROM users;"
```

## ⚠️ 注意事项

1. **字符集问题**
   - 确保数据库字符集为 utf8mb4
   - 避免中文乱码问题

2. **权限设置**
   - 生产环境用户只给必要权限
   - 不要使用root用户连接应用

3. **备份策略**
   - 导入前先备份现有数据
   - 设置定期自动备份

4. **安全考虑**
   - 使用强密码
   - 限制数据库访问IP
   - 定期更新密码

## 🔧 常见问题解决

### 问题1：字符集错误
```sql
-- 修改数据库字符集
ALTER DATABASE reelshortfund CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 问题2：导入超时
```bash
# 增加超时时间
mysql --max_allowed_packet=1024M --net_buffer_length=32K -u root -p reelshortfund < reelshortfund_production.sql
```

### 问题3：权限不足
```sql
-- 重新授权
GRANT ALL PRIVILEGES ON reelshortfund.* TO 'reelshort_user'@'localhost';
FLUSH PRIVILEGES;
```

## 📝 更新记录

- **2025-08-05**: 初始数据库导出，包含完整的表结构和测试数据
- **数据量**: 用户、短剧、演员等核心数据已包含
- **状态**: 可直接用于生产环境部署
