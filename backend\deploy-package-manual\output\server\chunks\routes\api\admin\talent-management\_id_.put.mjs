import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getR<PERSON>er<PERSON><PERSON><PERSON>, f as createError, r as readBody, q as query, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import { c as validateId } from '../../../../_/validators.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__put = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const artistId = validateId(getRouterParam(event, "id"));
    if (!artistId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u827A\u4EBAID"
      });
    }
    const body = await readBody(event);
    const {
      name,
      avatarUrl,
      bio,
      tags,
      roleType,
      sortOrder,
      status
    } = body;
    const artists = await query(
      "SELECT id, name FROM actors WHERE id = ?",
      [artistId]
    );
    if (artists.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u827A\u4EBA\u4E0D\u5B58\u5728"
      });
    }
    const currentArtist = artists[0];
    if (name && name !== currentArtist.name) {
      const conflictArtists = await query(
        "SELECT id FROM actors WHERE name = ? AND id != ?",
        [name, artistId]
      );
      if (conflictArtists.length > 0) {
        throw createError({
          statusCode: 409,
          statusMessage: "\u827A\u4EBA\u59D3\u540D\u5DF2\u88AB\u5176\u4ED6\u827A\u4EBA\u4F7F\u7528"
        });
      }
    }
    if (roleType) {
      const validRoleTypes = ["\u51FA\u54C1\u4EBA", "\u8054\u5408\u51FA\u54C1\u4EBA", "\u603B\u5236\u7247\u4EBA", "\u5236\u7247\u4EBA", "\u8054\u5408\u5236\u7247\u4EBA", "\u5BFC\u6F14", "\u76D1\u5236", "\u7F16\u5267", "\u7EDF\u7B79", "\u6F14\u5458"];
      if (!validRoleTypes.includes(roleType)) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u65E0\u6548\u7684\u8EAB\u4EFD\u7C7B\u578B"
        });
      }
    }
    const updateFields = [];
    const updateValues = [];
    if (name !== void 0) {
      updateFields.push("name = ?");
      updateValues.push(name);
    }
    if (avatarUrl !== void 0) {
      updateFields.push("avatar_url = ?");
      updateValues.push(avatarUrl || null);
    }
    if (bio !== void 0) {
      updateFields.push("bio = ?");
      updateValues.push(bio || null);
    }
    if (tags !== void 0) {
      updateFields.push("tags = ?");
      updateValues.push(tags || null);
    }
    if (roleType !== void 0) {
      updateFields.push("role_type = ?");
      updateValues.push(roleType);
    }
    if (sortOrder !== void 0) {
      updateFields.push("sort_order = ?");
      updateValues.push(sortOrder || 0);
    }
    if (status !== void 0) {
      updateFields.push("is_active = ?");
      let statusValue;
      if (typeof status === "string") {
        statusValue = status === "active" ? 1 : 0;
      } else {
        statusValue = status ? 1 : 0;
      }
      updateValues.push(statusValue);
    }
    if (updateFields.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6CA1\u6709\u63D0\u4F9B\u8981\u66F4\u65B0\u7684\u5B57\u6BB5"
      });
    }
    updateFields.push("updated_at = NOW()");
    updateValues.push(artistId);
    await query(
      `UPDATE actors SET ${updateFields.join(", ")} WHERE id = ?`,
      updateValues
    );
    logger.info("\u7BA1\u7406\u5458\u66F4\u65B0\u827A\u4EBA\u6210\u529F", {
      adminId: admin.id,
      artistId,
      updatedFields: updateFields,
      ip: getClientIP(event)
    });
    return {
      success: true,
      message: "\u827A\u4EBA\u4FE1\u606F\u66F4\u65B0\u6210\u529F"
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u827A\u4EBA\u4FE1\u606F\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      artistId: getRouterParam(event, "id"),
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { _id__put as default };
//# sourceMappingURL=_id_.put.mjs.map
