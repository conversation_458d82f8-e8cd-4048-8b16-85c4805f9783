import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, r as readBody, f as createError, q as query, m as transaction, o as logAdminAction, l as logger, e as getClientIP } from '../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const body = await readBody(event);
    const {
      title,
      summary,
      content,
      cover_image_url,
      category_id,
      author,
      source_url,
      status = "draft",
      is_featured = false,
      publish_date,
      tags = [],
      seo
    } = body;
    if (!title || typeof title !== "string" || title.trim().length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65B0\u95FB\u6807\u9898\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    if (!content || typeof content !== "string" || content.trim().length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65B0\u95FB\u5185\u5BB9\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const validStatuses = ["draft", "pending", "published", "archived"];
    if (!validStatuses.includes(status)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u65B0\u95FB\u72B6\u6001"
      });
    }
    if (category_id && !isNaN(parseInt(category_id))) {
      const categoryCheck = await query(
        "SELECT id FROM news_categories WHERE id = ? AND is_active = 1",
        [parseInt(category_id)]
      );
      if (!categoryCheck || categoryCheck.length === 0) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u65E0\u6548\u7684\u65B0\u95FB\u5206\u7C7B"
        });
      }
    }
    let publishDateTime = null;
    if (publish_date) {
      publishDateTime = new Date(publish_date);
      if (isNaN(publishDateTime.getTime())) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u65E0\u6548\u7684\u53D1\u5E03\u65F6\u95F4"
        });
      }
    } else if (status === "published") {
      publishDateTime = /* @__PURE__ */ new Date();
    }
    const newsId = await transaction(async (connection) => {
      const insertNewsQuery = `
        INSERT INTO news (
          title, summary, content, cover_image_url, category_id, 
          author, source_url, status, is_featured, publish_date,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `;
      const newsResult = await connection.execute(insertNewsQuery, [
        title.trim(),
        summary ? summary.trim() : null,
        content.trim(),
        cover_image_url || null,
        category_id ? parseInt(category_id) : null,
        author ? author.trim() : null,
        source_url || null,
        status,
        is_featured ? 1 : 0,
        publishDateTime
      ]);
      const newNewsId = newsResult.insertId;
      if (tags && Array.isArray(tags) && tags.length > 0) {
        for (const tagName of tags) {
          if (typeof tagName === "string" && tagName.trim()) {
            let tagId;
            const existingTag = await connection.execute(
              "SELECT id FROM news_tags WHERE name = ?",
              [tagName.trim()]
            );
            if (existingTag[0].length > 0) {
              tagId = existingTag[0][0].id;
            } else {
              const tagResult = await connection.execute(
                "INSERT INTO news_tags (name) VALUES (?)",
                [tagName.trim()]
              );
              tagId = tagResult.insertId;
            }
            await connection.execute(
              "INSERT INTO news_tag_relations (news_id, tag_id) VALUES (?, ?)",
              [newNewsId, tagId]
            );
          }
        }
      }
      if (seo && typeof seo === "object") {
        const {
          meta_title,
          meta_description,
          meta_keywords,
          canonical_url,
          og_title,
          og_description,
          og_image
        } = seo;
        await connection.execute(
          `INSERT INTO news_seo (
            news_id, meta_title, meta_description, meta_keywords,
            canonical_url, og_title, og_description, og_image
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            newNewsId,
            meta_title || null,
            meta_description || null,
            meta_keywords || null,
            canonical_url || null,
            og_title || null,
            og_description || null,
            og_image || null
          ]
        );
      }
      return newNewsId;
    });
    await logAdminAction(admin.id, "news:create", "\u521B\u5EFA\u65B0\u95FB", {
      newsId,
      title: title.trim(),
      status,
      categoryId: category_id
    });
    return {
      success: true,
      message: "\u65B0\u95FB\u521B\u5EFA\u6210\u529F",
      data: {
        id: newsId,
        title: title.trim(),
        status
      }
    };
  } catch (error) {
    logger.error("\u521B\u5EFA\u65B0\u95FB\u5931\u8D25", {
      error: error.message,
      stack: error.stack,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u521B\u5EFA\u65B0\u95FB\u5931\u8D25"
    });
  }
});

export { index_post as default };
//# sourceMappingURL=index.post6.mjs.map
