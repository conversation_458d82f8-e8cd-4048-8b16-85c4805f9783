import { c as defineEvent<PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, r as readBody, q as query, k as getPool, l as logger, e as getClientIP } from '../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const roles_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(adminPayload.id, PERMISSIONS.ROLE_MANAGE);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u521B\u5EFA\u89D2\u8272"
      });
    }
    const body = await readBody(event);
    const { name, code, description, permissions = [], status = 1 } = body;
    if (!name || !code) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u89D2\u8272\u540D\u79F0\u548C\u7F16\u7801\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const existingRole = await query(`
      SELECT id FROM admin_roles WHERE code = ?
    `, [code]);
    if (existingRole.length > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u89D2\u8272\u7F16\u7801\u5DF2\u5B58\u5728"
      });
    }
    const connection = await getPool().getConnection();
    await connection.beginTransaction();
    try {
      const result = await connection.execute(`
        INSERT INTO admin_roles (name, code, description, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, NOW(), NOW())
      `, [name, code, description || null, status]);
      const roleId = result[0].insertId;
      if (permissions.length > 0) {
        const permissionValues = permissions.map((permissionCode) => [roleId, permissionCode]);
        await connection.query(`
          INSERT INTO admin_role_permissions (role_id, permission_code, created_at)
          VALUES ?
        `, [permissionValues]);
      }
      await connection.commit();
      logger.info("\u521B\u5EFA\u89D2\u8272\u6210\u529F", {
        adminId: adminPayload.id,
        roleId,
        roleName: name,
        roleCode: code,
        permissionCount: permissions.length,
        ip: getClientIP(event)
      });
      return {
        success: true,
        data: {
          id: roleId,
          name,
          code,
          description,
          permissions,
          status
        }
      };
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    logger.error("\u521B\u5EFA\u89D2\u8272\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { roles_post as default };
//# sourceMappingURL=roles.post.mjs.map
