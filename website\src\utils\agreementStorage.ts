/**
 * 协议状态管理工具
 * 基于用户操作记录来判断是否默认勾选协议
 */

import axios from 'axios'
// 确保axios配置已加载
import '../utils/axiosConfig'

// 协议类型枚举
export enum AgreementType {
  RECHARGE = 'recharge-agreement',
  CROWDFUNDING = 'crowdfunding-agreement'
}

/**
 * 检查用户是否有充值记录
 * @returns Promise<boolean>
 */
export async function hasRechargeRecords(): Promise<boolean> {
  try {
    const response = await axios.get('/users/wallet/recharge/records', {
      params: { page: 1, pageSize: 1 }
    })

    if (response.data.success && response.data.data) {
      return response.data.data.records && response.data.data.records.length > 0
    }
    return false
  } catch (error) {
    console.error('检查充值记录失败:', error)
    return false
  }
}

/**
 * 检查用户是否有投资记录
 * @returns Promise<boolean>
 */
export async function hasInvestmentRecords(): Promise<boolean> {
  try {
    const response = await axios.get('/users/investments/records', {
      params: { page: 1, pageSize: 1 }
    })

    if (response.data.success && response.data.data) {
      return response.data.data.records && response.data.data.records.length > 0
    }
    return false
  } catch (error) {
    console.error('检查投资记录失败:', error)
    return false
  }
}

/**
 * 检查协议是否应该默认勾选
 * @param agreementType 协议类型
 * @returns Promise<boolean>
 */
export async function shouldDefaultCheck(agreementType: AgreementType): Promise<boolean> {
  try {
    switch (agreementType) {
      case AgreementType.RECHARGE:
        return await hasRechargeRecords()
      case AgreementType.CROWDFUNDING:
        return await hasInvestmentRecords()
      default:
        return false
    }
  } catch (error) {
    console.error('检查协议默认状态失败:', error)
    return false
  }
}

/**
 * 设置协议同意状态（保留接口兼容性，但不再使用本地存储）
 * @param agreementType 协议类型
 * @param agreed 是否同意
 */
export function setAgreementAgreed(agreementType: AgreementType, agreed: boolean): void {
  // 不再需要本地存储，保留接口兼容性
  console.log(`协议状态变更: ${agreementType} = ${agreed}`)
}

/**
 * 标记协议对应的操作已完成（保留接口兼容性，但不再使用本地存储）
 * @param agreementType 协议类型
 */
export function markAgreementCompleted(agreementType: AgreementType): void {
  // 不再需要本地存储，保留接口兼容性
  console.log(`协议操作完成: ${agreementType}`)
}
