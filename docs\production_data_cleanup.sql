-- 生产环境数据清理脚本
-- 用途：清理测试数据，保留核心业务数据
-- 执行时机：在生产环境导入数据后执行

USE mengtu;

-- 1. 清理测试用户数据（保留管理员账户）
-- 注意：请根据实际情况调整，不要删除重要的用户数据
-- DELETE FROM users WHERE email LIKE '%test%' OR email LIKE '%demo%';

-- 2. 清理测试投资记录
-- DELETE FROM user_investments WHERE created_at < '2025-01-01';

-- 3. 清理测试充值记录  
-- DELETE FROM user_asset_transactions WHERE transaction_type = 'test';

-- 4. 清理测试新闻
-- DELETE FROM news WHERE title LIKE '%测试%' OR title LIKE '%test%';

-- 5. 清理测试帖子
-- DELETE FROM posts WHERE title LIKE '%测试%' OR title LIKE '%test%';

-- 6. 重置自增ID（可选）
-- ALTER TABLE users AUTO_INCREMENT = 1;
-- ALTER TABLE news AUTO_INCREMENT = 1;
-- ALTE<PERSON> TABLE posts AUTO_INCREMENT = 1;

-- 7. 清理审计日志中的敏感信息
-- DELETE FROM audit_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 8. 清理令牌黑名单中的过期记录
DELETE FROM token_blacklist WHERE expires_at < NOW();

-- 9. 优化表（重建索引，回收空间）
OPTIMIZE TABLE users;
OPTIMIZE TABLE user_assets;
OPTIMIZE TABLE user_investments;
OPTIMIZE TABLE drama_series;
OPTIMIZE TABLE funds;
OPTIMIZE TABLE news;
OPTIMIZE TABLE actors;

-- 10. 更新统计信息
ANALYZE TABLE users;
ANALYZE TABLE user_assets;
ANALYZE TABLE user_investments;
ANALYZE TABLE drama_series;
ANALYZE TABLE funds;
ANALYZE TABLE news;

-- 11. 检查数据完整性
-- 检查用户资产表
SELECT COUNT(*) as user_count FROM users WHERE is_active = 1;
SELECT COUNT(*) as asset_count FROM user_assets;

-- 检查短剧数据
SELECT COUNT(*) as drama_count FROM drama_series WHERE status = 'active';

-- 检查基金数据  
SELECT COUNT(*) as fund_count FROM funds WHERE status = 'active';

-- 检查演员数据
SELECT COUNT(*) as actor_count FROM actors WHERE is_active = 1;

-- 12. 设置生产环境的系统配置
UPDATE system_settings SET setting_value = 'production' WHERE setting_key = 'app_env';
UPDATE system_settings SET setting_value = 'https://qinghee.com.cn' WHERE setting_key = 'app_url';
UPDATE system_settings SET setting_value = 'https://api.qinghee.com.cn' WHERE setting_key = 'api_url';

-- 13. 创建生产环境管理员账户（如果需要）
-- INSERT INTO admins (username, email, password, role, is_active, created_at) 
-- VALUES ('admin', '<EMAIL>', '$2b$10$encrypted_password_here', 'super_admin', 1, NOW());

COMMIT;
