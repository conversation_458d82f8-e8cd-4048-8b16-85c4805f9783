import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, r as readBody, f as createError, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const batchDelete_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const body = await readBody(event);
    const { ids } = body;
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u8BF7\u9009\u62E9\u8981\u5220\u9664\u7684\u6A2A\u5E45"
      });
    }
    const validIds = ids.filter((id) => !isNaN(Number(id))).map((id) => Number(id));
    if (validIds.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u6A2A\u5E45ID"
      });
    }
    const existingBanners = await query(
      `SELECT id, title FROM banners WHERE id IN (${validIds.map(() => "?").join(",")})`,
      validIds
    );
    if (existingBanners.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u672A\u627E\u5230\u8981\u5220\u9664\u7684\u6A2A\u5E45"
      });
    }
    const deleteResult = await query(
      `DELETE FROM banners WHERE id IN (${validIds.map(() => "?").join(",")})`,
      validIds
    );
    const deletedCount = deleteResult.affectedRows || 0;
    await logAuditAction({
      action: "ADMIN_BATCH_DELETE_BANNERS",
      description: `\u7BA1\u7406\u5458\u6279\u91CF\u5220\u9664\u6A2A\u5E45: ${existingBanners.map((b) => b.title).join(", ")}`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        deletedIds: validIds,
        deletedCount,
        deletedBanners: existingBanners
      }
    });
    logger.info("\u7BA1\u7406\u5458\u6279\u91CF\u5220\u9664\u6A2A\u5E45\u6210\u529F", {
      adminId: admin.id,
      adminUsername: admin.username,
      deletedIds: validIds,
      deletedCount
    });
    return {
      success: true,
      message: `\u6210\u529F\u5220\u9664 ${deletedCount} \u4E2A\u6A2A\u5E45`,
      data: {
        deletedCount,
        deletedIds: validIds
      }
    };
  } catch (error) {
    logger.error("\u6279\u91CF\u5220\u9664\u6A2A\u5E45\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { batchDelete_post as default };
//# sourceMappingURL=batch-delete.post.mjs.map
