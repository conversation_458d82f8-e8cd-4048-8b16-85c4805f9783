import { c as define<PERSON>vent<PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, q as query, n as readMultipartFormData, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../../_/nitro.mjs';
import { g as generateUniqueFilename, a as getUploadPath, d as deleteFile } from '../../../../../../_/file-utils.mjs';
import { u as uploadToCOS } from '../../../../../../_/cos-uploader.mjs';
import { writeFile } from 'node:fs/promises';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';
import 'fs';
import 'path';
import 'crypto';
import 'cos-nodejs-sdk-v5';
import 'fs/promises';

const upload_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const fundId = getRouterParam(event, "id");
    if (!fundId || isNaN(Number(fundId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u57FA\u91D1ID"
      });
    }
    const fundExists = await query(
      "SELECT id FROM funds WHERE id = ?",
      [fundId]
    );
    if (fundExists.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u57FA\u91D1\u4E0D\u5B58\u5728"
      });
    }
    const formData = await readMultipartFormData(event);
    if (!formData || formData.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u672A\u627E\u5230\u4E0A\u4F20\u6587\u4EF6"
      });
    }
    const fileData = formData.find((item) => item.name === "file");
    if (!fileData || !fileData.data || !fileData.filename) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6587\u4EF6\u6570\u636E\u65E0\u6548"
      });
    }
    const allowedTypes = [".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt", ".rtf"];
    const fileExt = "." + ((_a = fileData.filename.split(".").pop()) == null ? void 0 : _a.toLowerCase());
    if (!allowedTypes.includes(fileExt)) {
      throw createError({
        statusCode: 400,
        statusMessage: `\u4E0D\u652F\u6301\u7684\u6587\u4EF6\u7C7B\u578B\uFF0C\u8BF7\u4E0A\u4F20 ${allowedTypes.join(", ")} \u683C\u5F0F\u7684\u6587\u4EF6`
      });
    }
    const maxSize = 50 * 1024 * 1024;
    if (fileData.data.length > maxSize) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6587\u4EF6\u5927\u5C0F\u4E0D\u80FD\u8D85\u8FC750MB"
      });
    }
    const uniqueFilename = generateUniqueFilename(fileData.filename);
    const tempPath = await getUploadPath(uniqueFilename, "temp");
    await writeFile(tempPath, fileData.data);
    try {
      const destPath = `mengtutv/fund-documents/fund_${fundId}_${uniqueFilename}`;
      const uploadResult = await uploadToCOS({
        path: tempPath,
        originalname: fileData.filename,
        mimetype: fileData.type || "application/octet-stream"
      }, destPath);
      await deleteFile(tempPath);
      const fileTypeMap = {
        ".pdf": "PDF",
        ".doc": "DOC",
        ".docx": "DOCX",
        ".xls": "XLS",
        ".xlsx": "XLSX",
        ".ppt": "PPT",
        ".pptx": "PPTX",
        ".txt": "TXT",
        ".rtf": "RTF"
      };
      const formatFileSize = (bytes) => {
        if (bytes === 0) return "0 B";
        const k = 1024;
        const sizes = ["B", "KB", "MB", "GB"];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
      };
      const documentName = fileData.filename.replace(/\.[^/.]+$/, "");
      await logAuditAction({
        action: "ADMIN_UPLOAD_FUND_DOCUMENT",
        description: `\u7BA1\u7406\u5458\u4E0A\u4F20\u57FA\u91D1\u6587\u6863: \u57FA\u91D1ID=${fundId}`,
        userId: adminPayload.id,
        username: adminPayload.username,
        ip: getClientIP(event),
        userAgent: getHeader(event, "user-agent") || "",
        metadata: {
          fundId: Number(fundId),
          originalName: fileData.filename,
          fileName: `fund_${fundId}_${uniqueFilename}`,
          fileSize: fileData.data.length,
          fileType: fileTypeMap[fileExt] || fileExt.toUpperCase(),
          url: uploadResult.url
        }
      });
      logger.info("\u57FA\u91D1\u6587\u6863\u4E0A\u4F20\u6210\u529F", {
        adminId: adminPayload.id,
        adminUsername: adminPayload.username,
        fundId: Number(fundId),
        originalName: fileData.filename,
        fileName: `fund_${fundId}_${uniqueFilename}`,
        fileSize: fileData.data.length,
        url: uploadResult.url
      });
      return {
        success: true,
        message: "\u57FA\u91D1\u6587\u6863\u4E0A\u4F20\u6210\u529F",
        data: {
          name: documentName,
          fileUrl: uploadResult.url,
          fileType: fileTypeMap[fileExt] || fileExt.toUpperCase(),
          fileSize: formatFileSize(fileData.data.length),
          originalName: fileData.filename,
          filename: `fund_${fundId}_${uniqueFilename}`,
          size: fileData.data.length
        }
      };
    } catch (uploadError) {
      await deleteFile(tempPath);
      logger.error("\u57FA\u91D1\u6587\u6863\u4E0A\u4F20\u5230COS\u5931\u8D25", {
        error: uploadError.message,
        adminId: adminPayload.id,
        fundId: Number(fundId),
        filename: fileData.filename
      });
      throw createError({
        statusCode: 500,
        statusMessage: `\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25: ${uploadError.message}`
      });
    }
  } catch (error) {
    logger.error("\u57FA\u91D1\u6587\u6863\u4E0A\u4F20\u5931\u8D25", {
      error: error.message,
      fundId: getRouterParam(event, "id")
    });
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "\u57FA\u91D1\u6587\u6863\u4E0A\u4F20\u5931\u8D25"
    });
  }
});

export { upload_post as default };
//# sourceMappingURL=upload.post.mjs.map
