#!/bin/bash

echo "========================================"
echo "剧投投后台管理系统部署脚本"
echo "========================================"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用root用户运行此脚本"
    exit 1
fi

# 备份现有部署
echo ""
echo "[1/6] 备份现有部署..."
cd /www/wwwroot
if [ -d "admin.qinghee.com.cn" ]; then
    mv admin.qinghee.com.cn admin.qinghee.com.cn.backup.$(date +%Y%m%d_%H%M%S)
fi

# 创建新部署目录
echo ""
echo "[2/6] 创建新部署目录..."
mkdir -p admin.qinghee.com.cn
cd admin.qinghee.com.cn

# 检查部署包
echo ""
echo "[3/6] 检查部署包..."
if [ ! -f "fundAdmin-deploy.tar.gz" ]; then
    echo "错误: 找不到部署包 fundAdmin-deploy.tar.gz"
    echo "请先上传部署包到 /www/wwwroot/admin.qinghee.com.cn/ 目录"
    exit 1
fi

# 解压部署包
echo ""
echo "[4/6] 解压部署包..."
tar -xzf fundAdmin-deploy.tar.gz

# 设置权限
echo ""
echo "[5/6] 设置权限..."
chown -R www:www /www/wwwroot/admin.qinghee.com.cn
chmod -R 755 /www/wwwroot/admin.qinghee.com.cn

# 配置Nginx（如果使用宝塔面板，需要手动配置）
echo ""
echo "[6/6] Nginx配置提醒..."
echo "请在宝塔面板中："
echo "1. 添加站点: admin.qinghee.com.cn"
echo "2. 设置网站目录: /www/wwwroot/admin.qinghee.com.cn"
echo "3. 配置SSL证书（推荐）"
echo "4. 设置伪静态规则（Vue Router支持）"

echo ""
echo "伪静态规则："
echo "location / {"
echo "    try_files \$uri \$uri/ /index.html;"
echo "}"

echo ""
echo "========================================"
echo "部署完成！"
echo "========================================"
echo ""
echo "访问地址: http://admin.qinghee.com.cn"
echo "HTTPS地址: https://admin.qinghee.com.cn (配置SSL后)"
echo ""
echo "注意事项："
echo "1. 确保域名已解析到服务器IP"
echo "2. 在宝塔面板中配置虚拟主机"
echo "3. 配置SSL证书提高安全性"
echo "4. 检查防火墙80/443端口开放"
echo ""
