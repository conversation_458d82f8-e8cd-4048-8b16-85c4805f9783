import { c as defineEvent<PERSON>and<PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, q as query, r as readBody, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _feeId__put = defineEventHandler(async (event) => {
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const fundId = getRouterParam(event, "id");
    const feeId = getRouterParam(event, "feeId");
    if (!fundId || isNaN(Number(fundId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u57FA\u91D1ID"
      });
    }
    if (!feeId || isNaN(Number(feeId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u8D39\u7528ID"
      });
    }
    const existingFee = await query(
      "SELECT * FROM fund_fees WHERE id = ? AND fund_id = ?",
      [feeId, fundId]
    );
    if (existingFee.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u8D39\u7528\u4E0D\u5B58\u5728"
      });
    }
    const body = await readBody(event);
    const { name, value, sortOrder } = body;
    if (!name || !name.trim() || !value || !value.trim()) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u8D39\u7528\u540D\u79F0\u548C\u8D39\u7528\u503C\u4E3A\u5FC5\u586B\u9879"
      });
    }
    await query(
      "UPDATE fund_fees SET name = ?, value = ?, sort_order = ? WHERE id = ? AND fund_id = ?",
      [name.trim(), value.trim(), Number(sortOrder) || 1, feeId, fundId]
    );
    await logAuditAction({
      action: "ADMIN_UPDATE_FUND_FEE",
      description: `\u7BA1\u7406\u5458\u66F4\u65B0\u57FA\u91D1\u8D39\u7528: \u57FA\u91D1ID=${fundId}, \u8D39\u7528ID=${feeId}`,
      userId: adminPayload.id,
      username: adminPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      metadata: {
        fundId: Number(fundId),
        feeId: Number(feeId),
        oldName: existingFee[0].name,
        newName: name.trim(),
        oldValue: existingFee[0].value,
        newValue: value.trim(),
        sortOrder: Number(sortOrder) || 1
      }
    });
    logger.info("\u57FA\u91D1\u8D39\u7528\u66F4\u65B0\u6210\u529F", {
      adminId: adminPayload.id,
      adminUsername: adminPayload.username,
      fundId: Number(fundId),
      feeId: Number(feeId),
      name: name.trim()
    });
    return {
      success: true,
      message: "\u57FA\u91D1\u8D39\u7528\u66F4\u65B0\u6210\u529F",
      data: {
        id: Number(feeId),
        fundId: Number(fundId),
        name: name.trim(),
        value: value.trim(),
        sortOrder: Number(sortOrder) || 1,
        updatedAt: (/* @__PURE__ */ new Date()).toISOString()
      }
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u57FA\u91D1\u8D39\u7528\u5931\u8D25", {
      error: error.message,
      fundId: getRouterParam(event, "id"),
      feeId: getRouterParam(event, "feeId")
    });
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "\u66F4\u65B0\u57FA\u91D1\u8D39\u7528\u5931\u8D25"
    });
  }
});

export { _feeId__put as default };
//# sourceMappingURL=_feeId_.put.mjs.map
