import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, r as readBody, q as query, l as logger, e as getClientIP } from '../../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const status_patch = defineEventHandler(async (event) => {
  var _a;
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(adminPayload.id, PERMISSIONS.ROLE_MANAGE);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u66F4\u65B0\u89D2\u8272\u72B6\u6001"
      });
    }
    const roleId = getRouterParam(event, "id");
    if (!roleId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u89D2\u8272ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const body = await readBody(event);
    const { status } = body;
    if (status !== 0 && status !== 1) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u72B6\u6001\u503C\u65E0\u6548"
      });
    }
    const existingRole = await query(`
      SELECT id, name, code, status FROM admin_roles WHERE id = ?
    `, [roleId]);
    if (existingRole.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u89D2\u8272\u4E0D\u5B58\u5728"
      });
    }
    const role = existingRole[0];
    if (role.code === "super" && status === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u8D85\u7EA7\u7BA1\u7406\u5458\u89D2\u8272\u4E0D\u5141\u8BB8\u7981\u7528"
      });
    }
    await query(`
      UPDATE admin_roles 
      SET status = ?, updated_at = NOW()
      WHERE id = ?
    `, [status, roleId]);
    logger.info("\u66F4\u65B0\u89D2\u8272\u72B6\u6001\u6210\u529F", {
      adminId: adminPayload.id,
      roleId,
      roleName: role.name,
      oldStatus: role.status,
      newStatus: status,
      ip: getClientIP(event)
    });
    return {
      success: true,
      data: {
        id: parseInt(roleId),
        status
      }
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u89D2\u8272\u72B6\u6001\u5931\u8D25", {
      error: error.message,
      roleId: getRouterParam(event, "id"),
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { status_patch as default };
//# sourceMappingURL=status.patch.mjs.map
