import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, f as createError, q as query } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__delete = defineEventHandler(async (event) => {
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const platformId = getRouterParam(event, "id");
    if (!platformId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5E73\u53F0ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const existingPlatform = await query(
      "SELECT id, platform_name FROM drama_platforms WHERE id = ?",
      [platformId]
    );
    if (existingPlatform.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u5E73\u53F0\u4E0D\u5B58\u5728"
      });
    }
    const platform = existingPlatform[0];
    await query("DELETE FROM drama_platforms WHERE id = ?", [platformId]);
    return {
      success: true,
      message: "\u5E73\u53F0\u5220\u9664\u6210\u529F",
      data: {
        id: platformId,
        platform_name: platform.platform_name
      }
    };
  } catch (error) {
    console.error("\u5220\u9664\u5E73\u53F0\u5931\u8D25:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u5220\u9664\u5E73\u53F0\u5931\u8D25: " + error.message
    });
  }
});

export { _id__delete as default };
//# sourceMappingURL=_id_.delete.mjs.map
