#!/usr/bin/env node

/**
 * 生产环境配置检查脚本
 * 用于验证生产环境配置是否正确
 */

const fs = require('fs');
const path = require('path');

// 颜色定义
const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m'
};

// 日志函数
const log = {
    info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
    success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
    warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
    error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`)
};

// 必需的环境变量配置
const requiredConfig = {
    // 数据库配置
    'DB_HOST': { required: true, description: '数据库主机地址' },
    'DB_PORT': { required: true, description: '数据库端口' },
    'DB_USER': { required: true, description: '数据库用户名' },
    'DB_PASSWORD': { required: true, description: '数据库密码', sensitive: true },
    'DB_NAME': { required: true, description: '数据库名称' },
    
    // JWT配置
    'JWT_SECRET': { required: true, description: 'JWT密钥', sensitive: true },
    'JWT_EXPIRES_IN': { required: true, description: 'JWT过期时间' },
    'JWT_ADMIN_EXPIRES_IN': { required: true, description: '管理员JWT过期时间' },
    'ACCESS_TOKEN_SECRET': { required: true, description: '访问令牌密钥', sensitive: true },
    'REFRESH_TOKEN_SECRET': { required: true, description: '刷新令牌密钥', sensitive: true },
    
    // 应用配置
    'NODE_ENV': { required: true, description: '运行环境', expectedValue: 'production' },
    'PORT': { required: true, description: '服务端口' },
    'APP_URL': { required: true, description: '应用URL' },
    'API_URL': { required: true, description: 'API URL' },
    
    // 文件上传配置
    'UPLOAD_DIR': { required: true, description: '上传目录' },
    'MAX_FILE_SIZE': { required: true, description: '最大文件大小' },
    'ALLOWED_FILE_TYPES': { required: true, description: '允许的文件类型' },
    
    // 云存储配置
    'OSS_PROVIDER': { required: true, description: '云存储提供商' },
    'COS_SECRET_ID': { required: false, description: '腾讯云COS密钥ID', sensitive: true },
    'COS_SECRET_KEY': { required: false, description: '腾讯云COS密钥', sensitive: true },
    'COS_REGION': { required: false, description: '腾讯云COS区域' },
    'COS_BUCKET': { required: false, description: '腾讯云COS存储桶' },
    'COS_DIRECTORY': { required: false, description: '腾讯云COS目录' },
    
    // 邮件配置
    'SMTP_HOST': { required: false, description: 'SMTP主机' },
    'SMTP_PORT': { required: false, description: 'SMTP端口' },
    'SMTP_USER': { required: false, description: 'SMTP用户名' },
    'SMTP_PASS': { required: false, description: 'SMTP密码', sensitive: true },
    'SMTP_FROM': { required: false, description: 'SMTP发件人' }
};

// 读取环境变量文件
function loadEnvFile(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const env = {};
        
        content.split('\n').forEach(line => {
            line = line.trim();
            if (line && !line.startsWith('#')) {
                const [key, ...valueParts] = line.split('=');
                if (key && valueParts.length > 0) {
                    env[key.trim()] = valueParts.join('=').trim();
                }
            }
        });
        
        return env;
    } catch (error) {
        log.error(`无法读取环境变量文件: ${filePath}`);
        log.error(error.message);
        return null;
    }
}

// 检查配置项
function checkConfig(env) {
    let hasErrors = false;
    let hasWarnings = false;
    
    log.info('开始检查生产环境配置...\n');
    
    // 检查必需配置
    Object.entries(requiredConfig).forEach(([key, config]) => {
        const value = env[key];
        const displayValue = config.sensitive && value ? '***' : value;
        
        if (config.required && (!value || value === 'your_production_password_here' || value === 'your_cos_secret_id')) {
            log.error(`${key}: 缺失或使用默认值 (${config.description})`);
            hasErrors = true;
        } else if (config.required && value) {
            if (config.expectedValue && value !== config.expectedValue) {
                log.warning(`${key}: ${displayValue} (期望值: ${config.expectedValue})`);
                hasWarnings = true;
            } else {
                log.success(`${key}: ${displayValue}`);
            }
        } else if (!config.required && value) {
            log.info(`${key}: ${displayValue} (可选)`);
        } else if (!config.required) {
            log.warning(`${key}: 未配置 (${config.description}) - 可选`);
        }
    });
    
    console.log('\n' + '='.repeat(50));
    
    // 安全检查
    log.info('进行安全检查...');
    
    // 检查密钥强度
    const secrets = ['JWT_SECRET', 'ACCESS_TOKEN_SECRET', 'REFRESH_TOKEN_SECRET'];
    secrets.forEach(secret => {
        const value = env[secret];
        if (value && value.length < 32) {
            log.warning(`${secret}: 密钥长度过短，建议至少32个字符`);
            hasWarnings = true;
        }
    });
    
    // 检查数据库配置
    if (env.DB_HOST === 'localhost' || env.DB_HOST === '127.0.0.1') {
        log.info('数据库配置: 使用本地数据库');
    }
    
    // 检查云存储配置
    if (env.OSS_PROVIDER === 'cos') {
        const cosRequired = ['COS_SECRET_ID', 'COS_SECRET_KEY', 'COS_REGION', 'COS_BUCKET'];
        const missingCos = cosRequired.filter(key => !env[key] || env[key].startsWith('your_'));
        if (missingCos.length > 0) {
            log.warning(`腾讯云COS配置不完整，缺少: ${missingCos.join(', ')}`);
            hasWarnings = true;
        }
    }
    
    // 检查URL配置
    if (env.APP_URL && !env.APP_URL.startsWith('https://')) {
        log.warning('APP_URL: 生产环境建议使用HTTPS');
        hasWarnings = true;
    }
    
    if (env.API_URL && !env.API_URL.startsWith('https://')) {
        log.warning('API_URL: 生产环境建议使用HTTPS');
        hasWarnings = true;
    }
    
    console.log('\n' + '='.repeat(50));
    
    // 总结
    if (hasErrors) {
        log.error('配置检查失败！请修复上述错误后重试。');
        return false;
    } else if (hasWarnings) {
        log.warning('配置检查通过，但有一些建议需要注意。');
        return true;
    } else {
        log.success('配置检查通过！所有必需配置都已正确设置。');
        return true;
    }
}

// 生成配置模板
function generateTemplate() {
    log.info('生成生产环境配置模板...');
    
    const template = `# 剧投投后端服务 - 生产环境配置模板
# 请根据实际情况修改以下配置

# 服务器配置
PORT=3001
NODE_ENV=production

# 数据库配置 - 请修改为实际数据库信息
DB_HOST=localhost
DB_PORT=3306
DB_USER=mengtu_user
DB_PASSWORD=your_secure_database_password
DB_NAME=mengtu

# JWT配置 - 请生成强密钥
JWT_SECRET=your_jwt_secret_at_least_32_characters_long
JWT_EXPIRES_IN=24h
JWT_ADMIN_EXPIRES_IN=12h
ACCESS_TOKEN_SECRET=your_access_token_secret_at_least_32_characters
REFRESH_TOKEN_SECRET=your_refresh_token_secret_at_least_32_characters

# CORS配置
CORS_ALLOWED_ORIGINS=https://www.qinghee.com.cn,https://qinghee.com.cn,https://admin.qinghee.com.cn

# 文件上传配置
UPLOAD_DIR=/www/wwwroot/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# 应用配置
APP_URL=https://www.qinghee.com.cn
API_URL=https://api.qinghee.com.cn/api

# 云存储配置
OSS_PROVIDER=cos
COS_SECRET_ID=your_cos_secret_id
COS_SECRET_KEY=your_cos_secret_key
COS_REGION=ap-shanghai
COS_BUCKET=mengtu-production
COS_DIRECTORY=mengtutv

# 邮件配置（可选）
SMTP_HOST=smtp.qinghee.com.cn
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_smtp_password
SMTP_FROM=剧投投 <<EMAIL>>

# 日志配置
LOG_LEVEL=warn
LOG_DIR=/www/wwwroot/logs
`;
    
    fs.writeFileSync('.env.production.template', template);
    log.success('配置模板已生成: .env.production.template');
}

// 主函数
function main() {
    const args = process.argv.slice(2);
    const command = args[0] || 'check';
    
    if (command === 'template') {
        generateTemplate();
        return;
    }
    
    const envFile = '.env.production';
    
    if (!fs.existsSync(envFile)) {
        log.error(`生产环境配置文件不存在: ${envFile}`);
        log.info('使用以下命令生成配置模板:');
        log.info('node check-production-config.js template');
        process.exit(1);
    }
    
    const env = loadEnvFile(envFile);
    if (!env) {
        process.exit(1);
    }
    
    const isValid = checkConfig(env);
    
    if (!isValid) {
        process.exit(1);
    }
}

// 显示帮助信息
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log(`
剧投投生产环境配置检查工具

使用方法:
  node check-production-config.js [命令]

命令:
  check     检查生产环境配置 (默认)
  template  生成配置模板

示例:
  node check-production-config.js
  node check-production-config.js check
  node check-production-config.js template
`);
    process.exit(0);
}

main();
