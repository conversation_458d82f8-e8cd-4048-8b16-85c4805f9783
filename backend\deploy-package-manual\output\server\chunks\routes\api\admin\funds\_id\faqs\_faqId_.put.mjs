import { c as defineEvent<PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, r as readBody, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _faqId__put = defineEventHandler(async (event) => {
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const fundId = getRouterParam(event, "id");
    const faqId = getRouterParam(event, "faqId");
    if (!fundId || isNaN(Number(fundId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u57FA\u91D1ID"
      });
    }
    if (!faqId || isNaN(Number(faqId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684FAQ ID"
      });
    }
    const body = await readBody(event);
    const { question, answer, sortOrder } = body;
    if (!question || question.trim() === "") {
      throw createError({
        statusCode: 400,
        statusMessage: "\u95EE\u9898\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    if (!answer || answer.trim() === "") {
      throw createError({
        statusCode: 400,
        statusMessage: "\u7B54\u6848\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    if (!sortOrder || isNaN(Number(sortOrder))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6392\u5E8F\u53F7\u5FC5\u987B\u662F\u6709\u6548\u6570\u5B57"
      });
    }
    const existingFaq = await query(
      "SELECT id, fund_id, question FROM fund_faqs WHERE id = ? AND fund_id = ?",
      [faqId, fundId]
    );
    if (existingFaq.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u57FA\u91D1FAQ\u4E0D\u5B58\u5728"
      });
    }
    await query(
      `UPDATE fund_faqs
       SET question = ?, answer = ?, sort_order = ?
       WHERE id = ? AND fund_id = ?`,
      [question.trim(), answer.trim(), Number(sortOrder), faqId, fundId]
    );
    await logAuditAction({
      action: "ADMIN_UPDATE_FUND_FAQ",
      description: `\u7BA1\u7406\u5458\u66F4\u65B0\u57FA\u91D1FAQ: \u57FA\u91D1ID=${fundId}, FAQ ID=${faqId}`,
      userId: adminPayload.id,
      username: adminPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      metadata: {
        fundId: Number(fundId),
        faqId: Number(faqId),
        oldQuestion: existingFaq[0].question,
        newQuestion: question.trim(),
        sortOrder: Number(sortOrder)
      }
    });
    return {
      success: true,
      message: "\u57FA\u91D1FAQ\u66F4\u65B0\u6210\u529F",
      data: {
        id: Number(faqId),
        fundId: Number(fundId),
        question: question.trim(),
        answer: answer.trim(),
        sortOrder: Number(sortOrder),
        updatedAt: (/* @__PURE__ */ new Date()).toISOString()
      }
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u57FA\u91D1FAQ\u5931\u8D25", {
      error: error.message,
      fundId: getRouterParam(event, "id"),
      faqId: getRouterParam(event, "faqId")
    });
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "\u66F4\u65B0\u57FA\u91D1FAQ\u5931\u8D25"
    });
  }
});

export { _faqId__put as default };
//# sourceMappingURL=_faqId_.put.mjs.map
