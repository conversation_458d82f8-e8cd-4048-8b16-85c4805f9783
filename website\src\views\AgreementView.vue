<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- 返回按钮 -->
      <div class="mb-6">
        <button 
          @click="goBack"
          class="inline-flex items-center text-primary hover:text-primary-dark transition-colors"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
          返回
        </button>
      </div>

      <!-- 协议内容 -->
      <div class="bg-white rounded-lg shadow-sm p-8">
        <!-- 加载状态 -->
        <div v-if="loading" class="text-center py-12">
          <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <p class="mt-4 text-gray-600">正在加载协议内容...</p>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="text-center py-12">
          <div class="text-red-500 mb-4">
            <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">加载失败</h3>
          <p class="text-gray-600 mb-4">{{ error }}</p>
          <button 
            @click="loadAgreement"
            class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
          >
            重试
          </button>
        </div>

        <!-- 协议内容 -->
        <div v-else-if="agreement" class="prose prose-lg max-w-none">
          <h1 class="text-3xl font-bold text-gray-900 mb-8">{{ agreement.title }}</h1>
          <div 
            class="text-gray-700 leading-relaxed"
            v-html="agreement.content"
          ></div>
          
          <!-- 更新时间 -->
          <div class="mt-8 pt-6 border-t border-gray-200 text-sm text-gray-500">
            最后更新时间：{{ formatDate(agreement.updated_at) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getUserAgreement, getPrivacyPolicy, type Agreement } from '../api/agreement'

const route = useRoute()
const router = useRouter()

// 状态管理
const loading = ref(true)
const error = ref<string | null>(null)
const agreement = ref<Agreement | null>(null)

// 获取协议类型
const agreementType = route.params.type as string

// 加载协议内容
const loadAgreement = async () => {
  loading.value = true
  error.value = null
  
  try {
    let response
    
    if (agreementType === 'user-agreement') {
      response = await getUserAgreement()
    } else if (agreementType === 'privacy-policy') {
      response = await getPrivacyPolicy()
    } else {
      throw new Error('无效的协议类型')
    }

    if (response.data.success && response.data.data) {
      agreement.value = response.data.data
    } else {
      throw new Error('协议内容不存在')
    }
  } catch (err: any) {
    console.error('加载协议失败:', err)
    error.value = err.message || '加载协议内容失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// 组件挂载时加载协议
onMounted(() => {
  loadAgreement()
})
</script>

<style scoped>
/* 协议内容样式 */
.prose {
  color: #374151;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: #111827;
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.prose h1 {
  font-size: 2rem;
  line-height: 2.5rem;
}

.prose h2 {
  font-size: 1.5rem;
  line-height: 2rem;
}

.prose h3 {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.prose p {
  margin-bottom: 1rem;
}

.prose ul,
.prose ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.5rem;
}

.prose strong {
  font-weight: 600;
  color: #111827;
}

.prose a {
  color: #8B5CF6;
  text-decoration: underline;
}

.prose a:hover {
  color: #7C3AED;
}
</style>
