import { promises } from 'fs';
import path from 'path';
import require$$1 from 'crypto';
import { l as logger } from './nitro.mjs';

function generateUniqueFilename(originalName) {
  const timestamp = Date.now();
  const randomStr = require$$1.randomBytes(8).toString("hex");
  const ext = path.extname(originalName);
  const baseName = path.basename(originalName, ext);
  return `${baseName}_${timestamp}_${randomStr}${ext}`;
}
async function ensureDirectoryExists(dirPath) {
  try {
    await promises.access(dirPath);
  } catch (error) {
    await promises.mkdir(dirPath, { recursive: true });
    logger.info(`\u521B\u5EFA\u76EE\u5F55: ${dirPath}`);
  }
}
async function getUploadPath(filename, subdir = "") {
  const uploadDir = path.join(process.cwd(), "uploads");
  const targetDir = subdir ? path.join(uploadDir, subdir) : uploadDir;
  await ensureDirectoryExists(targetDir);
  return path.join(targetDir, filename);
}
async function deleteFile(filePath) {
  try {
    await promises.access(filePath);
    await promises.unlink(filePath);
    logger.info(`\u6210\u529F\u5220\u9664\u6587\u4EF6: ${filePath}`);
    return true;
  } catch (error) {
    if (error.code === "ENOENT") {
      logger.warn(`\u8981\u5220\u9664\u7684\u6587\u4EF6\u4E0D\u5B58\u5728: ${filePath}`);
    } else {
      logger.error(`\u5220\u9664\u6587\u4EF6\u5931\u8D25: ${filePath}`, { error: error.message });
    }
    return false;
  }
}
function getFileExtension(filename) {
  return path.extname(filename).toLowerCase();
}
function validateFileType(filename, allowedTypes) {
  const ext = getFileExtension(filename);
  return allowedTypes.includes(ext);
}
function formatFileSize(bytes) {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

export { getUploadPath as a, getFileExtension as b, deleteFile as d, formatFileSize as f, generateUniqueFilename as g, validateFileType as v };
//# sourceMappingURL=file-utils.mjs.map
