# 短剧编辑上传功能集成指南

## 🎯 修复内容总结

### 1. 封面图片上传修复

**问题**: 封面上传没有正确调用COS上传API
**解决方案**: 
- 使用专门的 `/admin/upload/drama-cover` API
- 添加完整的文件类型验证
- 正确处理上传响应和状态更新

```typescript
// 修复后的封面上传函数
const handleCoverUpload = async ({ file }: { file: File }) => {
  const uploadFormData = new FormData();
  uploadFormData.append('cover', file);
  
  const response = await requestClient.post('/admin/upload/drama-cover', uploadFormData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
  
  if (response && response.url) {
    formData.cover = response.url;
    hasDataChanged.value = true;
    emit('dataChanged', hasDataChanged.value);
  }
};
```

### 2. 项目素材上传修复

**问题**: 素材上传没有正确设置缩略图和状态
**解决方案**:
- 图片素材自动设置为缩略图
- 正确更新数据变化状态
- 改进文件类型验证

```typescript
// 修复后的素材上传函数
const handleFileUpload = async ({ file }: { file: File }) => {
  const uploadFormData = new FormData();
  uploadFormData.append('file', file);
  uploadFormData.append('type', materialForm.type);
  uploadFormData.append('dramaId', props.drama.id.toString());
  uploadFormData.append('title', materialForm.title || `${materialForm.type === 'image' ? '图片' : '视频'}素材`);
  
  const response = await requestClient.post('/admin/upload/drama-material', uploadFormData);
  
  if (response && response.url) {
    materialForm.url = response.url;
    // 如果是图片，自动设置为缩略图
    if (materialForm.type === 'image') {
      materialForm.thumbnail = response.url;
    }
    hasDataChanged.value = true;
    emit('dataChanged', hasDataChanged.value);
  }
};
```

### 3. 文件验证改进

**问题**: 文件类型验证不够严格
**解决方案**: 基于文件扩展名进行严格验证

```typescript
// 改进的文件验证
const beforeUpload = (file: File) => {
  let allowedTypes: string[];
  let maxSize: number;
  
  if (materialForm.type === 'image') {
    allowedTypes = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
    maxSize = 10 * 1024 * 1024; // 10MB
  } else {
    allowedTypes = ['.mp4', '.webm', '.ogg', '.mov', '.avi'];
    maxSize = 100 * 1024 * 1024; // 100MB
  }
  
  const fileExt = '.' + file.name.split('.').pop()?.toLowerCase();
  if (!allowedTypes.includes(fileExt)) {
    message.error(`不支持的文件类型，请选择 ${allowedTypes.join(', ')} 格式的文件`);
    return false;
  }
  
  if (file.size > maxSize) {
    const sizeText = materialForm.type === 'image' ? '10MB' : '100MB';
    message.error(`文件大小不能超过 ${sizeText}！`);
    return false;
  }
  
  return false; // 使用自定义上传
};
```

### 4. 删除确认功能

**问题**: 用户反馈删除没有二次确认
**现状**: 代码中已经有 `Modal.confirm` 确认对话框

```typescript
// 删除确认功能（已存在）
async function handleDeleteMaterial(material: DramaManagementApi.DramaMaterial) {
  try {
    await Modal.confirm({
      title: '确认删除',
      content: `确定要删除素材 "${material.title || '未命名素材'}" 吗？`,
      okText: '确定',
      cancelText: '取消',
    });
    
    await deleteMaterial(props.drama.id, material.id);
    message.success('删除成功');
    hasDataChanged.value = true;
    emit('saveSuccess');
  } catch (error: any) {
    if (error.message !== 'User canceled') {
      message.error(error.message || '删除失败');
    }
  }
}
```

## 🔧 COS存储桶路径

上传的文件会自动保存到以下COS路径：

- **短剧封面**: `mengtutv/drama-covers/`
- **图片素材**: `mengtutv/drama-images/`
- **视频素材**: `mengtutv/drama-videos/`

## 🚀 测试步骤

### 1. 测试封面上传
1. 进入短剧编辑页面
2. 在"基本信息"选项卡中
3. 点击"上传新封面"按钮
4. 选择图片文件（支持 .jpg, .jpeg, .png, .gif, .webp）
5. 验证上传成功后URL自动填入输入框
6. 验证图片预览正常显示

### 2. 测试素材上传
1. 在"项目素材"选项卡中
2. 点击"添加素材"按钮
3. 选择素材类型（图片/视频）
4. 点击对应的上传按钮
5. 验证上传成功后URL自动填入
6. 对于图片素材，验证缩略图自动设置

### 3. 测试删除确认
1. 在素材列表中点击删除按钮
2. 验证弹出确认对话框
3. 点击"取消"验证不删除
4. 点击"确定"验证删除成功

## 🐛 故障排除

### 上传失败常见原因
1. **对象存储未配置**: 检查系统设置中的COS配置
2. **文件格式不支持**: 检查文件扩展名是否在允许列表中
3. **文件大小超限**: 图片最大10MB，视频最大100MB
4. **网络问题**: 检查网络连接和后端服务状态

### 删除确认不显示
1. 检查浏览器控制台是否有JavaScript错误
2. 确认Modal组件已正确导入
3. 检查删除按钮的点击事件绑定

### 图片不显示
1. 检查COS存储桶的访问权限
2. 验证返回的URL是否正确
3. 检查图片文件是否成功上传到COS

## 📝 开发注意事项

1. **变量命名**: 避免与现有变量冲突（如使用 `uploadFormData` 而不是 `formData`）
2. **状态管理**: 上传成功后要更新 `hasDataChanged` 状态
3. **事件发射**: 通过 `emit('dataChanged')` 通知父组件数据变化
4. **错误处理**: 提供详细的错误信息给用户
5. **进度显示**: 使用进度条提升用户体验

## 🎉 预期效果

修复后的上传功能应该：
- ✅ 文件正确上传到COS存储桶
- ✅ URL自动填入表单字段
- ✅ 图片预览正常显示
- ✅ 删除操作有确认对话框
- ✅ 文件类型和大小验证严格
- ✅ 上传进度和状态反馈清晰
