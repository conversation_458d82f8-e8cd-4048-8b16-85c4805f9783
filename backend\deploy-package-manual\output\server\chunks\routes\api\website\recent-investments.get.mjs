import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, g as getQuery, q as query } from '../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

function maskUsername(username) {
  if (!username || username.length === 0) {
    return "\u533F\u540D\u7528\u6237";
  }
  if (username.length === 1) {
    return username + "*";
  }
  if (username.length === 2) {
    return username.charAt(0) + "*";
  }
  return username.charAt(0) + "*".repeat(username.length - 1);
}
function getRelativeTime(createdAt) {
  const now = /* @__PURE__ */ new Date();
  const diffMs = now.getTime() - createdAt.getTime();
  const diffMinutes = Math.floor(diffMs / (1e3 * 60));
  if (diffMinutes < 1) {
    return "\u521A\u521A";
  } else if (diffMinutes < 60) {
    return `${diffMinutes}\u5206\u949F\u524D`;
  } else if (diffMinutes < 1440) {
    const diffHours = Math.floor(diffMinutes / 60);
    return `${diffHours}\u5C0F\u65F6\u524D`;
  } else {
    const diffDays = Math.floor(diffMinutes / 1440);
    if (diffDays === 1) {
      return "\u6628\u5929";
    } else if (diffDays <= 7) {
      return `${diffDays}\u5929\u524D`;
    } else {
      return "\u4E00\u5468\u524D";
    }
  }
}
const recentInvestments_get = defineEventHandler(async (event) => {
  try {
    const queryParams = getQuery(event);
    const limit = parseInt(queryParams.limit) || 30;
    const investmentQuery = `
      SELECT 
        t.amount,
        t.description,
        t.created_at,
        u.username
      FROM user_asset_transactions t
      INNER JOIN users u ON t.user_id = u.id
      WHERE t.transaction_type = 'shells_out' 
        AND t.related_type = 'investment'
        AND t.status = 'completed'
      ORDER BY t.created_at DESC
      LIMIT ?
    `;
    const investmentRecords = await query(investmentQuery, [limit]);
    const processedRecords = investmentRecords.map((record) => ({
      investor: maskUsername(record.username),
      time: getRelativeTime(new Date(record.created_at)),
      amount: parseFloat(record.amount),
      description: record.description,
      timestamp: record.created_at
    }));
    return {
      success: true,
      data: {
        records: processedRecords,
        total: processedRecords.length,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      },
      message: "\u83B7\u53D6\u6295\u8D44\u8BB0\u5F55\u6210\u529F"
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u6295\u8D44\u8BB0\u5F55\u5931\u8D25:", error);
    return {
      success: false,
      message: "\u83B7\u53D6\u6295\u8D44\u8BB0\u5F55\u5931\u8D25",
      data: {
        records: [],
        total: 0,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      },
      error: error.message
    };
  }
});

export { recentInvestments_get as default };
//# sourceMappingURL=recent-investments.get.mjs.map
