import { c as defineEvent<PERSON>and<PERSON>, n as readMultipartFormData, f as createError, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import { b as getFileExtension, v as validateFileType, f as formatFileSize, g as generateUniqueFilename, a as getUploadPath, d as deleteFile } from '../../../../_/file-utils.mjs';
import { u as uploadToCOS } from '../../../../_/cos-uploader.mjs';
import { writeFile } from 'fs/promises';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';
import 'fs';
import 'path';
import 'crypto';
import 'cos-nodejs-sdk-v5';

const dramaCover_post = defineEventHandler(async (event) => {
  var _a;
  console.log("\u{1F680} [\u540E\u7AEF-\u5C01\u9762\u4E0A\u4F20] API\u8C03\u7528\u5F00\u59CB");
  console.log("\u{1F310} [\u540E\u7AEF-\u5C01\u9762\u4E0A\u4F20] \u8BF7\u6C42URL:", event.node.req.url);
  console.log("\u{1F4E1} [\u540E\u7AEF-\u5C01\u9762\u4E0A\u4F20] \u8BF7\u6C42\u65B9\u6CD5:", event.node.req.method);
  console.log("\u{1F511} [\u540E\u7AEF-\u5C01\u9762\u4E0A\u4F20] \u8BF7\u6C42\u5934:", event.node.req.headers);
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    console.log("\u{1F464} [\u540E\u7AEF-\u5C01\u9762\u4E0A\u4F20] \u7BA1\u7406\u5458\u4FE1\u606F:", admin ? { id: admin.id, username: admin.username } : "null");
    console.log("\u{1F4E6} [\u540E\u7AEF-\u5C01\u9762\u4E0A\u4F20] \u5F00\u59CB\u89E3\u6790FormData...");
    const formData = await readMultipartFormData(event);
    console.log("\u{1F4E6} [\u540E\u7AEF-\u5C01\u9762\u4E0A\u4F20] FormData\u89E3\u6790\u5B8C\u6210\uFF0C\u5B57\u6BB5\u6570\u91CF:", (formData == null ? void 0 : formData.length) || 0);
    if (!formData || formData.length === 0) {
      console.error("\u274C [\u540E\u7AEF-\u5C01\u9762\u4E0A\u4F20] \u672A\u627E\u5230FormData\u6216\u4E3A\u7A7A");
      throw createError({
        statusCode: 400,
        statusMessage: "\u672A\u9009\u62E9\u6587\u4EF6"
      });
    }
    formData.forEach((item, index) => {
      var _a2;
      console.log(`\u{1F4E6} [\u540E\u7AEF-\u5C01\u9762\u4E0A\u4F20] FormData[${index}]:`, {
        name: item.name,
        filename: item.filename,
        type: item.type,
        dataSize: ((_a2 = item.data) == null ? void 0 : _a2.length) || 0
      });
    });
    const fileData = formData.find((item) => item.name === "file");
    console.log("\u{1F4C1} [\u540E\u7AEF-\u5C01\u9762\u4E0A\u4F20] \u67E5\u627E\u6587\u4EF6\u5B57\u6BB5\u7ED3\u679C:", fileData ? "\u627E\u5230" : "\u672A\u627E\u5230");
    if (!fileData || !fileData.data || !fileData.filename) {
      console.error("\u274C [\u540E\u7AEF-\u5C01\u9762\u4E0A\u4F20] \u6587\u4EF6\u6570\u636E\u65E0\u6548:", {
        hasFileData: !!fileData,
        hasData: !!(fileData == null ? void 0 : fileData.data),
        hasFilename: !!(fileData == null ? void 0 : fileData.filename)
      });
      throw createError({
        statusCode: 400,
        statusMessage: "\u6587\u4EF6\u6570\u636E\u65E0\u6548"
      });
    }
    console.log("\u2705 [\u540E\u7AEF-\u5C01\u9762\u4E0A\u4F20] \u6587\u4EF6\u6570\u636E\u9A8C\u8BC1\u901A\u8FC7:", {
      filename: fileData.filename,
      type: fileData.type,
      size: fileData.data.length
    });
    const allowedTypes = [".jpg", ".jpeg", ".png", ".gif", ".webp", ".svg"];
    const fileExt = getFileExtension(fileData.filename);
    if (!validateFileType(fileData.filename, allowedTypes)) {
      throw createError({
        statusCode: 400,
        statusMessage: `\u77ED\u5267\u5C01\u9762\u53EA\u652F\u6301\u56FE\u7247\u683C\u5F0F: ${allowedTypes.join(", ")}`
      });
    }
    const maxSize = 5 * 1024 * 1024;
    if (fileData.data.length > maxSize) {
      throw createError({
        statusCode: 400,
        statusMessage: `\u5C01\u9762\u6587\u4EF6\u5927\u5C0F\u8D85\u8FC7\u9650\u5236\uFF0C\u6700\u5927\u5141\u8BB8 ${formatFileSize(maxSize)}`
      });
    }
    const uniqueFilename = generateUniqueFilename(fileData.filename);
    const tempPath = await getUploadPath(uniqueFilename, "temp");
    await writeFile(tempPath, fileData.data);
    try {
      const destPath = `mengtutv/drama-covers/${uniqueFilename}`;
      const uploadResult = await uploadToCOS({
        path: tempPath,
        originalname: fileData.filename,
        mimetype: fileData.type || "image/jpeg"
      }, destPath);
      await deleteFile(tempPath);
      await logAuditAction({
        action: "ADMIN_UPLOAD_DRAMA_COVER",
        description: `\u7BA1\u7406\u5458\u4E0A\u4F20\u77ED\u5267\u5C01\u9762: ${fileData.filename}`,
        userId: admin.id,
        username: admin.username,
        ip: getClientIP(event),
        userAgent: getHeader(event, "user-agent") || "",
        details: {
          originalName: fileData.filename,
          fileName: uniqueFilename,
          fileSize: fileData.data.length,
          url: uploadResult.url
        }
      });
      logger.info("\u7BA1\u7406\u5458\u4E0A\u4F20\u77ED\u5267\u5C01\u9762\u6210\u529F", {
        adminId: admin.id,
        adminUsername: admin.username,
        originalName: fileData.filename,
        url: uploadResult.url
      });
      return {
        success: true,
        message: "\u77ED\u5267\u5C01\u9762\u4E0A\u4F20\u6210\u529F",
        data: {
          url: uploadResult.url,
          filename: uniqueFilename,
          originalName: fileData.filename,
          size: fileData.data.length
        }
      };
    } catch (uploadError) {
      await deleteFile(tempPath);
      logger.error("\u77ED\u5267\u5C01\u9762\u4E0A\u4F20\u5230COS\u5931\u8D25", {
        error: uploadError.message,
        adminId: admin.id,
        filename: fileData.filename
      });
      throw createError({
        statusCode: 500,
        statusMessage: `\u77ED\u5267\u5C01\u9762\u4E0A\u4F20\u5931\u8D25: ${uploadError.message}`
      });
    }
  } catch (error) {
    logger.error("\u7BA1\u7406\u5458\u4E0A\u4F20\u77ED\u5267\u5C01\u9762\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { dramaCover_post as default };
//# sourceMappingURL=drama-cover.post.mjs.map
