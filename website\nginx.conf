server {
    # 监听80端口
    listen 80;
    
    # 服务器名称，可以是域名或IP
    server_name localhost;
    
    # 网站根目录
    root /usr/share/nginx/html;
    
    # 默认首页文件
    index index.html index.htm;
    
    # 开启gzip压缩
    # 为什么：减少传输数据量，提高加载速度
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 静态文件缓存
    # 为什么：减少服务器负载，提高用户体验
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
    }
    
    # 处理Vue Router的history模式
    # 为什么：SPA应用需要将所有路由请求指向index.html
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 安全头设置
    # 为什么：提高网站安全性
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
