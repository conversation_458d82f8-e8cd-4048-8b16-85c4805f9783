import { c as defineE<PERSON><PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, r as readBody, k as getPool, l as logger, e as getClientIP } from '../../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const sort_patch = defineEventHandler(async (event) => {
  var _a;
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(adminPayload.id, PERMISSIONS.SYSTEM_MENU_EDIT);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u4FEE\u6539\u83DC\u5355\u6392\u5E8F"
      });
    }
    const body = await readBody(event);
    const { menuItems } = body;
    if (!Array.isArray(menuItems) || menuItems.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u83DC\u5355\u6392\u5E8F\u6570\u636E"
      });
    }
    for (const item of menuItems) {
      if (!item.id || typeof item.sortOrder !== "number") {
        throw createError({
          statusCode: 400,
          statusMessage: "\u83DC\u5355\u9879\u6570\u636E\u683C\u5F0F\u9519\u8BEF"
        });
      }
    }
    const connection = await getPool().getConnection();
    await connection.beginTransaction();
    try {
      for (const item of menuItems) {
        await connection.execute(`
          UPDATE admin_menus 
          SET sort_order = ?, updated_at = NOW()
          WHERE id = ?
        `, [item.sortOrder, item.id]);
      }
      await connection.commit();
      logger.info("\u83DC\u5355\u6392\u5E8F\u66F4\u65B0\u6210\u529F", {
        adminId: adminPayload.id,
        menuCount: menuItems.length,
        ip: getClientIP(event)
      });
      return {
        success: true,
        data: {
          message: "\u83DC\u5355\u6392\u5E8F\u66F4\u65B0\u6210\u529F",
          updatedCount: menuItems.length
        }
      };
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    logger.error("\u83DC\u5355\u6392\u5E8F\u66F4\u65B0\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { sort_patch as default };
//# sourceMappingURL=sort.patch.mjs.map
