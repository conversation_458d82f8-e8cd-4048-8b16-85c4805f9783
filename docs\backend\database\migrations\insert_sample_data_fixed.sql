-- 修复字符集问题的示例数据插入脚本
-- 设置正确的字符集
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 插入投资记录（使用英文项目名称避免编码问题）
INSERT IGNORE INTO user_investments (
    user_id, project_id, project_name, investment_amount, investment_date,
    expected_return_rate, expected_return_amount, actual_return_amount,
    project_status, investment_status, start_date, end_date, progress
) VALUES 
(3, 1, 'Urban Romance Series', 500000, '2023-05-20', 18.5, 92500, 75000, 'active', 'active', '2023-05-20', '2024-05-19', 65),
(3, 2, 'Youth Drama Series', 400000, '2023-07-10', 16.0, 64000, 48000, 'active', 'active', '2023-07-10', '2024-07-09', 40),
(3, 3, 'Fantasy World Series', 300000, '2023-09-01', 20.0, 60000, 15000, 'active', 'active', '2023-09-01', '2024-08-31', 15);

-- 获取投资记录ID并插入收益记录
SET @investment1 = (SELECT id FROM user_investments WHERE user_id = 3 AND project_id = 1 LIMIT 1);
SET @investment2 = (SELECT id FROM user_investments WHERE user_id = 3 AND project_id = 2 LIMIT 1);
SET @investment3 = (SELECT id FROM user_investments WHERE user_id = 3 AND project_id = 3 LIMIT 1);

INSERT IGNORE INTO user_returns (
    user_id, investment_id, return_type, return_amount, return_date,
    return_period, description, status
) VALUES 
(3, @investment1, 'monthly', 25000, '2023-10-01', '2023-09', 'Urban Romance Series September Returns', 'paid'),
(3, @investment2, 'monthly', 16000, '2023-10-01', '2023-09', 'Youth Drama Series September Returns', 'paid'),
(3, @investment3, 'monthly', 5000, '2023-10-01', '2023-09', 'Fantasy World Series September Returns', 'paid'),
(3, @investment1, 'monthly', 25000, '2023-11-01', '2023-10', 'Urban Romance Series October Returns', 'paid'),
(3, @investment2, 'monthly', 16000, '2023-11-01', '2023-10', 'Youth Drama Series October Returns', 'paid'),
(3, @investment3, 'monthly', 5000, '2023-11-01', '2023-10', 'Fantasy World Series October Returns', 'paid');

-- 插入通知（使用英文避免编码问题）
INSERT IGNORE INTO user_notifications (
    user_id, type, title, content, is_read, priority
) VALUES 
(3, 'system', 'Q3 Financial Report Released', 'You can view the latest financial report on the project details page', 0, 'normal'),
(3, 'project', 'Urban Romance Episode 8 Started Filming', 'Project is progressing smoothly and expected to complete on time', 1, 'normal'),
(3, 'return', 'September Returns Distributed', 'You received 46000 diamonds this month', 1, 'normal'),
(3, 'system', 'Investor Online Meeting Next Wednesday', 'Please check email notifications and attend on time', 0, 'high'),
(3, 'investment', 'New Project Sci-Fi Future Fundraising', 'Expected annual return rate 22%, limited investment', 0, 'normal');

-- 插入资产变动记录
INSERT IGNORE INTO user_asset_transactions (
    user_id, transaction_type, amount, balance_before, balance_after,
    related_type, description, transaction_no, status
) VALUES 
(3, 'shells_out', 500000, 800000, 300000, 'investment', 'Investment in Urban Romance Series', 'TXN202305200001', 'completed'),
(3, 'diamonds_in', 25000, 130000, 155000, 'return', 'Urban Romance Series September Returns', 'TXN202310010001', 'completed'),
(3, 'shells_out', 400000, 300000, -100000, 'investment', 'Investment in Youth Drama Series', 'TXN202307100001', 'completed'),
(3, 'diamonds_in', 16000, 155000, 171000, 'return', 'Youth Drama Series September Returns', 'TXN202310010002', 'completed'),
(3, 'shells_out', 300000, -100000, -400000, 'investment', 'Investment in Fantasy World Series', 'TXN202309010001', 'completed'),
(3, 'diamonds_in', 5000, 171000, 176000, 'return', 'Fantasy World Series September Returns', 'TXN202310010003', 'completed');
