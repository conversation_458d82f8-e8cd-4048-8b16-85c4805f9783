# 依赖文件夹
node_modules/
*/node_modules/
**/node_modules/

# 构建输出
dist/
build/
.output/
.nitro/
.nuxt/
.next/
out/

# 缓存文件
.cache/
.temp/
.tmp/
*.log
*.log.*

# 环境配置文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 文档文件夹（排除）
docs/

# 包管理器锁定文件（保留package-lock.json和pnpm-lock.yaml，排除yarn.lock）
yarn.lock

# 临时文件
*.tmp
*.temp

# 上传文件
uploads/
temp/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 特定项目排除
# Backend相关
backend/.nitro/
backend/dist/
backend/.output/
backend/node_modules/
backend/database/
backend/docs/
backend/scripts/test*
backend/api/test/

# Website相关
website/dist/
website/.nuxt/
website/.output/
website/docs/

# FundAdmin相关
fundAdmin/dist/
fundAdmin/build/
fundAdmin/apps/*/dist/
fundAdmin/apps/*/build/
fundAdmin/packages/*/dist/
fundAdmin/packages/*/build/
fundAdmin/README*.md
fundAdmin/apps/web-antd/ROLE_MANAGEMENT_MIGRATION.md
fundAdmin/apps/web-antd/src/views/*/README.md
fundAdmin/apps/web-antd/src/views/*/*.md

# 测试文件和开发文档
test-results/
playwright-report/
playwright/.cache/
**/test*.ts
**/test*.js
**/*test*.md
**/*TEST*.md
**/*REPORT*.md
**/*GUIDE*.md
**/*FIXES*.md

# 其他
*.tgz
*.tar.gz
