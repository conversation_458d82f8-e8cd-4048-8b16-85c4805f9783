import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, Z as verifyUserAccessToken, f as createError, r as readBody, q as query, D as verifyPassword, h as logAuditAction, i as getHeader, e as getClientIP, y as hashPassword, l as logger } from '../../../_/nitro.mjs';
import { d as validatePassword } from '../../../_/validators.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const changePassword_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const userPayload = verifyUserAccessToken(event);
    if (!userPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u7528\u6237\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"
      });
    }
    const body = await readBody(event);
    const { currentPassword, newPassword } = body;
    if (!currentPassword || !newPassword) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5F53\u524D\u5BC6\u7801\u548C\u65B0\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const passwordValidation = validatePassword(newPassword);
    if (!passwordValidation.valid) {
      throw createError({
        statusCode: 400,
        statusMessage: passwordValidation.message
      });
    }
    const users = await query(
      "SELECT id, username, password, status FROM users WHERE id = ?",
      [userPayload.id]
    );
    if (users.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u7528\u6237\u4E0D\u5B58\u5728"
      });
    }
    const user = users[0];
    if (user.status !== 1) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u7528\u6237\u8D26\u6237\u5DF2\u88AB\u7981\u7528"
      });
    }
    const isCurrentPasswordValid = await verifyPassword(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      await logAuditAction({
        action: "USER_CHANGE_PASSWORD_WRONG_CURRENT",
        description: "\u7528\u6237\u4FEE\u6539\u5BC6\u7801\u65F6\u5F53\u524D\u5BC6\u7801\u9519\u8BEF",
        userId: userPayload.id,
        username: userPayload.username,
        ip: getClientIP(event),
        userAgent: getHeader(event, "user-agent") || ""
      });
      throw createError({
        statusCode: 400,
        statusMessage: "\u5F53\u524D\u5BC6\u7801\u4E0D\u6B63\u786E"
      });
    }
    const isSamePassword = await verifyPassword(newPassword, user.password);
    if (isSamePassword) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65B0\u5BC6\u7801\u4E0D\u80FD\u4E0E\u5F53\u524D\u5BC6\u7801\u76F8\u540C"
      });
    }
    const hashedNewPassword = await hashPassword(newPassword);
    await query(
      "UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?",
      [hashedNewPassword, userPayload.id]
    );
    await logAuditAction({
      action: "USER_CHANGE_PASSWORD_SUCCESS",
      description: "\u7528\u6237\u6210\u529F\u4FEE\u6539\u5BC6\u7801",
      userId: userPayload.id,
      username: userPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || ""
    });
    logger.info("\u7528\u6237\u4FEE\u6539\u5BC6\u7801\u6210\u529F", {
      userId: userPayload.id,
      username: userPayload.username,
      ip: getClientIP(event)
    });
    return {
      success: true,
      message: "\u5BC6\u7801\u4FEE\u6539\u6210\u529F"
    };
  } catch (error) {
    logger.error("\u7528\u6237\u4FEE\u6539\u5BC6\u7801\u5931\u8D25", {
      error: error.message,
      userId: (_a = event.context.user) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { changePassword_post as default };
//# sourceMappingURL=change-password.post.mjs.map
