import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, Z as verifyUserAccessToken, f as createError, q as query } from '../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const dashboard_get = defineEventHandler(async (event) => {
  try {
    console.log("Dashboard API called");
    const userPayload = verifyUserAccessToken(event);
    if (!userPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u7528\u6237\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"
      });
    }
    const userId = userPayload.id;
    const assetQuery = `
      SELECT
        shells_balance,
        diamonds_balance,
        total_invested_shells,
        total_earned_diamonds,
        frozen_shells,
        frozen_diamonds
      FROM user_assets
      WHERE user_id = ?
    `;
    const assetRows = await query(assetQuery, [userId]);
    console.log("Asset rows:", assetRows);
    const assetData = assetRows[0] || {
      shells_balance: 0,
      diamonds_balance: 0,
      total_invested_shells: 0,
      total_earned_diamonds: 0,
      frozen_shells: 0,
      frozen_diamonds: 0
    };
    const projectsQuery = `
      SELECT
        project_id,
        project_name as name,
        SUM(investment_amount) as investAmount,
        MIN(DATE_FORMAT(start_date, '%Y-%m-%d')) as startDate,
        MAX(DATE_FORMAT(end_date, '%Y-%m-%d')) as endDate,
        CASE
          WHEN MAX(project_status) = 'active' THEN '\u8FDB\u884C\u4E2D'
          WHEN MAX(project_status) = 'completed' THEN '\u5DF2\u5B8C\u6210'
          WHEN MAX(project_status) = 'paused' THEN '\u6682\u505C'
          ELSE '\u672A\u77E5'
        END as status,
        AVG(progress) as progress,
        SUM(COALESCE(actual_return_amount, 0)) as returns,
        SUM(expected_return_amount) as expectedTotal,
        COUNT(*) as investmentCount
      FROM user_investments
      WHERE user_id = ? AND investment_status = 'active'
      GROUP BY project_id, project_name
      ORDER BY MIN(investment_date) DESC
    `;
    const projectRows = await query(projectsQuery, [userId]);
    const notificationsQuery = `
      SELECT
        id,
        type,
        title,
        content as message,
        DATE_FORMAT(created_at, '%Y-%m-%d') as date,
        is_read as is_read_status
      FROM user_notifications
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT 10
    `;
    const notificationRows = await query(notificationsQuery, [userId]);
    const trendsQuery = `
      SELECT
        DATE_FORMAT(created_at, '%Y-%m') as month,
        DATE_FORMAT(created_at, '%m\u6708') as monthLabel,
        SUM(CASE WHEN transaction_type = 'shells_out' AND related_type = 'investment' THEN amount ELSE 0 END) as consumedShells,
        SUM(CASE WHEN transaction_type = 'diamonds_in' AND related_type = 'return' THEN amount ELSE 0 END) as earnedDiamonds
      FROM user_asset_transactions
      WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 6 MONTH)
      GROUP BY DATE_FORMAT(created_at, '%Y-%m')
      ORDER BY month ASC
    `;
    const trendsRows = await query(trendsQuery, [userId]);
    const totalInvested = parseFloat(assetData.total_invested_shells);
    const shellsBalance = parseFloat(assetData.shells_balance);
    const totalEarned = parseFloat(assetData.total_earned_diamonds);
    const remainingShells = shellsBalance;
    const consumedShells = totalInvested - shellsBalance;
    const returnRate = consumedShells > 0 ? (totalEarned - consumedShells) / consumedShells * 100 : 0;
    const dashboardData = {
      // 资产概览 - 使用真实数据
      assetOverview: {
        totalShells: totalInvested,
        // 总投资贝壳数
        consumedShells,
        // 已消耗贝壳数
        totalDiamonds: totalEarned,
        // 累计收益钻石数
        returnRate: parseFloat(returnRate.toFixed(2)),
        // 收益率
        availableShells: shellsBalance,
        // 可用贝壳数（所剩投资贝壳）
        frozenShells: parseFloat(assetData.frozen_shells),
        // 冻结贝壳数
        projectsCount: projectRows.length,
        // 投资项目数
        monthlyDiamonds: 25e3
        // 本月钻石收益（暂时保持模拟数据）
      },
      // 用户投资项目 - 使用真实数据
      userProjects: projectRows.map((project) => ({
        ...project,
        investAmount: parseFloat(project.investAmount),
        returns: parseFloat(project.returns),
        expectedTotal: parseFloat(project.expectedTotal),
        fundCode: `FD${String(project.id).padStart(3, "0")}`,
        projectType: "\u77ED\u5267",
        riskLevel: "\u4E2D\u7B49",
        currentValue: parseFloat(project.investAmount) + parseFloat(project.returns),
        totalReturn: parseFloat(project.returns),
        monthlyReturn: Math.round(parseFloat(project.returns) / Math.max(1, project.progress / 100 * 12))
      })),
      // 收益趋势数据（支持双Y轴）- 使用真实数据
      returnTrends: trendsRows.map((trend) => ({
        month: trend.monthLabel,
        consumedShells: parseFloat(trend.consumedShells) || 0,
        earnedDiamonds: parseFloat(trend.earnedDiamonds) || 0
      })),
      // 投资分布（短剧项目占比）- 使用真实数据
      investmentDistribution: (() => {
        const totalInvestment = projectRows.reduce((sum, project) => sum + parseFloat(project.investAmount), 0);
        const colors = ["#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6", "#F97316"];
        return projectRows.map((project, index) => ({
          projectName: project.name,
          shellsAmount: parseFloat(project.investAmount),
          percentage: totalInvestment > 0 ? parseFloat((parseFloat(project.investAmount) / totalInvestment * 100).toFixed(1)) : 0,
          color: colors[index % colors.length]
        }));
      })(),
      // 风险分布
      riskDistribution: [
        { level: "\u4F4E\u98CE\u9669", amount: 4e5, percentage: 33.3, color: "#10B981" },
        { level: "\u4E2D\u7B49\u98CE\u9669", amount: 5e5, percentage: 41.7, color: "#F59E0B" },
        { level: "\u9AD8\u98CE\u9669", amount: 3e5, percentage: 25, color: "#EF4444" }
      ],
      // 最近交易记录
      recentTransactions: [
        {
          id: 1,
          type: "\u6295\u8D44",
          project: "\u300A\u5947\u5E7B\u4E16\u754C\u300B\u7CFB\u5217",
          amount: 3e5,
          date: "2023-09-01",
          status: "\u5DF2\u5B8C\u6210",
          description: "\u6295\u8D44\u77ED\u5267\u9879\u76EE"
        },
        {
          id: 2,
          type: "\u6536\u76CA",
          project: "\u300A\u9752\u6625\u6709\u4F60\u300B\u7CFB\u5217",
          amount: 8e3,
          date: "2023-10-01",
          status: "\u5DF2\u5230\u8D26",
          description: "\u6708\u5EA6\u6536\u76CA\u5206\u7EA2"
        }
      ],
      // 通知消息 - 使用真实数据
      notifications: notificationRows.map((notification) => ({
        ...notification,
        type: notification.type === "system" ? "\u7CFB\u7EDF\u901A\u77E5" : notification.type === "project" ? "\u9879\u76EE\u8FDB\u5C55" : notification.type === "return" ? "\u6536\u76CA\u53D1\u653E" : notification.type === "investment" ? "\u6295\u8D44\u673A\u4F1A" : "\u5176\u4ED6",
        read: notification.is_read_status === 1
      })),
      // 市场概览
      marketOverview: {
        totalMarketSize: "50\u4EBF",
        yearGrowthRate: "25%",
        activeProjects: 156,
        totalInvestors: 8520,
        averageReturn: "18.5%",
        successRate: "92%"
      },
      // 充值记录
      rechargeRecords: [],
      // 提现记录
      withdrawRecords: []
    };
    return {
      success: true,
      data: dashboardData
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u7528\u6237\u4EEA\u8868\u677F\u6570\u636E\u5931\u8D25:", error);
    return {
      success: false,
      error: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    };
  }
});

export { dashboard_get as default };
//# sourceMappingURL=dashboard.get.mjs.map
