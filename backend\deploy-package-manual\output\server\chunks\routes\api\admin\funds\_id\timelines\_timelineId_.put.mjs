import { c as defineEvent<PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, q as query, r as readBody, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _timelineId__put = defineEventHandler(async (event) => {
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const fundId = getRouterParam(event, "id");
    const timelineId = getRouterParam(event, "timelineId");
    if (!fundId || isNaN(Number(fundId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u57FA\u91D1ID"
      });
    }
    if (!timelineId || isNaN(Number(timelineId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u65F6\u95F4\u7EBFID"
      });
    }
    const existingTimeline = await query(
      "SELECT * FROM fund_timelines WHERE id = ? AND fund_id = ?",
      [timelineId, fundId]
    );
    if (existingTimeline.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u65F6\u95F4\u7EBF\u4E0D\u5B58\u5728"
      });
    }
    const body = await readBody(event);
    const { stage, startDate, endDate, isOpenEnded, status, sortOrder } = body;
    if (!stage || !stage.trim() || !startDate || !startDate.trim() || !status || !status.trim()) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u9636\u6BB5\u540D\u79F0\u3001\u5F00\u59CB\u65E5\u671F\u548C\u72B6\u6001\u4E3A\u5FC5\u586B\u9879"
      });
    }
    const startDateObj = new Date(startDate);
    if (isNaN(startDateObj.getTime())) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5F00\u59CB\u65E5\u671F\u683C\u5F0F\u65E0\u6548"
      });
    }
    let endDateObj = null;
    if (!isOpenEnded && endDate) {
      endDateObj = new Date(endDate);
      if (isNaN(endDateObj.getTime())) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u7ED3\u675F\u65E5\u671F\u683C\u5F0F\u65E0\u6548"
        });
      }
      if (endDateObj < startDateObj) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u7ED3\u675F\u65E5\u671F\u4E0D\u80FD\u65E9\u4E8E\u5F00\u59CB\u65E5\u671F"
        });
      }
    }
    let dateDisplay = "";
    if (isOpenEnded) {
      dateDisplay = `${startDate} \u8D77`;
    } else if (endDate && startDate !== endDate) {
      dateDisplay = `${startDate} \u81F3 ${endDate}`;
    } else {
      dateDisplay = startDate;
    }
    await query(
      `UPDATE fund_timelines 
       SET stage = ?, date = ?, start_date = ?, end_date = ?, is_open_ended = ?, status = ?, sort_order = ? 
       WHERE id = ? AND fund_id = ?`,
      [
        stage.trim(),
        dateDisplay,
        startDate,
        endDate || null,
        isOpenEnded || false,
        status,
        Number(sortOrder) || 1,
        timelineId,
        fundId
      ]
    );
    await logAuditAction({
      action: "ADMIN_UPDATE_FUND_TIMELINE",
      description: `\u7BA1\u7406\u5458\u66F4\u65B0\u57FA\u91D1\u65F6\u95F4\u7EBF: \u57FA\u91D1ID=${fundId}, \u65F6\u95F4\u7EBFID=${timelineId}`,
      userId: adminPayload.id,
      username: adminPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      metadata: {
        fundId: Number(fundId),
        timelineId: Number(timelineId),
        oldStage: existingTimeline[0].stage,
        newStage: stage.trim(),
        oldStartDate: existingTimeline[0].start_date,
        newStartDate: startDate,
        oldEndDate: existingTimeline[0].end_date,
        newEndDate: endDate || null,
        oldIsOpenEnded: existingTimeline[0].is_open_ended,
        newIsOpenEnded: isOpenEnded || false,
        sortOrder: Number(sortOrder) || 1
      }
    });
    logger.info("\u57FA\u91D1\u65F6\u95F4\u7EBF\u66F4\u65B0\u6210\u529F", {
      adminId: adminPayload.id,
      adminUsername: adminPayload.username,
      fundId: Number(fundId),
      timelineId: Number(timelineId),
      stage: stage.trim()
    });
    return {
      success: true,
      message: "\u57FA\u91D1\u65F6\u95F4\u7EBF\u66F4\u65B0\u6210\u529F",
      data: {
        id: Number(timelineId),
        fundId: Number(fundId),
        stage: stage.trim(),
        startDate,
        endDate: endDate || null,
        isOpenEnded: isOpenEnded || false,
        status,
        sortOrder: Number(sortOrder) || 1,
        updatedAt: (/* @__PURE__ */ new Date()).toISOString()
      }
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u57FA\u91D1\u65F6\u95F4\u7EBF\u5931\u8D25", {
      error: error.message,
      fundId: getRouterParam(event, "id"),
      timelineId: getRouterParam(event, "timelineId")
    });
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "\u66F4\u65B0\u57FA\u91D1\u65F6\u95F4\u7EBF\u5931\u8D25"
    });
  }
});

export { _timelineId__put as default };
//# sourceMappingURL=_timelineId_.put.mjs.map
