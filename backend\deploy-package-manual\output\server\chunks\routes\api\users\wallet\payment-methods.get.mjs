import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, Z as verifyUserAccessToken, f as createError, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const paymentMethods_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const userPayload = verifyUserAccessToken(event);
    if (!userPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u7528\u6237\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"
      });
    }
    const paymentMethods = [
      {
        id: "alipay",
        name: "\u652F\u4ED8\u5B9D",
        icon: "/images/payment/alipay.png",
        enabled: true,
        minAmount: 100,
        maxAmount: 5e4
      },
      {
        id: "wechat",
        name: "\u5FAE\u4FE1\u652F\u4ED8",
        icon: "/images/payment/wechat.png",
        enabled: true,
        minAmount: 100,
        maxAmount: 5e4
      },
      {
        id: "bank",
        name: "\u94F6\u884C\u5361",
        icon: "/images/payment/bank.png",
        enabled: true,
        minAmount: 1e3,
        maxAmount: 1e5
      }
    ];
    return {
      success: true,
      data: paymentMethods
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u652F\u4ED8\u65B9\u5F0F\u5217\u8868\u5931\u8D25", {
      error: error.message,
      userId: (_a = event.context.user) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { paymentMethods_get as default };
//# sourceMappingURL=payment-methods.get.mjs.map
