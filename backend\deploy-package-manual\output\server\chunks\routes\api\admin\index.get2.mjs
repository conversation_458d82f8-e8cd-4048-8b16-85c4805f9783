import { c as defineEvent<PERSON><PERSON><PERSON>, f as createError, g as getQuery, q as query, l as logger, e as getClientIP } from '../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../_/permission.mjs';
import { v as validatePagination } from '../../../_/validators.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_get = defineEventHandler(async (event) => {
  var _a, _b;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u67E5\u770B\u5BA1\u8BA1\u65E5\u5FD7"
      });
    }
    const queryParams = getQuery(event);
    const { page, pageSize, offset } = validatePagination(queryParams.page, queryParams.pageSize);
    const {
      user_type,
      action,
      start_date,
      end_date,
      user_id,
      username,
      sort_by = "created_at",
      sort_order = "desc"
    } = queryParams;
    let whereClause = "WHERE 1=1";
    const params = [];
    if (user_type) {
      whereClause += " AND user_type = ?";
      params.push(user_type);
    }
    if (action) {
      whereClause += " AND action = ?";
      params.push(action);
    }
    if (start_date) {
      whereClause += " AND created_at >= ?";
      params.push(start_date);
    }
    if (end_date) {
      whereClause += " AND created_at <= ?";
      params.push(end_date);
    }
    if (user_id) {
      whereClause += " AND user_id = ?";
      params.push(parseInt(user_id));
    }
    if (username) {
      whereClause += " AND username LIKE ?";
      params.push(`%${username}%`);
    }
    const allowedSortFields = ["created_at", "action", "username", "user_type"];
    const sortField = allowedSortFields.includes(sort_by) ? sort_by : "created_at";
    const sortDirection = sort_order.toLowerCase() === "asc" ? "ASC" : "DESC";
    const countResult = await query(
      `SELECT COUNT(*) as total FROM audit_log ${whereClause}`,
      params
    );
    const total = ((_a = countResult[0]) == null ? void 0 : _a.total) || 0;
    const logs = await query(
      `SELECT id, user_id, username, user_type, action, description, ip, user_agent, 
              details, created_at
       FROM audit_log 
       ${whereClause}
       ORDER BY ${sortField} ${sortDirection}
       LIMIT ? OFFSET ?`,
      [...params, pageSize, offset]
    );
    const actionTypes = await query("SELECT DISTINCT action FROM audit_log ORDER BY action");
    const formattedLogs = logs.map((log) => ({
      id: log.id,
      userId: log.user_id,
      username: log.username,
      userType: log.user_type,
      action: log.action,
      description: log.description,
      ip: log.ip,
      userAgent: log.user_agent,
      details: log.details ? JSON.parse(log.details) : null,
      createdAt: log.created_at
    }));
    return {
      success: true,
      data: {
        logs: formattedLogs,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize)
        },
        filters: {
          actions: actionTypes.map((item) => item.action)
        }
      }
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u5BA1\u8BA1\u65E5\u5FD7\u5931\u8D25", {
      error: error.message,
      adminId: (_b = event.context.admin) == null ? void 0 : _b.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { index_get as default };
//# sourceMappingURL=index.get2.mjs.map
