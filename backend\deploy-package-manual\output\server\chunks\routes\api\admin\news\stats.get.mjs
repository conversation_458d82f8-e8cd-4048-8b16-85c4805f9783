import { c as defineEvent<PERSON>and<PERSON>, q as query, o as logAdminAction, l as logger, e as getClientIP, f as createError } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const stats_get = defineEventHandler(async (event) => {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const totalNewsQuery = `
      SELECT COUNT(*) as total
      FROM news
    `;
    const totalNewsResult = await query(totalNewsQuery);
    const totalNews = ((_a = totalNewsResult[0]) == null ? void 0 : _a.total) || 0;
    const publishedNewsQuery = `
      SELECT COUNT(*) as total
      FROM news
      WHERE status = 'published'
    `;
    const publishedNewsResult = await query(publishedNewsQuery);
    const publishedNews = ((_b = publishedNewsResult[0]) == null ? void 0 : _b.total) || 0;
    const draftNewsQuery = `
      SELECT COUNT(*) as total
      FROM news
      WHERE status = 'draft'
    `;
    const draftNewsResult = await query(draftNewsQuery);
    const draftNews = ((_c = draftNewsResult[0]) == null ? void 0 : _c.total) || 0;
    const featuredNewsQuery = `
      SELECT COUNT(*) as total
      FROM news
      WHERE is_featured = 1
    `;
    const featuredNewsResult = await query(featuredNewsQuery);
    const featuredNews = ((_d = featuredNewsResult[0]) == null ? void 0 : _d.total) || 0;
    const totalViewsQuery = `
      SELECT SUM(view_count) as total
      FROM news
      WHERE status = 'published'
    `;
    const totalViewsResult = await query(totalViewsQuery);
    const totalViews = ((_e = totalViewsResult[0]) == null ? void 0 : _e.total) || 0;
    const todayNewsQuery = `
      SELECT COUNT(*) as total
      FROM news
      WHERE DATE(created_at) = CURDATE()
    `;
    const todayNewsResult = await query(todayNewsQuery);
    const todayNews = ((_f = todayNewsResult[0]) == null ? void 0 : _f.total) || 0;
    const weekNewsQuery = `
      SELECT COUNT(*) as total
      FROM news
      WHERE YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)
    `;
    const weekNewsResult = await query(weekNewsQuery);
    const weekNews = ((_g = weekNewsResult[0]) == null ? void 0 : _g.total) || 0;
    const monthNewsQuery = `
      SELECT COUNT(*) as total
      FROM news
      WHERE YEAR(created_at) = YEAR(CURDATE()) 
        AND MONTH(created_at) = MONTH(CURDATE())
    `;
    const monthNewsResult = await query(monthNewsQuery);
    const monthNews = ((_h = monthNewsResult[0]) == null ? void 0 : _h.total) || 0;
    const categoryStatsQuery = `
      SELECT 
        nc.id,
        nc.name,
        COUNT(n.id) as news_count
      FROM news_categories nc
      LEFT JOIN news n ON nc.id = n.category_id
      GROUP BY nc.id, nc.name
      ORDER BY news_count DESC
      LIMIT 10
    `;
    const categoryStats = await query(categoryStatsQuery);
    const trendQuery = `
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count
      FROM news
      WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `;
    const trendData = await query(trendQuery);
    const stats = {
      overview: {
        totalNews,
        publishedNews,
        draftNews,
        featuredNews,
        totalViews
      },
      period: {
        today: todayNews,
        week: weekNews,
        month: monthNews
      },
      categories: categoryStats.map((cat) => ({
        id: cat.id,
        name: cat.name,
        newsCount: cat.news_count
      })),
      trend: trendData.map((item) => ({
        date: item.date,
        count: item.count
      }))
    };
    await logAdminAction(admin.id, "news:stats", "\u67E5\u770B\u65B0\u95FB\u7EDF\u8BA1\u6570\u636E", {});
    return {
      success: true,
      data: stats
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u65B0\u95FB\u7EDF\u8BA1\u6570\u636E\u5931\u8D25", {
      error: error.message,
      stack: error.stack,
      adminId: (_i = event.context.admin) == null ? void 0 : _i.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u83B7\u53D6\u65B0\u95FB\u7EDF\u8BA1\u6570\u636E\u5931\u8D25"
    });
  }
});

export { stats_get as default };
//# sourceMappingURL=stats.get.mjs.map
