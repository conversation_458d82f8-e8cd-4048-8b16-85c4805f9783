import { c as defineEvent<PERSON><PERSON><PERSON>, f as createError, n as readMultipartFormData, l as logger, h as logAuditAction, i as getHeader, e as getClientIP } from '../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import { i as isObjectStorageAvailable, u as uploadToObjectStorage } from '../../../../_/cos-http-uploader.mjs';
import { b as getFileExtension, v as validateFileType, g as generateUniqueFilename, a as getUploadPath, d as deleteFile } from '../../../../_/file-utils.mjs';
import { writeFile } from 'fs/promises';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';
import 'path';
import 'fs';
import 'crypto';

const common_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u4E0A\u4F20\u6587\u4EF6"
      });
    }
    const isStorageAvailable = await isObjectStorageAvailable();
    if (!isStorageAvailable) {
      throw createError({
        statusCode: 503,
        statusMessage: "\u5BF9\u8C61\u5B58\u50A8\u670D\u52A1\u4E0D\u53EF\u7528\uFF0C\u8BF7\u68C0\u67E5\u7CFB\u7EDF\u8BBE\u7F6E"
      });
    }
    const formData = await readMultipartFormData(event);
    if (!formData || formData.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u672A\u9009\u62E9\u6587\u4EF6"
      });
    }
    const fileData = formData.find((item) => item.name === "file");
    if (!fileData || !fileData.data || !fileData.filename) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6587\u4EF6\u6570\u636E\u65E0\u6548"
      });
    }
    const destPathData = formData.find((item) => item.name === "destPath");
    const destPath = (destPathData == null ? void 0 : destPathData.data) ? new TextDecoder().decode(destPathData.data) : void 0;
    const allowedTypesData = formData.find((item) => item.name === "allowedTypes");
    const allowedTypesStr = (allowedTypesData == null ? void 0 : allowedTypesData.data) ? new TextDecoder().decode(allowedTypesData.data) : "";
    const allowedTypes = allowedTypesStr ? allowedTypesStr.split(",").map((t) => t.trim()) : [
      ".jpg",
      ".jpeg",
      ".png",
      ".gif",
      ".bmp",
      ".webp",
      // 图片
      ".pdf",
      ".doc",
      ".docx",
      ".xls",
      ".xlsx",
      ".ppt",
      ".pptx",
      // 文档
      ".txt",
      ".csv",
      ".json",
      ".xml",
      // 文本
      ".mp4",
      ".avi",
      ".mov",
      ".wmv",
      ".flv",
      ".mkv",
      // 视频
      ".mp3",
      ".wav",
      ".flac",
      ".aac",
      // 音频
      ".zip",
      ".rar",
      ".7z",
      ".tar",
      ".gz"
      // 压缩包
    ];
    const fileExt = getFileExtension(fileData.filename);
    if (!validateFileType(fileData.filename, allowedTypes)) {
      throw createError({
        statusCode: 400,
        statusMessage: `\u4E0D\u652F\u6301\u7684\u6587\u4EF6\u7C7B\u578B: ${fileExt}\u3002\u652F\u6301\u7684\u7C7B\u578B: ${allowedTypes.join(", ")}`
      });
    }
    const maxSize = 50 * 1024 * 1024;
    if (fileData.data.length > maxSize) {
      throw createError({
        statusCode: 400,
        statusMessage: `\u6587\u4EF6\u5927\u5C0F\u8D85\u8FC7\u9650\u5236\uFF0C\u6700\u5927\u5141\u8BB8 ${Math.round(maxSize / 1024 / 1024)}MB`
      });
    }
    const uniqueFilename = generateUniqueFilename(fileData.filename);
    const tempPath = await getUploadPath(uniqueFilename, "temp");
    await writeFile(tempPath, fileData.data);
    logger.info("\u5F00\u59CB\u4E0A\u4F20\u6587\u4EF6", {
      originalName: fileData.filename,
      fileName: uniqueFilename,
      fileSize: fileData.data.length,
      fileType: fileExt,
      destPath,
      adminId: admin.id
    });
    try {
      const uploadResult = await uploadToObjectStorage({
        path: tempPath,
        originalname: fileData.filename,
        mimetype: fileData.type || "application/octet-stream"
      }, destPath);
      await deleteFile(tempPath);
      await logAuditAction({
        action: "ADMIN_UPLOAD_FILE",
        description: `\u7BA1\u7406\u5458\u4E0A\u4F20\u6587\u4EF6: ${fileData.filename}`,
        userId: admin.id,
        username: admin.username,
        ip: getClientIP(event),
        userAgent: getHeader(event, "user-agent") || "",
        details: {
          originalName: fileData.filename,
          fileName: uniqueFilename,
          fileSize: fileData.data.length,
          fileType: fileExt,
          url: uploadResult.url,
          provider: uploadResult.provider,
          destPath: destPath || "auto"
        }
      });
      logger.info("\u6587\u4EF6\u4E0A\u4F20\u6210\u529F", {
        originalName: fileData.filename,
        url: uploadResult.url,
        provider: uploadResult.provider,
        adminId: admin.id
      });
      return {
        success: true,
        data: {
          url: uploadResult.url,
          filename: uniqueFilename,
          originalname: fileData.filename,
          mimetype: fileData.type || "application/octet-stream",
          size: fileData.data.length,
          provider: uploadResult.provider,
          key: uploadResult.key
        }
      };
    } catch (uploadError) {
      await deleteFile(tempPath);
      logger.error("\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25", {
        error: uploadError.message,
        originalName: fileData.filename,
        adminId: admin.id
      });
      throw createError({
        statusCode: 500,
        statusMessage: `\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25: ${uploadError.message}`
      });
    }
  } catch (error) {
    logger.error("\u901A\u7528\u6587\u4EF6\u4E0A\u4F20\u63A5\u53E3\u9519\u8BEF", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5"
    });
  }
});

export { common_post as default };
//# sourceMappingURL=common.post.mjs.map
