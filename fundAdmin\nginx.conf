# 剧投投后台管理系统 - 生产环境Nginx配置
# 适用于 admin.qinghee.com.cn

server {
    listen 80;
    server_name admin.qinghee.com.cn;
    root /www/wwwroot/admin.qinghee.com.cn;
    index index.html index.htm;

    # 开启gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
    }

    # 处理Vue Router的hash模式（生产环境使用hash模式）
    location / {
        try_files $uri $uri/ /index.html;

        # 添加CORS头（如果需要）
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
    }

    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
    }

    # 错误页面
    error_page 404 /index.html;
}
