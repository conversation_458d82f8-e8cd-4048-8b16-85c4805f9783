import { c as defineEvent<PERSON>and<PERSON>, q as query, l as logger, e as getClientIP, f as createError } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const all_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const tagsQuery = `
      SELECT 
        nt.id,
        nt.name,
        nt.created_at,
        COUNT(ntr.news_id) as usage_count
      FROM news_tags nt
      LEFT JOIN news_tag_relations ntr ON nt.id = ntr.tag_id
      GROUP BY nt.id, nt.name, nt.created_at
      ORDER BY usage_count DESC, nt.name ASC
      LIMIT 100
    `;
    const tags = await query(tagsQuery);
    const formattedTags = tags.map((tag) => ({
      id: tag.id,
      name: tag.name,
      usageCount: tag.usage_count || 0,
      createdAt: tag.created_at
    }));
    return {
      success: true,
      data: formattedTags
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u6240\u6709\u65B0\u95FB\u6807\u7B7E\u5931\u8D25", {
      error: error.message,
      stack: error.stack,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u83B7\u53D6\u6807\u7B7E\u5217\u8868\u5931\u8D25"
    });
  }
});

export { all_get as default };
//# sourceMappingURL=all.get.mjs.map
