@echo off
echo ========================================
echo 剧投投后端服务构建和部署脚本
echo ========================================

echo.
echo [1/8] 清理之前的构建...
if exist .output rmdir /s /q .output
if exist node_modules\.vite rmdir /s /q node_modules\.vite

echo.
echo [2/8] 复制生产环境配置...
copy .env.production .env

echo.
echo [3/8] 安装依赖...
call pnpm install

echo.
echo [4/8] 构建生产版本...
call pnpm build

echo.
echo [5/8] 检查构建结果...
if not exist .output\server\index.mjs (
    echo 错误: 构建失败，找不到 .output\server\index.mjs
    pause
    exit /b 1
)

echo.
echo [6/8] 创建部署包...
if exist deploy-package-new rmdir /s /q deploy-package-new
mkdir deploy-package-new

echo 复制构建文件...
xcopy .output deploy-package-new\.output\ /E /I /Q

echo 复制配置文件...
copy package.json deploy-package-new\
copy .env deploy-package-new\
copy deploy-package\ecosystem.config.js deploy-package-new\

echo 创建必要目录...
mkdir deploy-package-new\uploads 2>nul
mkdir deploy-package-new\logs 2>nul

echo.
echo [7/8] 压缩部署包...
cd deploy-package-new
tar -czf ..\mengtu-backend-deploy.tar.gz *
cd ..

echo.
echo [8/8] 构建完成！
echo.
echo 部署包已创建: mengtu-backend-deploy.tar.gz
echo.
echo 接下来请：
echo 1. 将 mengtu-backend-deploy.tar.gz 上传到服务器
echo 2. 在服务器上执行部署命令
echo.
echo 服务器部署命令：
echo cd /www/wwwroot
echo mv api.qinghee.com.cn api.qinghee.com.cn.backup
echo mkdir api.qinghee.com.cn
echo cd api.qinghee.com.cn
echo # 上传 mengtu-backend-deploy.tar.gz 到此目录
echo tar -xzf mengtu-backend-deploy.tar.gz
echo chown -R www:www /www/wwwroot/api.qinghee.com.cn
echo chmod -R 755 /www/wwwroot/api.qinghee.com.cn
echo pnpm install --prod
echo pm2 start ecosystem.config.js
echo.
pause
