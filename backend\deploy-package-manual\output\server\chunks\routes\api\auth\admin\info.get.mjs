import { c as defineEvent<PERSON>and<PERSON>, v as verifyAdminAccessToken, f as createError, A as findAdminById, B as getAdminPermissions, q as query, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const info_get = defineEventHandler(async (event) => {
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const admin = await findAdminById(adminPayload.id);
    if (!admin) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u7BA1\u7406\u5458\u4E0D\u5B58\u5728"
      });
    }
    const permissions = await getAdminPermissions(admin.id);
    const roles = await query(
      `SELECT ar.code
       FROM admin_role_relations arr
       JOIN admin_roles ar ON arr.role_id = ar.id
       WHERE arr.admin_id = ?`,
      [admin.id]
    );
    return {
      success: true,
      data: {
        userId: admin.id.toString(),
        // 前端期望字符串类型
        username: admin.username,
        email: admin.email,
        realName: admin.real_name || admin.username,
        // 前端期望realName字段
        avatar: admin.avatar || "",
        homePath: admin.home_path || "/workspace",
        // 前端期望homePath字段
        roles: roles.map((r) => r.code),
        // 前端期望roles数组
        desc: "\u7BA1\u7406\u5458",
        // 前端期望desc字段
        token: "",
        // 前端期望token字段，但这里不需要返回
        permissions,
        status: admin.status,
        last_login_at: admin.last_login_at,
        created_at: admin.created_at
      }
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u7BA1\u7406\u5458\u4FE1\u606F\u5931\u8D25", {
      error: error.message,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { info_get as default };
//# sourceMappingURL=info.get.mjs.map
