-- 插入示例数据
-- 为用户ID 3 (qianying) 插入示例数据

-- 插入用户资产数据
INSERT IGNORE INTO user_assets (
    user_id, shells_balance, diamonds_balance, 
    total_invested_shells, total_earned_diamonds,
    frozen_shells, frozen_diamonds
) VALUES (3, 300000, 180000, 1200000, 180000, 0, 0);

-- 插入投资记录
INSERT IGNORE INTO user_investments (
    user_id, project_id, project_name, investment_amount, investment_date,
    expected_return_rate, expected_return_amount, actual_return_amount,
    project_status, investment_status, start_date, end_date, progress
) VALUES 
(3, 1, '《都市情感》系列', 500000, '2023-05-20', 18.5, 92500, 75000, 'active', 'active', '2023-05-20', '2024-05-19', 65),
(3, 2, '《青春有你》系列', 400000, '2023-07-10', 16.0, 64000, 48000, 'active', 'active', '2023-07-10', '2024-07-09', 40),
(3, 3, '《奇幻世界》系列', 300000, '2023-09-01', 20.0, 60000, 15000, 'active', 'active', '2023-09-01', '2024-08-31', 15);

-- 获取投资记录ID并插入收益记录
SET @investment1 = (SELECT id FROM user_investments WHERE user_id = 3 AND project_id = 1 LIMIT 1);
SET @investment2 = (SELECT id FROM user_investments WHERE user_id = 3 AND project_id = 2 LIMIT 1);
SET @investment3 = (SELECT id FROM user_investments WHERE user_id = 3 AND project_id = 3 LIMIT 1);

INSERT IGNORE INTO user_returns (
    user_id, investment_id, return_type, return_amount, return_date,
    return_period, description, status
) VALUES 
(3, @investment1, 'monthly', 25000, '2023-10-01', '2023年9月', '《都市情感》系列 9月份收益分红', 'paid'),
(3, @investment2, 'monthly', 16000, '2023-10-01', '2023年9月', '《青春有你》系列 9月份收益分红', 'paid'),
(3, @investment3, 'monthly', 5000, '2023-10-01', '2023年9月', '《奇幻世界》系列 9月份收益分红', 'paid'),
(3, @investment1, 'monthly', 25000, '2023-11-01', '2023年10月', '《都市情感》系列 10月份收益分红', 'paid'),
(3, @investment2, 'monthly', 16000, '2023-11-01', '2023年10月', '《青春有你》系列 10月份收益分红', 'paid'),
(3, @investment3, 'monthly', 5000, '2023-11-01', '2023年10月', '《奇幻世界》系列 10月份收益分红', 'paid');

-- 插入通知
INSERT IGNORE INTO user_notifications (
    user_id, type, title, content, is_read, priority
) VALUES 
(3, 'system', '第三季度财务报告已发布', '您可以在项目详情页查看最新的财务报告', 0, 'normal'),
(3, 'project', '《都市情感》第8集开始拍摄', '项目进展顺利，预计按时完成', 1, 'normal'),
(3, 'return', '9月份收益已发放至您的账户', '本月共获得46000钻石收益', 1, 'normal'),
(3, 'system', '投资者线上会议将于下周三举行', '请关注邮件通知，准时参加会议', 0, 'high'),
(3, 'investment', '新项目《科幻未来》开始募资', '预期年化收益率22%，限额投资', 0, 'normal');

-- 插入资产变动记录
INSERT IGNORE INTO user_asset_transactions (
    user_id, transaction_type, amount, balance_before, balance_after,
    related_type, description, transaction_no, status
) VALUES 
(3, 'shells_out', 500000, 800000, 300000, 'investment', '投资《都市情感》系列', 'TXN202305200001', 'completed'),
(3, 'diamonds_in', 25000, 130000, 155000, 'return', '《都市情感》系列9月收益', 'TXN202310010001', 'completed'),
(3, 'shells_out', 400000, 300000, -100000, 'investment', '投资《青春有你》系列', 'TXN202307100001', 'completed'),
(3, 'diamonds_in', 16000, 155000, 171000, 'return', '《青春有你》系列9月收益', 'TXN202310010002', 'completed'),
(3, 'shells_out', 300000, -100000, -400000, 'investment', '投资《奇幻世界》系列', 'TXN202309010001', 'completed'),
(3, 'diamonds_in', 5000, 171000, 176000, 'return', '《奇幻世界》系列9月收益', 'TXN202310010003', 'completed');

-- 为用户ID 4 插入基础数据
INSERT IGNORE INTO user_assets (
    user_id, shells_balance, diamonds_balance, 
    total_invested_shells, total_earned_diamonds,
    frozen_shells, frozen_diamonds
) VALUES (4, 150000, 85000, 600000, 85000, 0, 0);

-- 为用户ID 5 插入基础数据
INSERT IGNORE INTO user_assets (
    user_id, shells_balance, diamonds_balance, 
    total_invested_shells, total_earned_diamonds,
    frozen_shells, frozen_diamonds
) VALUES (5, 200000, 120000, 800000, 120000, 0, 0);
