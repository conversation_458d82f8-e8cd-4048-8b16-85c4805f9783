# 短剧编辑功能故障排除指南

## 🚨 已解决的问题

### 1. 图标导入冲突
**问题**: `Identifier 'Upload' has already been declared`
**原因**: `ant-design-vue` 和 `@vben/icons` 都有 `Upload` 导出
**解决方案**: 使用 `IconifyIcon` 组件

```typescript
// ❌ 错误做法
import { Upload } from '@vben/icons';
import { Upload } from 'ant-design-vue'; // 冲突！

// ✅ 正确做法
import { IconifyIcon } from '@vben/icons';
// 在模板中使用
<IconifyIcon icon="ant-design:upload-outlined" class="text-base" />
```

### 2. 缺少图标导出
**问题**: `does not provide an export named 'Save'`
**原因**: 图标包中没有导出该图标
**解决方案**: 在核心图标包中添加图标

```typescript
// fundAdmin/packages/@core/base/icons/src/lucide.ts
export { Save } from 'lucide-vue-next';
```

### 3. Ant Design Vue Form.Item 警告
**问题**: `FormItem can only collect one field item, you haved set AInput, AUpload 2 field items`
**原因**: 在一个 FormItem 中放置了多个表单控件
**解决方案**: 将控件分别包装在不同的 FormItem 中

```vue
<!-- ❌ 错误做法 -->
<FormItem label="封面图片" name="cover">
  <Input v-model:value="formData.cover" />
  <Upload :custom-request="handleUpload">
    <Button>上传</Button>
  </Upload>
</FormItem>

<!-- ✅ 正确做法 -->
<FormItem label="封面图片" name="cover">
  <Input v-model:value="formData.cover" />
</FormItem>
<FormItem label=" ">
  <Upload :custom-request="handleUpload">
    <Button>上传</Button>
  </Upload>
</FormItem>
```

### 4. 后端删除API错误
**问题**: `DELETE /api/admin/dramas/13/materials/3 500 (Internal Server Error)`
**原因**: 代码中引用了未定义的 `admin` 变量
**解决方案**: 使用可选链操作符

```typescript
// ❌ 错误做法
adminId: admin.id,

// ✅ 正确做法
adminId: event.context.admin?.id || 'unknown',
```

### 5. 文件上传COS集成
**问题**: 上传功能没有使用专门的COS上传API
**原因**: 前端调用了通用上传API而不是专门的短剧上传API
**解决方案**: 使用专门的上传端点

```typescript
// ❌ 错误做法
const response = await requestClient.post('/admin/upload/common', formData);

// ✅ 正确做法
const response = await requestClient.post('/admin/upload/drama-cover', formData);
const response = await requestClient.post('/admin/upload/drama-material', formData);
```

## 🔧 常见问题解决步骤

### 问题1: 页面无法加载
```
Failed to fetch dynamically imported module
```

**解决步骤**:
1. 检查组件内部是否有语法错误
2. 检查导入语句是否正确
3. 检查图标导入是否有冲突
4. 重启开发服务器

### 问题2: 图标不显示
```
Cannot resolve module '@vben/icons'
```

**解决步骤**:
1. 确认图标在核心包中已导出
2. 使用 `IconifyIcon` 组件代替直接导入
3. 检查图标名称是否正确

### 问题3: TypeScript 类型错误
```
Type 'xxx' is not assignable to type 'yyy'
```

**解决步骤**:
1. 检查 API 类型定义是否完整
2. 确认组件 props 类型是否正确
3. 检查事件发射类型是否匹配

## 📋 快速检查清单

### 开发前检查
- [ ] 前端服务正常运行 (`pnpm dev`)
- [ ] 后端服务正常运行
- [ ] 数据库连接正常
- [ ] 对象存储配置正确

### 代码检查
- [ ] 导入语句无冲突
- [ ] 图标使用 `IconifyIcon` 或正确别名
- [ ] TypeScript 类型定义完整
- [ ] 事件处理函数命名规范

### 功能测试
- [ ] 页面正常加载
- [ ] 各选项卡切换正常
- [ ] 表单数据正确显示
- [ ] 保存功能正常
- [ ] 文件上传功能正常

## 🛠️ 调试命令

### 重启服务
```bash
# 停止服务
Ctrl + C

# 重新启动前端
cd fundAdmin/apps/web-antd
pnpm dev

# 重新启动后端
cd backend
npm start
```

### 清理缓存
```bash
# 清理 node_modules
rm -rf node_modules
pnpm install

# 清理 Vite 缓存
rm -rf .vite
```

### 检查依赖
```bash
# 检查图标包
pnpm list @vben/icons

# 检查 ant-design-vue
pnpm list ant-design-vue
```

## 📞 获取帮助

### 查看错误信息
1. 打开浏览器开发者工具 (F12)
2. 查看 Console 面板的错误信息
3. 查看 Network 面板的网络请求
4. 查看 Sources 面板的源码

### 常见错误关键词
- `Identifier 'XXX' has already been declared` → 导入冲突
- `does not provide an export named 'XXX'` → 缺少导出
- `Failed to fetch dynamically imported module` → 组件语法错误
- `Cannot resolve module` → 路径或依赖问题

### 联系开发者
如果遇到无法解决的问题，请提供：
1. 完整的错误信息
2. 浏览器控制台截图
3. 相关代码片段
4. 操作步骤

## 🎯 最佳实践提醒

1. **图标使用**: 优先使用 `IconifyIcon` 组件
2. **导入顺序**: Vue → UI库 → 图标 → 内部模块 → 类型
3. **错误处理**: 每个异步操作都要有 try-catch
4. **类型安全**: 使用 TypeScript 类型定义
5. **代码规范**: 遵循项目的命名和结构规范

记住：大多数前端问题都是由导入冲突、路径错误或类型不匹配引起的。按照这个指南逐步排查，通常能快速解决问题。
