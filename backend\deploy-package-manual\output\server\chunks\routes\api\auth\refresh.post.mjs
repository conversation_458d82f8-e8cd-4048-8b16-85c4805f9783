import { q as query, l as logger, c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, T as verifyRefreshToken, U as findUserById, A as findAdminById, Q as generateUserAccessToken, R as generateUserRefreshToken, E as generateAdminAccessToken, F as generateAdminRefreshToken, h as logAuditAction, i as getHeader, e as getClientIP } from '../../../_/nitro.mjs';
import { g as getRefreshTokenFromCookie, c as clearRefreshTokenCookie, s as setRefreshTokenCookie } from '../../../_/cookie-utils.mjs';
import require$$1 from 'crypto';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const blacklistCache = /* @__PURE__ */ new Map();
function generateTokenHash(token) {
  return require$$1.createHash("sha256").update(token).digest("hex");
}
async function addTokenToBlacklist(token, expiryDate) {
  try {
    const tokenHash = generateTokenHash(token);
    const mysqlDatetime = expiryDate.toISOString().slice(0, 19).replace("T", " ");
    await query(
      "INSERT INTO token_blacklist (token_hash, exp) VALUES (?, ?)",
      [tokenHash, mysqlDatetime]
    );
    blacklistCache.set(tokenHash, expiryDate.getTime());
    logger.info("\u4EE4\u724C\u5DF2\u6DFB\u52A0\u5230\u9ED1\u540D\u5355", { tokenHash: tokenHash.substring(0, 8) + "..." });
    return true;
  } catch (error) {
    logger.error("\u6DFB\u52A0\u4EE4\u724C\u5230\u9ED1\u540D\u5355\u5931\u8D25", { error: error.message });
    return false;
  }
}
async function isTokenBlacklisted(token) {
  try {
    const tokenHash = generateTokenHash(token);
    const cachedExpiry = blacklistCache.get(tokenHash);
    if (cachedExpiry) {
      if (Date.now() > cachedExpiry) {
        blacklistCache.delete(tokenHash);
        return false;
      }
      return true;
    }
    const result = await query(
      "SELECT exp FROM token_blacklist WHERE token_hash = ? AND exp > NOW()",
      [tokenHash]
    );
    if (result.length > 0) {
      const expiry = new Date(result[0].exp).getTime();
      blacklistCache.set(tokenHash, expiry);
      return true;
    }
    return false;
  } catch (error) {
    logger.error("\u68C0\u67E5\u4EE4\u724C\u9ED1\u540D\u5355\u72B6\u6001\u5931\u8D25", { error: error.message });
    return false;
  }
}

const refresh_post = defineEventHandler(async (event) => {
  try {
    const refreshToken = getRefreshTokenFromCookie(event);
    if (!refreshToken) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u5237\u65B0\u4EE4\u724C\u4E0D\u5B58\u5728"
      });
    }
    const payload = verifyRefreshToken(refreshToken);
    if (!payload) {
      clearRefreshTokenCookie(event);
      throw createError({
        statusCode: 401,
        statusMessage: "\u5237\u65B0\u4EE4\u724C\u65E0\u6548"
      });
    }
    if (await isTokenBlacklisted(refreshToken)) {
      clearRefreshTokenCookie(event);
      throw createError({
        statusCode: 401,
        statusMessage: "\u5237\u65B0\u4EE4\u724C\u5DF2\u5931\u6548"
      });
    }
    let user = null;
    if (payload.type === "user") {
      user = await findUserById(payload.userId || payload.id);
    } else if (payload.type === "admin") {
      user = await findAdminById(payload.userId || payload.id);
    }
    if (!user) {
      clearRefreshTokenCookie(event);
      throw createError({
        statusCode: 401,
        statusMessage: "\u7528\u6237\u4E0D\u5B58\u5728"
      });
    }
    let newAccessToken = "";
    let newRefreshToken = "";
    if (payload.type === "user") {
      newAccessToken = generateUserAccessToken(user);
      newRefreshToken = generateUserRefreshToken(user);
    } else if (payload.type === "admin") {
      newAccessToken = generateAdminAccessToken(user);
      newRefreshToken = generateAdminRefreshToken(user);
    }
    await addTokenToBlacklist(refreshToken, "refresh");
    setRefreshTokenCookie(event, newRefreshToken);
    await logAuditAction({
      action: payload.type === "admin" ? "ADMIN_REFRESH_TOKEN" : "USER_REFRESH_TOKEN",
      description: `${payload.type === "admin" ? "\u7BA1\u7406\u5458" : "\u7528\u6237"}\u5237\u65B0\u4EE4\u724C`,
      userId: user.id,
      username: user.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || ""
    });
    logger.info(`${payload.type === "admin" ? "\u7BA1\u7406\u5458" : "\u7528\u6237"}\u5237\u65B0\u4EE4\u724C\u6210\u529F`, {
      userId: user.id,
      username: user.username,
      type: payload.type
    });
    return {
      success: true,
      data: {
        accessToken: newAccessToken,
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          avatar: user.avatar,
          type: payload.type
        }
      }
    };
  } catch (error) {
    logger.error("\u5237\u65B0\u4EE4\u724C\u5931\u8D25", {
      error: error.message,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { refresh_post as default };
//# sourceMappingURL=refresh.post.mjs.map
