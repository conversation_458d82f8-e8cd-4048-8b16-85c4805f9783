{"name": "compress-commons", "version": "6.0.2", "description": "a library that defines a common interface for working with archive formats within node", "homepage": "https://github.com/archiverjs/node-compress-commons", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/archiverjs/node-compress-commons.git"}, "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "license": "MIT", "main": "lib/compress-commons.js", "files": ["lib"], "engines": {"node": ">= 14"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"crc-32": "^1.2.0", "crc32-stream": "^6.0.0", "is-stream": "^2.0.1", "normalize-path": "^3.0.0", "readable-stream": "^4.0.0"}, "devDependencies": {"chai": "4.4.1", "mkdirp": "3.0.1", "mocha": "10.3.0", "rimraf": "5.0.5"}, "keywords": ["compress", "commons", "archive"]}