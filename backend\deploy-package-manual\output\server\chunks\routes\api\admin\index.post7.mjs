import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, r as readBody, f as createError, q as query } from '../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_post = defineEventHandler(async (event) => {
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const body = await readBody(event);
    const {
      title,
      slug,
      content,
      author,
      status = "draft",
      isOnline = false,
      publishDate
    } = body;
    if (!title || !slug) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6807\u9898\u548C\u552F\u4E00\u6807\u8BC6\u4E3A\u5FC5\u586B\u5B57\u6BB5"
      });
    }
    const existingPost = await query(
      "SELECT id FROM posts WHERE slug = ?",
      [slug]
    );
    if (existingPost.length > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u552F\u4E00\u6807\u8BC6\u5DF2\u5B58\u5728\uFF0C\u8BF7\u4F7F\u7528\u5176\u4ED6\u6807\u8BC6"
      });
    }
    const validStatuses = ["draft", "pending", "published", "archived"];
    if (!validStatuses.includes(status)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u72B6\u6001\u503C"
      });
    }
    const insertPostQuery = `
      INSERT INTO posts (
        title, slug, content, author, status, is_online,
        publish_date, view_count, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 0, NOW(), NOW())
    `;
    let formattedPublishDate = null;
    if (publishDate) {
      try {
        const date = new Date(publishDate);
        formattedPublishDate = date.toISOString().slice(0, 19).replace("T", " ");
      } catch (error) {
        console.error("\u65E5\u671F\u683C\u5F0F\u8F6C\u6362\u5931\u8D25:", error);
        formattedPublishDate = null;
      }
    }
    const postResult = await query(insertPostQuery, [
      title,
      slug,
      content,
      author || admin.username,
      status,
      isOnline ? 1 : 0,
      formattedPublishDate
    ]);
    const postId = postResult.insertId;
    return {
      success: true,
      message: "\u63A8\u6587\u521B\u5EFA\u6210\u529F",
      data: {
        id: postId,
        title,
        slug
      }
    };
  } catch (error) {
    console.error("\u521B\u5EFA\u63A8\u6587\u5931\u8D25:", error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.message || "\u521B\u5EFA\u63A8\u6587\u5931\u8D25"
    });
  }
});

export { index_post as default };
//# sourceMappingURL=index.post7.mjs.map
