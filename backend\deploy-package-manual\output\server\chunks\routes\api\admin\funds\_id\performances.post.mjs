import { c as defineE<PERSON><PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, q as query, r as readBody, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const performances_post = defineEventHandler(async (event) => {
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const fundId = getRouterParam(event, "id");
    if (!fundId || isNaN(Number(fundId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u57FA\u91D1ID"
      });
    }
    const fundExists = await query(
      "SELECT id FROM funds WHERE id = ?",
      [fundId]
    );
    if (fundExists.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u57FA\u91D1\u4E0D\u5B58\u5728"
      });
    }
    const body = await readBody(event);
    const { year, return: returnValue, average } = body;
    if (!year || !returnValue) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5E74\u4EFD\u548C\u6536\u76CA\u7387\u4E3A\u5FC5\u586B\u9879"
      });
    }
    if (isNaN(Number(year)) || Number(year) < 1900 || Number(year) > 2100) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5E74\u4EFD\u683C\u5F0F\u65E0\u6548"
      });
    }
    const result = await query(
      "INSERT INTO fund_performances (fund_id, year, `return`, average, created_at) VALUES (?, ?, ?, ?, NOW())",
      [fundId, year, returnValue, average || null]
    );
    await logAuditAction({
      action: "ADMIN_CREATE_FUND_PERFORMANCE",
      description: `\u7BA1\u7406\u5458\u521B\u5EFA\u57FA\u91D1\u5386\u53F2\u4E1A\u7EE9: \u57FA\u91D1ID=${fundId}`,
      userId: adminPayload.id,
      username: adminPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      metadata: {
        fundId: Number(fundId),
        year,
        return: returnValue,
        average: average || null,
        performanceId: result.insertId
      }
    });
    logger.info("\u57FA\u91D1\u5386\u53F2\u4E1A\u7EE9\u521B\u5EFA\u6210\u529F", {
      adminId: adminPayload.id,
      adminUsername: adminPayload.username,
      fundId: Number(fundId),
      performanceId: result.insertId,
      year
    });
    return {
      success: true,
      message: "\u57FA\u91D1\u5386\u53F2\u4E1A\u7EE9\u521B\u5EFA\u6210\u529F",
      data: {
        id: result.insertId,
        fundId: Number(fundId),
        year,
        return: returnValue,
        average: average || null,
        createdAt: (/* @__PURE__ */ new Date()).toISOString()
      }
    };
  } catch (error) {
    logger.error("\u521B\u5EFA\u57FA\u91D1\u5386\u53F2\u4E1A\u7EE9\u5931\u8D25", {
      error: error.message,
      fundId: getRouterParam(event, "id")
    });
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "\u521B\u5EFA\u57FA\u91D1\u5386\u53F2\u4E1A\u7EE9\u5931\u8D25"
    });
  }
});

export { performances_post as default };
//# sourceMappingURL=performances.post.mjs.map
