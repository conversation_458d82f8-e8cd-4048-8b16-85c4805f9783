import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, r as readBody, f as createError, q as query } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_post = defineEventHandler(async (event) => {
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const body = await readBody(event);
    const {
      platform_name,
      platform_logo_url,
      company_id,
      platform_domain,
      mini_program_name,
      mini_program_link,
      mini_program_qrcode_url,
      platform_type,
      description,
      is_active = 1
    } = body;
    if (!platform_name) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5E73\u53F0\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    if (!platform_logo_url) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5E73\u53F0Logo\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const existingPlatform = await query(
      "SELECT id FROM drama_platforms WHERE platform_name = ?",
      [platform_name]
    );
    if (existingPlatform.length > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5E73\u53F0\u540D\u79F0\u5DF2\u5B58\u5728"
      });
    }
    const result = await query(
      `INSERT INTO drama_platforms (
        platform_name,
        platform_logo_url,
        company_id,
        platform_domain,
        mini_program_name,
        mini_program_link,
        mini_program_qrcode_url,
        platform_type,
        description,
        is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        platform_name,
        platform_logo_url,
        company_id || null,
        platform_domain || null,
        mini_program_name || null,
        mini_program_link || null,
        mini_program_qrcode_url || null,
        platform_type || "content",
        description || null,
        is_active
      ]
    );
    return {
      success: true,
      message: "\u5E73\u53F0\u521B\u5EFA\u6210\u529F",
      data: {
        id: result.insertId,
        platform_name,
        platform_logo_url,
        company_id,
        platform_domain,
        mini_program_name,
        mini_program_link,
        mini_program_qrcode_url,
        platform_type,
        description,
        is_active
      }
    };
  } catch (error) {
    console.error("\u521B\u5EFA\u5E73\u53F0\u5931\u8D25:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u521B\u5EFA\u5E73\u53F0\u5931\u8D25: " + error.message
    });
  }
});

export { index_post as default };
//# sourceMappingURL=index.post.mjs.map
