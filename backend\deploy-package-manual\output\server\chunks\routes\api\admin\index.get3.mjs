import { c as define<PERSON>vent<PERSON><PERSON><PERSON>, g as getQuery, q as query, l as logger, e as getClientIP, f as createError } from '../../../_/nitro.mjs';
import { v as validatePagination, a as validateStatus } from '../../../_/validators.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_get = defineEventHandler(async (event) => {
  var _a, _b;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const queryParams = getQuery(event);
    const { page, pageSize, offset } = validatePagination(queryParams.page, queryParams.pageSize);
    const status = validateStatus(queryParams.status);
    const search = queryParams.search;
    let whereClause = "WHERE 1=1";
    const params = [];
    if (search) {
      whereClause += " AND (title LIKE ? OR subtitle LIKE ?)";
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern);
    }
    if (status !== null) {
      whereClause += " AND is_active = ?";
      params.push(status);
    }
    const countResult = await query(
      `SELECT COUNT(*) as total FROM banners ${whereClause}`,
      params
    );
    const total = ((_a = countResult[0]) == null ? void 0 : _a.total) || 0;
    const banners = await query(
      `SELECT id, title, subtitle, image_url, link_url, background_color, 
              text_color, is_active, open_in_new_tab, sort_order, created_at, updated_at
       FROM banners 
       ${whereClause}
       ORDER BY sort_order ASC, id ASC
       LIMIT ? OFFSET ?`,
      [...params, pageSize, offset]
    );
    const formattedBanners = banners.map((banner) => ({
      id: banner.id,
      title: banner.title,
      subtitle: banner.subtitle,
      imageUrl: banner.image_url,
      linkUrl: banner.link_url,
      backgroundColor: banner.background_color,
      textColor: banner.text_color,
      isActive: banner.is_active === 1,
      openInNewTab: banner.open_in_new_tab === 1,
      sortOrder: banner.sort_order,
      createdAt: banner.created_at,
      updatedAt: banner.updated_at
    }));
    return {
      success: true,
      data: {
        list: formattedBanners,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize)
        }
      }
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u6A2A\u5E45\u5217\u8868\u5931\u8D25", {
      error: error.message,
      adminId: (_b = event.context.admin) == null ? void 0 : _b.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { index_get as default };
//# sourceMappingURL=index.get3.mjs.map
