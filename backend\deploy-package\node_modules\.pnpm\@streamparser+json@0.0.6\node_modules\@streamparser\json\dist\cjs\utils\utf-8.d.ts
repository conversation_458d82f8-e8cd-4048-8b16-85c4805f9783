export declare enum charset {
    BACKSPACE = 8,
    FORM_FEED = 12,
    NEWLINE = 10,
    CARRIAGE_RETURN = 13,
    TAB = 9,
    SPACE = 32,
    EXCLAMATION_MARK = 33,
    QUOTATION_MARK = 34,
    NUMBER_SIGN = 35,
    DOLLAR_SIGN = 36,
    PERCENT_SIGN = 37,
    AMPERSAND = 38,
    APOSTROPHE = 39,
    LEFT_PARENTHESIS = 40,
    RIGHT_PARENTHESIS = 41,
    ASTERISK = 42,
    PLUS_SIGN = 43,
    COMMA = 44,
    HYPHEN_MINUS = 45,
    FULL_STOP = 46,
    SOLIDUS = 47,
    DIGIT_ZERO = 48,
    DIGIT_ONE = 49,
    DIGIT_TWO = 50,
    DIGIT_THREE = 51,
    DIGIT_FOUR = 52,
    DIGIT_FIVE = 53,
    DIGIT_SIX = 54,
    DIGIT_SEVEN = 55,
    DIGIT_EIGHT = 56,
    DIGIT_NINE = 57,
    COLON = 58,
    SEMICOLON = 59,
    LESS_THAN_SIGN = 60,
    EQUALS_SIGN = 61,
    G<PERSON><PERSON><PERSON>_THAN_SIGN = 62,
    QUESTION_MARK = 63,
    <PERSON>MMERCIAL_AT = 64,
    LATIN_CAPITAL_LETTER_A = 65,
    LATIN_CAPITAL_LETTER_B = 66,
    LATIN_CAPITAL_LETTER_C = 67,
    LATIN_CAPITAL_LETTER_D = 68,
    LATIN_CAPITAL_LETTER_E = 69,
    LATIN_CAPITAL_LETTER_F = 70,
    LATIN_CAPITAL_LETTER_G = 71,
    LATIN_CAPITAL_LETTER_H = 72,
    LATIN_CAPITAL_LETTER_I = 73,
    LATIN_CAPITAL_LETTER_J = 74,
    LATIN_CAPITAL_LETTER_K = 75,
    LATIN_CAPITAL_LETTER_L = 76,
    LATIN_CAPITAL_LETTER_M = 77,
    LATIN_CAPITAL_LETTER_N = 78,
    LATIN_CAPITAL_LETTER_O = 79,
    LATIN_CAPITAL_LETTER_P = 80,
    LATIN_CAPITAL_LETTER_Q = 81,
    LATIN_CAPITAL_LETTER_R = 82,
    LATIN_CAPITAL_LETTER_S = 83,
    LATIN_CAPITAL_LETTER_T = 84,
    LATIN_CAPITAL_LETTER_U = 85,
    LATIN_CAPITAL_LETTER_V = 86,
    LATIN_CAPITAL_LETTER_W = 87,
    LATIN_CAPITAL_LETTER_X = 88,
    LATIN_CAPITAL_LETTER_Y = 89,
    LATIN_CAPITAL_LETTER_Z = 90,
    LEFT_SQUARE_BRACKET = 91,
    REVERSE_SOLIDUS = 92,
    RIGHT_SQUARE_BRACKET = 93,
    CIRCUMFLEX_ACCENT = 94,
    LOW_LINE = 95,
    GRAVE_ACCENT = 96,
    LATIN_SMALL_LETTER_A = 97,
    LATIN_SMALL_LETTER_B = 98,
    LATIN_SMALL_LETTER_C = 99,
    LATIN_SMALL_LETTER_D = 100,
    LATIN_SMALL_LETTER_E = 101,
    LATIN_SMALL_LETTER_F = 102,
    LATIN_SMALL_LETTER_G = 103,
    LATIN_SMALL_LETTER_H = 104,
    LATIN_SMALL_LETTER_I = 105,
    LATIN_SMALL_LETTER_J = 106,
    LATIN_SMALL_LETTER_K = 107,
    LATIN_SMALL_LETTER_L = 108,
    LATIN_SMALL_LETTER_M = 109,
    LATIN_SMALL_LETTER_N = 110,
    LATIN_SMALL_LETTER_O = 111,
    LATIN_SMALL_LETTER_P = 112,
    LATIN_SMALL_LETTER_Q = 113,
    LATIN_SMALL_LETTER_R = 114,
    LATIN_SMALL_LETTER_S = 115,
    LATIN_SMALL_LETTER_T = 116,
    LATIN_SMALL_LETTER_U = 117,
    LATIN_SMALL_LETTER_V = 118,
    LATIN_SMALL_LETTER_W = 119,
    LATIN_SMALL_LETTER_X = 120,
    LATIN_SMALL_LETTER_Y = 121,
    LATIN_SMALL_LETTER_Z = 122,
    LEFT_CURLY_BRACKET = 123,
    VERTICAL_LINE = 124,
    RIGHT_CURLY_BRACKET = 125,
    TILDE = 126
}
export declare const escapedSequences: {
    [key: number]: number;
};
