import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, f as createError, r as readBody, q as query, o as logAdminAction, l as logger, e as getClientIP } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const publish_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const newsId = getRouterParam(event, "id");
    if (!newsId || isNaN(parseInt(newsId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u65B0\u95FBID"
      });
    }
    const newsIdNum = parseInt(newsId);
    const body = await readBody(event);
    const {
      action = "publish",
      // publish, unpublish, archive
      publish_date
    } = body;
    const validActions = ["publish", "unpublish", "archive"];
    if (!validActions.includes(action)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u64CD\u4F5C\u7C7B\u578B"
      });
    }
    const existingNews = await query(
      "SELECT id, title, status, content FROM news WHERE id = ?",
      [newsIdNum]
    );
    if (!existingNews || existingNews.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u65B0\u95FB\u4E0D\u5B58\u5728"
      });
    }
    const news = existingNews[0];
    if (action === "publish") {
      if (!news.title || !news.content) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u65B0\u95FB\u6807\u9898\u548C\u5185\u5BB9\u4E0D\u80FD\u4E3A\u7A7A"
        });
      }
    }
    let publishDateTime = null;
    let newStatus = "";
    switch (action) {
      case "publish":
        newStatus = "published";
        if (publish_date) {
          publishDateTime = new Date(publish_date);
          if (isNaN(publishDateTime.getTime())) {
            throw createError({
              statusCode: 400,
              statusMessage: "\u65E0\u6548\u7684\u53D1\u5E03\u65F6\u95F4"
            });
          }
        } else {
          publishDateTime = /* @__PURE__ */ new Date();
        }
        break;
      case "unpublish":
        newStatus = "draft";
        publishDateTime = null;
        break;
      case "archive":
        newStatus = "archived";
        break;
    }
    const updateQuery = action === "archive" ? "UPDATE news SET status = ?, updated_at = NOW() WHERE id = ?" : "UPDATE news SET status = ?, publish_date = ?, updated_at = NOW() WHERE id = ?";
    const updateParams = action === "archive" ? [newStatus, newsIdNum] : [newStatus, publishDateTime, newsIdNum];
    await query(updateQuery, updateParams);
    let message = "";
    switch (action) {
      case "publish":
        message = publishDateTime > /* @__PURE__ */ new Date() ? "\u65B0\u95FB\u5B9A\u65F6\u53D1\u5E03\u8BBE\u7F6E\u6210\u529F" : "\u65B0\u95FB\u53D1\u5E03\u6210\u529F";
        break;
      case "unpublish":
        message = "\u65B0\u95FB\u5DF2\u4E0B\u7EBF";
        break;
      case "archive":
        message = "\u65B0\u95FB\u5DF2\u5F52\u6863";
        break;
    }
    await logAdminAction(admin.id, `news:${action}`, message, {
      newsId: newsIdNum,
      title: news.title,
      oldStatus: news.status,
      newStatus,
      publishDate: publishDateTime
    });
    return {
      success: true,
      message,
      data: {
        id: newsIdNum,
        title: news.title,
        status: newStatus,
        publishDate: publishDateTime
      }
    };
  } catch (error) {
    logger.error("\u65B0\u95FB\u72B6\u6001\u66F4\u65B0\u5931\u8D25", {
      error: error.message,
      stack: error.stack,
      newsId: getRouterParam(event, "id"),
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u65B0\u95FB\u72B6\u6001\u66F4\u65B0\u5931\u8D25"
    });
  }
});

export { publish_post as default };
//# sourceMappingURL=publish.post.mjs.map
