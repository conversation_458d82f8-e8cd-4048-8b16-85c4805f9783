import { c as defineEvent<PERSON>and<PERSON>, g as getQuery, q as query, l as logger, e as getClientIP, f as createError } from '../../../_/nitro.mjs';
import { v as validatePagination } from '../../../_/validators.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const queryParams = getQuery(event);
    const { page, pageSize, offset } = validatePagination(queryParams.page, queryParams.pageSize);
    const {
      status,
      search
    } = queryParams;
    let whereClause = "WHERE 1=1";
    const params = [];
    if (status) {
      whereClause += " AND dfi.status = ?";
      params.push(status);
    }
    if (search) {
      whereClause += " AND (ds.title LIKE ? OR ds.description LIKE ? OR dpt.director LIKE ? OR dpt.producer LIKE ?)";
      params.push(`%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`);
    }
    const countResult = await query(
      `SELECT COUNT(DISTINCT ds.id) as total
       FROM drama_series ds
       LEFT JOIN drama_funding_info dfi ON ds.id = dfi.drama_id
       LEFT JOIN drama_production_team dpt ON ds.id = dpt.drama_id
       ${whereClause}`,
      params
    );
    const total = ((_a = countResult[0]) == null ? void 0 : _a.total) || 0;
    const dramas = await query(
      `SELECT
         ds.id, ds.title, ds.cover, ds.tags, ds.description, ds.episodes, ds.episode_length,
         ds.target_platform, ds.projected_views, ds.cast, ds.is_online, ds.creator_id,
         ds.created_at, ds.updated_at,
         dpt.production_company, dpt.co_production_company, dpt.executive_producer,
         dpt.co_executive_producer, dpt.chief_producer, dpt.producer, dpt.co_producer,
         dpt.director, dpt.scriptwriter, dpt.supervisor, dpt.coordinator,
         dps.schedule_pre_production, dps.schedule_filming, dps.schedule_post_production,
         dps.expected_release_date,
         dfi.funding_goal, dfi.current_funding, dfi.funding_end_date, dfi.funding_share,
         dfi.min_investment, dfi.roi, dfi.status,
         dai.risk_management, dai.confirmed_resources, dai.investment_tiers
       FROM drama_series ds
       LEFT JOIN drama_production_team dpt ON ds.id = dpt.drama_id
       LEFT JOIN drama_production_schedule dps ON ds.id = dps.drama_id
       LEFT JOIN drama_funding_info dfi ON ds.id = dfi.drama_id
       LEFT JOIN drama_additional_info dai ON ds.id = dai.drama_id
       ${whereClause}
       ORDER BY ds.created_at DESC
       LIMIT ? OFFSET ?`,
      [...params, pageSize, offset]
    );
    const formattedDramas = dramas.map((drama) => ({
      id: drama.id,
      title: drama.title,
      cover: drama.cover,
      tags: drama.tags ? JSON.parse(drama.tags) : [],
      description: drama.description,
      episodes: drama.episodes,
      episodeLength: drama.episode_length,
      targetPlatform: drama.target_platform ? JSON.parse(drama.target_platform) : [],
      projectedViews: drama.projected_views,
      cast: drama.cast ? JSON.parse(drama.cast) : [],
      isOnline: drama.is_online,
      creatorId: drama.creator_id,
      createdAt: drama.created_at,
      updatedAt: drama.updated_at,
      // 制作团队信息
      productionTeam: {
        productionCompany: drama.production_company,
        coProductionCompany: drama.co_production_company,
        executiveProducer: drama.executive_producer,
        coExecutiveProducer: drama.co_executive_producer,
        chiefProducer: drama.chief_producer,
        producer: drama.producer,
        coProducer: drama.co_producer,
        director: drama.director,
        scriptwriter: drama.scriptwriter,
        supervisor: drama.supervisor,
        coordinator: drama.coordinator
      },
      // 制作进度信息
      productionSchedule: {
        preProduction: drama.schedule_pre_production,
        filming: drama.schedule_filming,
        postProduction: drama.schedule_post_production,
        expectedReleaseDate: drama.expected_release_date
      },
      // 募资信息
      fundingInfo: {
        fundingGoal: parseFloat(drama.funding_goal) || 0,
        currentFunding: parseFloat(drama.current_funding) || 0,
        fundingEndDate: drama.funding_end_date,
        fundingShare: parseFloat(drama.funding_share) || 0,
        minInvestment: parseFloat(drama.min_investment) || 0,
        roi: parseFloat(drama.roi) || 0,
        status: drama.status || "draft"
      },
      // 其他信息
      additionalInfo: {
        riskManagement: drama.risk_management ? JSON.parse(drama.risk_management) : [],
        confirmedResources: drama.confirmed_resources ? JSON.parse(drama.confirmed_resources) : [],
        investmentTiers: drama.investment_tiers ? JSON.parse(drama.investment_tiers) : []
      }
    }));
    return {
      success: true,
      data: {
        list: formattedDramas,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize)
        }
      }
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u77ED\u5267\u5217\u8868\u5931\u8D25", {
      error: error.message,
      // adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { index_get as default };
//# sourceMappingURL=index.get5.mjs.map
