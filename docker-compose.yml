version: '3.8'

# 为什么用version 3.8：支持最新的Docker Compose特性，兼容性好

services:
  # MySQL数据库服务
  # 为什么独立数据库：数据持久化，便于备份和维护
  mysql:
    image: mysql:8.0
    container_name: reelshort-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: your_root_password_here
      MYSQL_DATABASE: reelshortfund
      MYSQL_USER: reelshort_user
      MYSQL_PASSWORD: your_password_here
    volumes:
      # 数据持久化：将数据存储在宿主机上
      - mysql_data:/var/lib/mysql
      # 初始化脚本：自动导入数据库结构
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    ports:
      - "3306:3306"
    networks:
      - reelshort-network
    # 健康检查：确保数据库完全启动后再启动其他服务
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis缓存服务
  # 为什么需要Redis：提高应用性能，存储会话和缓存
  redis:
    image: redis:7-alpine
    container_name: reelshort-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - reelshort-network
    # Redis配置：启用持久化
    command: redis-server --appendonly yes

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: reelshort-backend
    restart: unless-stopped
    environment:
      # 数据库连接配置
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USER: reelshort_user
      DB_PASSWORD: your_password_here
      DB_NAME: reelshortfund
      # Redis配置
      REDIS_HOST: redis
      REDIS_PORT: 6379
      # 应用配置
      NODE_ENV: production
      PORT: 3001
      # JWT密钥（生产环境请使用强密钥）
      JWT_SECRET: your_jwt_secret_here
      JWT_REFRESH_SECRET: your_jwt_refresh_secret_here
    ports:
      - "3001:3001"
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - reelshort-network
    # 资源限制：防止单个容器占用过多资源
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # 官方网站
  website:
    build:
      context: ./website
      dockerfile: Dockerfile
    container_name: reelshort-website
    restart: unless-stopped
    ports:
      - "3002:80"
    networks:
      - reelshort-network
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

  # 后台管理系统
  fundadmin:
    build:
      context: ./fundAdmin
      dockerfile: Dockerfile
    container_name: reelshort-fundadmin
    restart: unless-stopped
    ports:
      - "3003:80"
    networks:
      - reelshort-network
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

  # 主Nginx代理
  # 为什么需要主代理：统一入口，负载均衡，SSL终止
  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    container_name: reelshort-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
      - website
      - fundadmin
    networks:
      - reelshort-network
    volumes:
      # SSL证书挂载点（如果有SSL证书）
      - ./ssl:/etc/nginx/ssl:ro
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

# 网络配置
# 为什么创建自定义网络：容器间通信，隔离外部网络
networks:
  reelshort-network:
    driver: bridge

# 数据卷配置
# 为什么使用数据卷：数据持久化，容器删除后数据不丢失
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
