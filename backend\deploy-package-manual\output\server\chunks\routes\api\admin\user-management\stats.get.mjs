import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const stats_get = defineEventHandler(async (event) => {
  var _a, _b, _c, _d, _e;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const totalResult = await query("SELECT COUNT(*) as total FROM users");
    const total = ((_a = totalResult[0]) == null ? void 0 : _a.total) || 0;
    const statusResult = await query(`
      SELECT
        status,
        COUNT(*) as count
      FROM users
      GROUP BY status
    `);
    const statusStats = {
      active: 0,
      inactive: 0
    };
    statusResult.forEach((item) => {
      if (item.status === 1) {
        statusStats.active = item.count;
      } else {
        statusStats.inactive = item.count;
      }
    });
    const typeResult = await query(`
      SELECT
        user_type,
        COUNT(*) as count
      FROM users
      GROUP BY user_type
    `);
    const typeStats = {
      investor: 0,
      producer: 0,
      fund_manager: 0
    };
    typeResult.forEach((item) => {
      if (item.user_type in typeStats) {
        typeStats[item.user_type] = item.count;
      }
    });
    const recentResult = await query(`
      SELECT
        DATE(created_at) as date,
        COUNT(*) as count
      FROM users
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `);
    const recentLoginResult = await query(`
      SELECT COUNT(*) as count
      FROM users
      WHERE last_login_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    `);
    const recentActiveUsers = ((_b = recentLoginResult[0]) == null ? void 0 : _b.count) || 0;
    const todayResult = await query(`
      SELECT COUNT(*) as count
      FROM users
      WHERE DATE(created_at) = CURDATE()
    `);
    const todayNew = ((_c = todayResult[0]) == null ? void 0 : _c.count) || 0;
    const monthResult = await query(`
      SELECT COUNT(*) as count
      FROM users
      WHERE YEAR(created_at) = YEAR(NOW())
      AND MONTH(created_at) = MONTH(NOW())
    `);
    const monthNew = ((_d = monthResult[0]) == null ? void 0 : _d.count) || 0;
    await logAuditAction({
      action: "ADMIN_VIEW_USER_STATS",
      description: "\u7BA1\u7406\u5458\u67E5\u770B\u7528\u6237\u7EDF\u8BA1\u6570\u636E",
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: { totalUsers: total }
    });
    return {
      success: true,
      data: {
        // 总体统计
        total,
        todayNew,
        monthNew,
        recentActiveUsers,
        // 状态统计
        statusStats,
        // 类型统计
        typeStats,
        // 最近7天注册趋势
        recentTrend: recentResult.map((item) => ({
          date: item.date,
          count: item.count
        })),
        // 计算增长率（与上月对比）
        growthRate: await calculateGrowthRate()
      }
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u7528\u6237\u7EDF\u8BA1\u5931\u8D25", {
      error: error.message,
      adminId: (_e = event.context.admin) == null ? void 0 : _e.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});
async function calculateGrowthRate() {
  var _a, _b;
  try {
    const thisMonthResult = await query(`
      SELECT COUNT(*) as count
      FROM users
      WHERE YEAR(created_at) = YEAR(NOW())
      AND MONTH(created_at) = MONTH(NOW())
    `);
    const thisMonth = ((_a = thisMonthResult[0]) == null ? void 0 : _a.count) || 0;
    const lastMonthResult = await query(`
      SELECT COUNT(*) as count
      FROM users
      WHERE YEAR(created_at) = YEAR(DATE_SUB(NOW(), INTERVAL 1 MONTH))
      AND MONTH(created_at) = MONTH(DATE_SUB(NOW(), INTERVAL 1 MONTH))
    `);
    const lastMonth = ((_b = lastMonthResult[0]) == null ? void 0 : _b.count) || 0;
    if (lastMonth === 0) {
      return thisMonth > 0 ? 100 : 0;
    }
    return Math.round((thisMonth - lastMonth) / lastMonth * 100);
  } catch (error) {
    logger.error("\u8BA1\u7B97\u589E\u957F\u7387\u5931\u8D25", { error: error.message });
    return 0;
  }
}

export { stats_get as default };
//# sourceMappingURL=stats.get.mjs.map
