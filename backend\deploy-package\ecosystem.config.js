module.exports = {
    apps: [{
      name: 'fundAdmin-backend',
      script: '.output/server/index.mjs',
      cwd: '/www/wwwroot/api.qinghee.com.cn',
      instances: 'max',
      exec_mode: 'cluster',
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      },
    // 日志配置 - 使用服务器路径
    log_file: '/www/wwwroot/api.qinghee.com.cn/logs/combined.log',
    out_file: '/www/wwwroot/api.qinghee.com.cn/logs/out.log',
    error_file: '/www/wwwroot/api.qinghee.com.cn/logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    
    min_uptime: '10s',
    max_restarts: 10,
    restart_delay: 4000
    }]
  };