import { c as defineEvent<PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, f as createError, q as query, r as readBody, m as transaction, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__put = defineEventHandler(async (event) => {
  var _a;
  try {
    const dramaId = getRouterParam(event, "id");
    if (!dramaId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u77ED\u5267ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const id = parseInt(dramaId);
    if (isNaN(id) || id <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u77ED\u5267ID"
      });
    }
    const existingDrama = await query(`
      SELECT id FROM drama_series WHERE id = ?
    `, [id]);
    if (existingDrama.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u77ED\u5267\u4E0D\u5B58\u5728"
      });
    }
    const body = await readBody(event);
    const {
      // 基础信息
      title,
      cover,
      tags,
      description,
      episodes,
      episodeLength,
      targetPlatform,
      projectedViews,
      cast,
      isOnline,
      // 制作团队信息
      productionTeam,
      // 制作进度信息
      productionSchedule,
      // 募资信息
      fundingInfo,
      // 其他信息
      additionalInfo
    } = body;
    if (episodes !== void 0 && (typeof episodes !== "number" || episodes <= 0)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u96C6\u6570\u5FC5\u987B\u4E3A\u6B63\u6574\u6570"
      });
    }
    if (episodeLength !== void 0 && (typeof episodeLength !== "number" || episodeLength <= 0)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5355\u96C6\u65F6\u957F\u5FC5\u987B\u4E3A\u6B63\u6570"
      });
    }
    if (fundingInfo) {
      if (fundingInfo.fundingGoal !== void 0 && (typeof fundingInfo.fundingGoal !== "number" || fundingInfo.fundingGoal <= 0)) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u52DF\u8D44\u76EE\u6807\u5FC5\u987B\u4E3A\u6B63\u6570"
        });
      }
      if (fundingInfo.currentFunding !== void 0 && (typeof fundingInfo.currentFunding !== "number" || fundingInfo.currentFunding < 0)) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u5F53\u524D\u52DF\u8D44\u91D1\u989D\u5FC5\u987B\u4E3A\u975E\u8D1F\u6570"
        });
      }
      if (fundingInfo.minInvestment !== void 0 && (typeof fundingInfo.minInvestment !== "number" || fundingInfo.minInvestment <= 0)) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u6700\u5C0F\u6295\u8D44\u91D1\u989D\u5FC5\u987B\u4E3A\u6B63\u6570"
        });
      }
      if (fundingInfo.status !== void 0) {
        const validStatuses = ["draft", "published", "ended", "archived"];
        if (!validStatuses.includes(fundingInfo.status)) {
          throw createError({
            statusCode: 400,
            statusMessage: "\u65E0\u6548\u7684\u72B6\u6001\u503C"
          });
        }
      }
    }
    await transaction(async (connection) => {
      const baseUpdateFields = [];
      const baseUpdateValues = [];
      if (title !== void 0) {
        baseUpdateFields.push("title = ?");
        baseUpdateValues.push(title);
      }
      if (cover !== void 0) {
        baseUpdateFields.push("cover = ?");
        baseUpdateValues.push(cover);
      }
      if (tags !== void 0) {
        baseUpdateFields.push("tags = ?");
        baseUpdateValues.push(JSON.stringify(tags));
      }
      if (description !== void 0) {
        baseUpdateFields.push("description = ?");
        baseUpdateValues.push(description);
      }
      if (episodes !== void 0) {
        baseUpdateFields.push("episodes = ?");
        baseUpdateValues.push(episodes);
      }
      if (episodeLength !== void 0) {
        baseUpdateFields.push("episode_length = ?");
        baseUpdateValues.push(episodeLength);
      }
      if (targetPlatform !== void 0) {
        baseUpdateFields.push("target_platform = ?");
        baseUpdateValues.push(JSON.stringify(targetPlatform));
      }
      if (projectedViews !== void 0) {
        baseUpdateFields.push("projected_views = ?");
        baseUpdateValues.push(projectedViews);
      }
      if (cast !== void 0) {
        baseUpdateFields.push("cast = ?");
        baseUpdateValues.push(JSON.stringify(cast));
      }
      if (isOnline !== void 0) {
        baseUpdateFields.push("is_online = ?");
        baseUpdateValues.push(isOnline);
      }
      if (baseUpdateFields.length > 0) {
        baseUpdateFields.push("updated_at = CURRENT_TIMESTAMP");
        baseUpdateValues.push(id);
        await connection.execute(`
          UPDATE drama_series
          SET ${baseUpdateFields.join(", ")}
          WHERE id = ?
        `, baseUpdateValues);
      }
      if (productionTeam && Object.keys(productionTeam).length > 0) {
        const teamUpdateFields = [];
        const teamUpdateValues = [];
        if (productionTeam.productionCompany !== void 0) {
          teamUpdateFields.push("production_company = ?");
          teamUpdateValues.push(productionTeam.productionCompany);
        }
        if (productionTeam.coProductionCompany !== void 0) {
          teamUpdateFields.push("co_production_company = ?");
          teamUpdateValues.push(productionTeam.coProductionCompany);
        }
        if (productionTeam.executiveProducer !== void 0) {
          teamUpdateFields.push("executive_producer = ?");
          teamUpdateValues.push(productionTeam.executiveProducer);
        }
        if (productionTeam.coExecutiveProducer !== void 0) {
          teamUpdateFields.push("co_executive_producer = ?");
          teamUpdateValues.push(productionTeam.coExecutiveProducer);
        }
        if (productionTeam.chiefProducer !== void 0) {
          teamUpdateFields.push("chief_producer = ?");
          teamUpdateValues.push(productionTeam.chiefProducer);
        }
        if (productionTeam.producer !== void 0) {
          teamUpdateFields.push("producer = ?");
          teamUpdateValues.push(productionTeam.producer);
        }
        if (productionTeam.coProducer !== void 0) {
          teamUpdateFields.push("co_producer = ?");
          teamUpdateValues.push(productionTeam.coProducer);
        }
        if (productionTeam.director !== void 0) {
          teamUpdateFields.push("director = ?");
          teamUpdateValues.push(productionTeam.director);
        }
        if (productionTeam.scriptwriter !== void 0) {
          teamUpdateFields.push("scriptwriter = ?");
          teamUpdateValues.push(productionTeam.scriptwriter);
        }
        if (productionTeam.supervisor !== void 0) {
          teamUpdateFields.push("supervisor = ?");
          teamUpdateValues.push(productionTeam.supervisor);
        }
        if (productionTeam.coordinator !== void 0) {
          teamUpdateFields.push("coordinator = ?");
          teamUpdateValues.push(productionTeam.coordinator);
        }
        if (teamUpdateFields.length > 0) {
          teamUpdateFields.push("updated_at = CURRENT_TIMESTAMP");
          teamUpdateValues.push(id);
          const teamExists = await connection.execute(
            "SELECT id FROM drama_production_team WHERE drama_id = ?",
            [id]
          );
          if (teamExists[0].length > 0) {
            await connection.execute(`
              UPDATE drama_production_team
              SET ${teamUpdateFields.join(", ")}
              WHERE drama_id = ?
            `, teamUpdateValues);
          } else {
            const insertFields = ["drama_id"];
            const insertValues = [id];
            const insertPlaceholders = ["?"];
            teamUpdateFields.forEach((field, index) => {
              if (field !== "updated_at = CURRENT_TIMESTAMP") {
                const fieldName = field.split(" = ")[0];
                insertFields.push(fieldName);
                insertValues.push(teamUpdateValues[index]);
                insertPlaceholders.push("?");
              }
            });
            await connection.execute(`
              INSERT INTO drama_production_team (${insertFields.join(", ")})
              VALUES (${insertPlaceholders.join(", ")})
            `, insertValues);
          }
        }
      }
      if (productionSchedule && Object.keys(productionSchedule).length > 0) {
        const scheduleUpdateFields = [];
        const scheduleUpdateValues = [];
        if (productionSchedule.preProduction !== void 0) {
          scheduleUpdateFields.push("schedule_pre_production = ?");
          scheduleUpdateValues.push(productionSchedule.preProduction);
        }
        if (productionSchedule.filming !== void 0) {
          scheduleUpdateFields.push("schedule_filming = ?");
          scheduleUpdateValues.push(productionSchedule.filming);
        }
        if (productionSchedule.postProduction !== void 0) {
          scheduleUpdateFields.push("schedule_post_production = ?");
          scheduleUpdateValues.push(productionSchedule.postProduction);
        }
        if (productionSchedule.expectedReleaseDate !== void 0) {
          scheduleUpdateFields.push("expected_release_date = ?");
          scheduleUpdateValues.push(productionSchedule.expectedReleaseDate);
        }
        if (scheduleUpdateFields.length > 0) {
          scheduleUpdateFields.push("updated_at = CURRENT_TIMESTAMP");
          scheduleUpdateValues.push(id);
          const scheduleExists = await connection.execute(
            "SELECT id FROM drama_production_schedule WHERE drama_id = ?",
            [id]
          );
          if (scheduleExists[0].length > 0) {
            await connection.execute(`
              UPDATE drama_production_schedule
              SET ${scheduleUpdateFields.join(", ")}
              WHERE drama_id = ?
            `, scheduleUpdateValues);
          } else {
            const insertFields = ["drama_id"];
            const insertValues = [id];
            const insertPlaceholders = ["?"];
            scheduleUpdateFields.forEach((field, index) => {
              if (field !== "updated_at = CURRENT_TIMESTAMP") {
                const fieldName = field.split(" = ")[0];
                insertFields.push(fieldName);
                insertValues.push(scheduleUpdateValues[index]);
                insertPlaceholders.push("?");
              }
            });
            await connection.execute(`
              INSERT INTO drama_production_schedule (${insertFields.join(", ")})
              VALUES (${insertPlaceholders.join(", ")})
            `, insertValues);
          }
        }
      }
      if (fundingInfo && Object.keys(fundingInfo).length > 0) {
        const fundingUpdateFields = [];
        const fundingUpdateValues = [];
        if (fundingInfo.fundingGoal !== void 0) {
          fundingUpdateFields.push("funding_goal = ?");
          fundingUpdateValues.push(fundingInfo.fundingGoal);
        }
        if (fundingInfo.currentFunding !== void 0) {
          fundingUpdateFields.push("current_funding = ?");
          fundingUpdateValues.push(fundingInfo.currentFunding);
        }
        if (fundingInfo.fundingEndDate !== void 0) {
          fundingUpdateFields.push("funding_end_date = ?");
          fundingUpdateValues.push(fundingInfo.fundingEndDate);
        }
        if (fundingInfo.fundingShare !== void 0) {
          fundingUpdateFields.push("funding_share = ?");
          fundingUpdateValues.push(fundingInfo.fundingShare);
        }
        if (fundingInfo.minInvestment !== void 0) {
          fundingUpdateFields.push("min_investment = ?");
          fundingUpdateValues.push(fundingInfo.minInvestment);
        }
        if (fundingInfo.expectedReturn !== void 0) {
          fundingUpdateFields.push("expected_return = ?");
          fundingUpdateValues.push(fundingInfo.expectedReturn);
        }
        if (fundingInfo.roi !== void 0) {
          fundingUpdateFields.push("roi = ?");
          fundingUpdateValues.push(fundingInfo.roi);
        }
        if (fundingInfo.status !== void 0) {
          fundingUpdateFields.push("status = ?");
          fundingUpdateValues.push(fundingInfo.status);
        }
        if (fundingUpdateFields.length > 0) {
          fundingUpdateFields.push("updated_at = CURRENT_TIMESTAMP");
          fundingUpdateValues.push(id);
          const fundingExists = await connection.execute(
            "SELECT id FROM drama_funding_info WHERE drama_id = ?",
            [id]
          );
          if (fundingExists[0].length > 0) {
            await connection.execute(`
              UPDATE drama_funding_info
              SET ${fundingUpdateFields.join(", ")}
              WHERE drama_id = ?
            `, fundingUpdateValues);
          } else {
            const insertFields = ["drama_id"];
            const insertValues = [id];
            const insertPlaceholders = ["?"];
            fundingUpdateFields.forEach((field, index) => {
              if (field !== "updated_at = CURRENT_TIMESTAMP") {
                const fieldName = field.split(" = ")[0];
                insertFields.push(fieldName);
                insertValues.push(fundingUpdateValues[index]);
                insertPlaceholders.push("?");
              }
            });
            await connection.execute(`
              INSERT INTO drama_funding_info (${insertFields.join(", ")})
              VALUES (${insertPlaceholders.join(", ")})
            `, insertValues);
          }
        }
      }
      if (additionalInfo && Object.keys(additionalInfo).length > 0) {
        const additionalUpdateFields = [];
        const additionalUpdateValues = [];
        if (additionalInfo.riskManagement !== void 0) {
          additionalUpdateFields.push("risk_management = ?");
          additionalUpdateValues.push(JSON.stringify(additionalInfo.riskManagement));
        }
        if (additionalInfo.confirmedResources !== void 0) {
          additionalUpdateFields.push("confirmed_resources = ?");
          additionalUpdateValues.push(JSON.stringify(additionalInfo.confirmedResources));
        }
        if (additionalInfo.investmentTiers !== void 0) {
          additionalUpdateFields.push("investment_tiers = ?");
          additionalUpdateValues.push(JSON.stringify(additionalInfo.investmentTiers));
        }
        if (additionalUpdateFields.length > 0) {
          additionalUpdateFields.push("updated_at = CURRENT_TIMESTAMP");
          additionalUpdateValues.push(id);
          const additionalExists = await connection.execute(
            "SELECT id FROM drama_additional_info WHERE drama_id = ?",
            [id]
          );
          if (additionalExists[0].length > 0) {
            await connection.execute(`
              UPDATE drama_additional_info
              SET ${additionalUpdateFields.join(", ")}
              WHERE drama_id = ?
            `, additionalUpdateValues);
          } else {
            const insertFields = ["drama_id"];
            const insertValues = [id];
            const insertPlaceholders = ["?"];
            additionalUpdateFields.forEach((field, index) => {
              if (field !== "updated_at = CURRENT_TIMESTAMP") {
                const fieldName = field.split(" = ")[0];
                insertFields.push(fieldName);
                insertValues.push(additionalUpdateValues[index]);
                insertPlaceholders.push("?");
              }
            });
            await connection.execute(`
              INSERT INTO drama_additional_info (${insertFields.join(", ")})
              VALUES (${insertPlaceholders.join(", ")})
            `, insertValues);
          }
        }
      }
      return true;
    });
    logger.info("\u66F4\u65B0\u77ED\u5267\u6210\u529F", {
      // adminId: admin.id,
      dramaId: id,
      title: title || "unchanged",
      ip: getClientIP(event)
    });
    return {
      success: true,
      data: {
        id,
        message: "\u77ED\u5267\u66F4\u65B0\u6210\u529F"
      }
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u77ED\u5267\u5931\u8D25", {
      error: error.message,
      dramaId: getRouterParam(event, "id"),
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { _id__put as default };
//# sourceMappingURL=_id_.put.mjs.map
