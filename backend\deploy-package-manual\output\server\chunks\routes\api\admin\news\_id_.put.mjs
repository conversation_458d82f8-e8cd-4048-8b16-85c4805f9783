import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, f as createError, q as query, r as readBody, m as transaction, o as logAdminAction, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__put = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const newsId = getRouterParam(event, "id");
    if (!newsId || isNaN(parseInt(newsId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u65B0\u95FBID"
      });
    }
    const newsIdNum = parseInt(newsId);
    const existingNews = await query(
      "SELECT id, title, status FROM news WHERE id = ?",
      [newsIdNum]
    );
    if (!existingNews || existingNews.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u65B0\u95FB\u4E0D\u5B58\u5728"
      });
    }
    const body = await readBody(event);
    const {
      title,
      summary,
      content,
      cover_image_url,
      category_id,
      author,
      source_url,
      status,
      is_featured,
      publish_date,
      tags = [],
      seo
    } = body;
    if (title !== void 0 && (!title || typeof title !== "string" || title.trim().length === 0)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65B0\u95FB\u6807\u9898\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    if (content !== void 0 && (!content || typeof content !== "string" || content.trim().length === 0)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65B0\u95FB\u5185\u5BB9\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    if (status !== void 0) {
      const validStatuses = ["draft", "pending", "published", "archived"];
      if (!validStatuses.includes(status)) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u65E0\u6548\u7684\u65B0\u95FB\u72B6\u6001"
        });
      }
    }
    if (category_id !== void 0 && category_id !== null && !isNaN(parseInt(category_id))) {
      const categoryCheck = await query(
        "SELECT id FROM news_categories WHERE id = ? AND is_active = 1",
        [parseInt(category_id)]
      );
      if (!categoryCheck || categoryCheck.length === 0) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u65E0\u6548\u7684\u65B0\u95FB\u5206\u7C7B"
        });
      }
    }
    let publishDateTime = void 0;
    if (publish_date !== void 0) {
      if (publish_date === null) {
        publishDateTime = null;
      } else {
        publishDateTime = new Date(publish_date);
        if (isNaN(publishDateTime.getTime())) {
          throw createError({
            statusCode: 400,
            statusMessage: "\u65E0\u6548\u7684\u53D1\u5E03\u65F6\u95F4"
          });
        }
      }
    }
    await transaction(async (connection) => {
      const updateFields = [];
      const updateValues = [];
      if (title !== void 0) {
        updateFields.push("title = ?");
        updateValues.push(title.trim());
      }
      if (summary !== void 0) {
        updateFields.push("summary = ?");
        updateValues.push(summary ? summary.trim() : null);
      }
      if (content !== void 0) {
        updateFields.push("content = ?");
        updateValues.push(content.trim());
      }
      if (cover_image_url !== void 0) {
        updateFields.push("cover_image_url = ?");
        updateValues.push(cover_image_url || null);
      }
      if (category_id !== void 0) {
        updateFields.push("category_id = ?");
        updateValues.push(category_id ? parseInt(category_id) : null);
      }
      if (author !== void 0) {
        updateFields.push("author = ?");
        updateValues.push(author ? author.trim() : null);
      }
      if (source_url !== void 0) {
        updateFields.push("source_url = ?");
        updateValues.push(source_url || null);
      }
      if (status !== void 0) {
        updateFields.push("status = ?");
        updateValues.push(status);
        if (status === "published" && publishDateTime === void 0) {
          updateFields.push("publish_date = ?");
          updateValues.push(/* @__PURE__ */ new Date());
        }
      }
      if (is_featured !== void 0) {
        updateFields.push("is_featured = ?");
        updateValues.push(is_featured ? 1 : 0);
      }
      if (publishDateTime !== void 0) {
        updateFields.push("publish_date = ?");
        updateValues.push(publishDateTime);
      }
      updateFields.push("updated_at = NOW()");
      if (updateFields.length > 0) {
        updateValues.push(newsIdNum);
        const updateQuery = `
          UPDATE news 
          SET ${updateFields.join(", ")} 
          WHERE id = ?
        `;
        await connection.execute(updateQuery, updateValues);
      }
      if (tags !== void 0 && Array.isArray(tags)) {
        await connection.execute(
          "DELETE FROM news_tag_relations WHERE news_id = ?",
          [newsIdNum]
        );
        for (const tagName of tags) {
          if (typeof tagName === "string" && tagName.trim()) {
            let tagId;
            const existingTag = await connection.execute(
              "SELECT id FROM news_tags WHERE name = ?",
              [tagName.trim()]
            );
            if (existingTag[0].length > 0) {
              tagId = existingTag[0][0].id;
            } else {
              const tagResult = await connection.execute(
                "INSERT INTO news_tags (name) VALUES (?)",
                [tagName.trim()]
              );
              tagId = tagResult.insertId;
            }
            await connection.execute(
              "INSERT INTO news_tag_relations (news_id, tag_id) VALUES (?, ?)",
              [newsIdNum, tagId]
            );
          }
        }
      }
      if (seo !== void 0 && typeof seo === "object") {
        const {
          meta_title,
          meta_description,
          meta_keywords,
          canonical_url,
          og_title,
          og_description,
          og_image
        } = seo;
        const existingSeo = await connection.execute(
          "SELECT news_id FROM news_seo WHERE news_id = ?",
          [newsIdNum]
        );
        if (existingSeo[0].length > 0) {
          await connection.execute(
            `UPDATE news_seo SET 
              meta_title = ?, meta_description = ?, meta_keywords = ?,
              canonical_url = ?, og_title = ?, og_description = ?, og_image = ?,
              updated_at = NOW()
            WHERE news_id = ?`,
            [
              meta_title || null,
              meta_description || null,
              meta_keywords || null,
              canonical_url || null,
              og_title || null,
              og_description || null,
              og_image || null,
              newsIdNum
            ]
          );
        } else {
          await connection.execute(
            `INSERT INTO news_seo (
              news_id, meta_title, meta_description, meta_keywords,
              canonical_url, og_title, og_description, og_image
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              newsIdNum,
              meta_title || null,
              meta_description || null,
              meta_keywords || null,
              canonical_url || null,
              og_title || null,
              og_description || null,
              og_image || null
            ]
          );
        }
      }
    });
    await logAdminAction(admin.id, "news:update", "\u66F4\u65B0\u65B0\u95FB", {
      newsId: newsIdNum,
      title: title || existingNews[0].title,
      changes: Object.keys(body)
    });
    return {
      success: true,
      message: "\u65B0\u95FB\u66F4\u65B0\u6210\u529F",
      data: {
        id: newsIdNum
      }
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u65B0\u95FB\u5931\u8D25", {
      error: error.message,
      stack: error.stack,
      newsId: getRouterParam(event, "id"),
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u66F4\u65B0\u65B0\u95FB\u5931\u8D25"
    });
  }
});

export { _id__put as default };
//# sourceMappingURL=_id_.put.mjs.map
