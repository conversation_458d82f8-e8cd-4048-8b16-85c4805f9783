import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, g as getQuery, q as query, f as createError } from '../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const rechargeRecords_get = defineEventHandler(async (event) => {
  var _a;
  try {
    console.log("\u5145\u503C\u8BB0\u5F55API\u88AB\u8C03\u7528");
    const queryParams = getQuery(event);
    const {
      page = 1,
      pageSize = 20,
      keyword = "",
      status = "",
      startDate = "",
      endDate = ""
    } = queryParams;
    const offset = (Number(page) - 1) * Number(pageSize);
    let whereConditions = [`uat.transaction_type = 'shells_in'`, `uat.related_type = 'recharge'`];
    let queryParams_array = [];
    if (keyword) {
      whereConditions.push(`(u.username LIKE ? OR uat.transaction_no LIKE ?)`);
      queryParams_array.push(`%${keyword}%`, `%${keyword}%`);
    }
    if (status) {
      whereConditions.push(`uat.status = ?`);
      queryParams_array.push(status);
    }
    if (startDate) {
      whereConditions.push(`DATE(uat.created_at) >= ?`);
      queryParams_array.push(startDate);
    }
    if (endDate) {
      whereConditions.push(`DATE(uat.created_at) <= ?`);
      queryParams_array.push(endDate);
    }
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(" AND ")}` : "";
    const countQuery = `
      SELECT COUNT(*) as total
      FROM user_asset_transactions uat
      LEFT JOIN users u ON uat.user_id = u.id
      ${whereClause}
    `;
    const countResult = await query(countQuery, queryParams_array);
    const total = ((_a = countResult[0]) == null ? void 0 : _a.total) || 0;
    const listQuery = `
      SELECT 
        uat.id,
        uat.user_id,
        u.username,
        uat.transaction_type,
        uat.amount,
        uat.balance_before,
        uat.balance_after,
        uat.related_id,
        uat.related_type,
        uat.description,
        uat.transaction_no,
        uat.status,
        DATE_FORMAT(uat.created_at, '%Y-%m-%d %H:%i:%s') as created_at,
        DATE_FORMAT(uat.updated_at, '%Y-%m-%d %H:%i:%s') as updated_at
      FROM user_asset_transactions uat
      LEFT JOIN users u ON uat.user_id = u.id
      ${whereClause}
      ORDER BY uat.created_at DESC
      LIMIT ? OFFSET ?
    `;
    const records = await query(listQuery, [...queryParams_array, Number(pageSize), offset]);
    return {
      success: true,
      data: {
        result: records,
        total,
        page: Number(page),
        pageSize: Number(pageSize),
        totalPages: Math.ceil(total / Number(pageSize))
      }
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u5145\u503C\u8BB0\u5F55\u5217\u8868\u5931\u8D25:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "\u83B7\u53D6\u5145\u503C\u8BB0\u5F55\u5217\u8868\u5931\u8D25"
    });
  }
});

export { rechargeRecords_get as default };
//# sourceMappingURL=recharge-records.get.mjs.map
