# 前端开发最佳实践总结

## 🚨 常见问题与解决方案

### 1. 图标导入冲突问题

#### ❌ 错误做法
```typescript
// 会导致命名冲突
import { Upload } from '@vben/icons';
import { Upload } from 'ant-design-vue';
```

#### ✅ 正确做法
```typescript
// 方案1：使用 IconifyIcon 组件
import { IconifyIcon } from '@vben/icons';

// 在模板中使用
<template #icon>
  <IconifyIcon icon="ant-design:upload-outlined" class="text-base" />
</template>

// 方案2：使用别名导入
import { Upload as UploadIcon } from '@vben/icons';
import { Upload } from 'ant-design-vue';

// 方案3：分别导入
import { Upload } from 'ant-design-vue';
// 使用 IconifyIcon 代替直接导入图标
```

### 2. 组件导入规范

#### ✅ 推荐的导入顺序
```typescript
// 1. Vue 相关
import { ref, reactive, watch, computed } from 'vue';

// 2. UI 组件库
import {
  Form,
  FormItem,
  Input,
  Button,
  message,
} from 'ant-design-vue';

// 3. 项目图标
import { IconifyIcon, Plus } from '@vben/icons';

// 4. 项目内部模块
import { updateDrama } from '#/api/drama-management';
import { requestClient } from '#/api/request';

// 5. 类型定义
import type { DramaManagementApi } from '#/api/drama-management';
```

### 3. 模板语法规范

#### ✅ 正确的模板结构
```vue
<template>
  <div class="settings-container">
    <!-- 头部操作区 -->
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-medium text-gray-900">标题</h3>
      <div class="flex items-center space-x-2">
        <Button @click="handleReset">重置</Button>
        <Button type="primary" :loading="saving" @click="handleSave">保存</Button>
      </div>
    </div>

    <!-- 表单区域 -->
    <Form
      :model="formData"
      :rules="rules"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
      layout="horizontal"
      @finish="handleSave"
    >
      <!-- 表单项 -->
    </Form>
  </div>
</template>
```

### 4. 图标使用最佳实践

#### ✅ 推荐使用 IconifyIcon
```vue
<!-- 上传图标 -->
<IconifyIcon icon="ant-design:upload-outlined" class="text-base" />

<!-- 加载图标 -->
<IconifyIcon icon="ant-design:loading-outlined" class="text-2xl text-primary" />

<!-- 加号图标 -->
<Plus class="size-4" />
```

#### 📚 常用图标名称
- `ant-design:upload-outlined` - 上传
- `ant-design:loading-outlined` - 加载
- `ant-design:plus-outlined` - 加号
- `ant-design:delete-outlined` - 删除
- `ant-design:edit-outlined` - 编辑
- `ant-design:eye-outlined` - 查看
- `ant-design:save-outlined` - 保存

### 5. 样式类名规范

#### ✅ 推荐的样式类名
```vue
<!-- 容器类 -->
<div class="settings-container">
<div class="card-box p-6">

<!-- 布局类 -->
<div class="flex items-center justify-between mb-6">
<div class="grid grid-cols-1 md:grid-cols-2 gap-4">

<!-- 间距类 -->
<div class="space-x-2">
<div class="space-y-4">

<!-- 文字类 -->
<h3 class="text-lg font-medium text-gray-900">
<p class="text-sm text-gray-500">

<!-- 禁用状态 -->
<Input disabled class="input-disabled" />
```

### 6. 事件处理规范

#### ✅ 正确的事件命名
```typescript
// 处理函数命名：handle + 动作
const handleSave = async () => { /* ... */ };
const handleReset = () => { /* ... */ };
const handleUpload = async (file: File) => { /* ... */ };
const handleDelete = async (id: number) => { /* ... */ };

// 事件发射命名：动作 + 结果
emit('saveSuccess');
emit('dataChanged', hasChanges);
emit('uploadComplete', result);
```

### 7. 响应式数据规范

#### ✅ 推荐的数据结构
```typescript
// 基础状态
const loading = ref(false);
const saving = ref(false);
const uploading = ref(false);
const error = ref('');

// 表单数据使用 reactive
const formData = reactive({
  title: '',
  description: '',
  // ...
});

// 计算属性
const hasDataChanged = computed(() => {
  // 比较逻辑
});
```

### 8. 错误处理规范

#### ✅ 统一的错误处理
```typescript
const handleSave = async () => {
  try {
    saving.value = true;
    
    await updateDrama(props.drama.id, formData);
    emit('saveSuccess');
  } catch (error: any) {
    message.error(error.message || '保存失败');
  } finally {
    saving.value = false;
  }
};
```

### 9. 文件上传规范

#### ✅ 标准的文件上传实现
```typescript
// 上传前验证
const beforeUpload = (file: File) => {
  const isValidType = file.type.startsWith('image/');
  if (!isValidType) {
    message.error('只能上传图片文件！');
    return false;
  }
  
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('图片大小不能超过 2MB！');
    return false;
  }
  
  return false; // 阻止自动上传
};

// 自定义上传
const handleUpload = async ({ file }: { file: File }) => {
  try {
    uploading.value = true;
    
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await requestClient.post('/admin/upload/common', formData);
    
    if (response && response.url) {
      // 处理成功结果
      message.success('上传成功');
    }
  } catch (error: any) {
    message.error(error.message || '上传失败');
  } finally {
    uploading.value = false;
  }
};
```

## 🔍 调试技巧

### 1. 控制台错误分析
- `Identifier 'XXX' has already been declared` → 导入冲突，使用别名或 IconifyIcon
- `Failed to fetch dynamically imported module` → 通常是组件内部语法错误导致
- `Cannot resolve module` → 检查导入路径和文件是否存在

### 2. 开发工具使用
- 使用 Vue DevTools 检查组件状态
- 使用浏览器网络面板检查 API 调用
- 使用控制台查看详细错误信息

### 3. 常见修复步骤
1. 检查导入语句是否正确
2. 检查组件名称是否冲突
3. 检查模板语法是否正确
4. 检查 TypeScript 类型是否匹配
5. 重启开发服务器

## 📝 代码检查清单

- [ ] 导入语句按顺序排列
- [ ] 没有命名冲突
- [ ] 图标使用 IconifyIcon 或正确的别名
- [ ] 事件处理函数命名规范
- [ ] 错误处理完整
- [ ] TypeScript 类型正确
- [ ] 样式类名规范
- [ ] 响应式数据结构合理
