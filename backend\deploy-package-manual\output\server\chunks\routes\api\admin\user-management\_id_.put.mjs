import { c as defineEvent<PERSON><PERSON><PERSON>, f as createError, j as getRouter<PERSON>aram, r as readBody, q as query, y as hashPassword, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import { c as validateId } from '../../../../_/validators.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__put = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.USER_EDIT);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u7F16\u8F91\u7528\u6237"
      });
    }
    const userId = validateId(getRouterParam(event, "id"));
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u7528\u6237ID"
      });
    }
    const body = await readBody(event);
    const {
      username,
      email,
      phone,
      password,
      userType,
      realName,
      companyName,
      idCard,
      businessLicense,
      status
    } = body;
    const users = await query(
      "SELECT id, username, email, phone FROM users WHERE id = ?",
      [userId]
    );
    if (users.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u7528\u6237\u4E0D\u5B58\u5728"
      });
    }
    const currentUser = users[0];
    if (username || email || phone) {
      const conflictUsers = await query(
        `SELECT id FROM users WHERE
         (username = ? OR email = ? OR (phone IS NOT NULL AND phone = ?))
         AND id != ?`,
        [username || "", email || "", phone || "", userId]
      );
      if (conflictUsers.length > 0) {
        throw createError({
          statusCode: 409,
          statusMessage: "\u7528\u6237\u540D\u3001\u90AE\u7BB1\u6216\u624B\u673A\u53F7\u5DF2\u88AB\u5176\u4ED6\u7528\u6237\u4F7F\u7528"
        });
      }
    }
    if (userType && !["investor", "producer", "fund_manager"].includes(userType)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u7528\u6237\u7C7B\u578B"
      });
    }
    const updateFields = [];
    const updateValues = [];
    if (username !== void 0) {
      updateFields.push("username = ?");
      updateValues.push(username);
    }
    if (email !== void 0) {
      updateFields.push("email = ?");
      updateValues.push(email);
    }
    if (phone !== void 0) {
      updateFields.push("phone = ?");
      updateValues.push(phone || null);
    }
    if (password !== void 0 && password !== "") {
      const hashedPassword = await hashPassword(password);
      updateFields.push("password_hash = ?");
      updateValues.push(hashedPassword);
    }
    if (userType !== void 0) {
      updateFields.push("user_type = ?");
      updateValues.push(userType);
    }
    if (realName !== void 0) {
      updateFields.push("real_name = ?");
      updateValues.push(realName || null);
    }
    if (companyName !== void 0) {
      updateFields.push("company_name = ?");
      updateValues.push(companyName || null);
    }
    if (idCard !== void 0) {
      updateFields.push("id_card = ?");
      updateValues.push(idCard || null);
    }
    if (businessLicense !== void 0) {
      updateFields.push("business_license = ?");
      updateValues.push(businessLicense || null);
    }
    if (status !== void 0) {
      updateFields.push("status = ?");
      let statusValue;
      if (typeof status === "string") {
        statusValue = status === "active" ? 1 : 0;
      } else {
        statusValue = status ? 1 : 0;
      }
      updateValues.push(statusValue);
    }
    if (updateFields.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6CA1\u6709\u63D0\u4F9B\u8981\u66F4\u65B0\u7684\u5B57\u6BB5"
      });
    }
    updateFields.push("updated_at = NOW()");
    updateValues.push(userId);
    await query(
      `UPDATE users SET ${updateFields.join(", ")} WHERE id = ?`,
      updateValues
    );
    await logAuditAction({
      action: "ADMIN_UPDATE_USER",
      description: `\u7BA1\u7406\u5458\u66F4\u65B0\u7528\u6237\u4FE1\u606F: ${currentUser.username}`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        targetUserId: userId,
        targetUsername: currentUser.username,
        updatedFields: Object.keys(body)
      }
    });
    logger.info("\u7BA1\u7406\u5458\u66F4\u65B0\u7528\u6237\u6210\u529F", {
      adminId: admin.id,
      targetUserId: userId,
      targetUsername: currentUser.username,
      updatedFields: Object.keys(body),
      ip: getClientIP(event)
    });
    return {
      success: true,
      message: "\u7528\u6237\u4FE1\u606F\u66F4\u65B0\u6210\u529F"
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u7528\u6237\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      userId: getRouterParam(event, "id"),
      ip: getClientIP(event)
    });
    throw error;
  }
});

export { _id__put as default };
//# sourceMappingURL=_id_.put.mjs.map
