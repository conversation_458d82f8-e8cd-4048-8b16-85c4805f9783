import { c as defineE<PERSON><PERSON><PERSON><PERSON>, f as createError, r as readBody, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_post = defineEventHandler(async (event) => {
  var _a, _b;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u521B\u5EFA\u6F14\u5458"
      });
    }
    const body = await readBody(event);
    const { name, avatar_url, bio, tags, sort_order, is_active } = body;
    if (!name) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6F14\u5458\u59D3\u540D\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    if (name.length > 100) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6F14\u5458\u59D3\u540D\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7100\u4E2A\u5B57\u7B26"
      });
    }
    const isActiveValue = is_active !== void 0 ? is_active ? 1 : 0 : 1;
    const sortOrderValue = sort_order ? parseInt(sort_order) : 0;
    let finalSortOrder = sortOrderValue;
    if (finalSortOrder === 0) {
      const maxSortResult = await query("SELECT MAX(sort_order) as max_sort FROM actors");
      finalSortOrder = (((_a = maxSortResult[0]) == null ? void 0 : _a.max_sort) || 0) + 1;
    }
    const result = await query(
      `INSERT INTO actors (name, avatar_url, bio, tags, sort_order, is_active, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [
        name.trim(),
        (avatar_url == null ? void 0 : avatar_url.trim()) || null,
        (bio == null ? void 0 : bio.trim()) || null,
        (tags == null ? void 0 : tags.trim()) || null,
        finalSortOrder,
        isActiveValue
      ]
    );
    const newActorId = result.insertId;
    await logAuditAction({
      action: "ADMIN_CREATE_ACTOR",
      description: `\u7BA1\u7406\u5458\u521B\u5EFA\u6F14\u5458: ${name}`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        actorId: newActorId,
        name,
        avatarUrl: avatar_url,
        isActive: isActiveValue === 1,
        sortOrder: finalSortOrder
      }
    });
    logger.info("\u7BA1\u7406\u5458\u521B\u5EFA\u6F14\u5458\u6210\u529F", {
      adminId: admin.id,
      adminUsername: admin.username,
      actorId: newActorId,
      name
    });
    return {
      success: true,
      message: "\u6F14\u5458\u521B\u5EFA\u6210\u529F",
      data: {
        id: newActorId
      }
    };
  } catch (error) {
    logger.error("\u521B\u5EFA\u6F14\u5458\u5931\u8D25", {
      error: error.message,
      adminId: (_b = event.context.admin) == null ? void 0 : _b.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { index_post as default };
//# sourceMappingURL=index.post.mjs.map
