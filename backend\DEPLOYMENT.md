# 剧投投后端服务部署指南

## 📋 部署前准备

### 1. 服务器环境要求
- **操作系统**: Ubuntu 20.04+ / CentOS 7+ / Debian 10+
- **Node.js**: 18.x 或更高版本
- **内存**: 最少 2GB，推荐 4GB+
- **存储**: 最少 20GB 可用空间
- **网络**: 稳定的互联网连接

### 2. 必需软件安装

```bash
# 安装 Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 pnpm
npm install -g pnpm

# 安装 PM2 (生产环境进程管理)
npm install -g pm2

# 安装 Docker (可选，用于容器化部署)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
```

### 3. 数据库准备
确保 MySQL 数据库已安装并运行，数据库 `mengtu` 已创建并导入了数据。

## 🚀 部署方式

### 方式一：Docker 容器化部署 (推荐)

#### 1. 检查配置
```bash
# 检查生产环境配置
node check-production-config.js

# 如果配置有问题，生成模板
node check-production-config.js template
```

#### 2. 修改生产环境配置
编辑 `.env.production` 文件，配置以下关键信息：

```bash
# 数据库配置
DB_HOST=localhost
DB_USER=mengtu_user
DB_PASSWORD=your_secure_password
DB_NAME=mengtu

# JWT密钥 (请生成强密钥)
JWT_SECRET=your_jwt_secret_at_least_32_characters_long
ACCESS_TOKEN_SECRET=your_access_token_secret_at_least_32_characters
REFRESH_TOKEN_SECRET=your_refresh_token_secret_at_least_32_characters

# 腾讯云COS配置 (如果使用云存储)
COS_SECRET_ID=your_cos_secret_id
COS_SECRET_KEY=your_cos_secret_key
COS_BUCKET=mengtu-production
```

#### 3. 使用部署脚本
```bash
# 给脚本执行权限
chmod +x deploy.sh

# 构建并启动生产环境
./deploy.sh prod start

# 查看服务状态
./deploy.sh prod status

# 查看日志
./deploy.sh prod logs
```

### 方式二：PM2 进程管理部署

#### 1. 构建应用
```bash
# 安装依赖
pnpm install

# 构建生产版本
pnpm build
```

#### 2. 配置环境变量
```bash
# 复制生产环境配置
cp .env.production .env

# 检查配置
node check-production-config.js
```

#### 3. 启动服务
```bash
# 使用 PM2 启动
pm2 start ecosystem.config.js --env production

# 保存 PM2 配置
pm2 save

# 设置开机自启
pm2 startup
```

#### 4. 管理服务
```bash
# 查看状态
pm2 status

# 查看日志
pm2 logs mengtu-backend

# 重启服务
pm2 restart mengtu-backend

# 停止服务
pm2 stop mengtu-backend

# 删除服务
pm2 delete mengtu-backend
```

## 🔧 配置说明

### 环境变量配置

| 变量名 | 必需 | 说明 | 示例 |
|--------|------|------|------|
| `DB_HOST` | ✅ | 数据库主机 | `localhost` |
| `DB_USER` | ✅ | 数据库用户 | `mengtu_user` |
| `DB_PASSWORD` | ✅ | 数据库密码 | `secure_password` |
| `DB_NAME` | ✅ | 数据库名 | `mengtu` |
| `JWT_SECRET` | ✅ | JWT密钥 | 至少32字符 |
| `APP_URL` | ✅ | 前端地址 | `https://www.qinghee.com.cn` |
| `API_URL` | ✅ | API地址 | `https://api.qinghee.com.cn/api` |
| `COS_SECRET_ID` | ❌ | 腾讯云密钥ID | 云存储配置 |
| `COS_SECRET_KEY` | ❌ | 腾讯云密钥 | 云存储配置 |

### 端口配置
- **默认端口**: 3001
- **健康检查**: `GET /api/health`
- **API前缀**: `/api`

## 🔍 监控和维护

### 健康检查
```bash
# 检查API健康状态
curl http://localhost:3001/api/health

# 预期响应
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 3600
}
```

### 日志管理
```bash
# 查看应用日志
tail -f logs/out.log

# 查看错误日志
tail -f logs/error.log

# 查看PM2日志
pm2 logs mengtu-backend --lines 100
```

### 性能监控
```bash
# PM2 监控面板
pm2 monit

# 系统资源使用
pm2 show mengtu-backend
```

## 🛠️ 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查数据库配置
node check-production-config.js

# 测试数据库连接
mysql -h localhost -u mengtu_user -p mengtu
```

#### 2. 端口被占用
```bash
# 查看端口占用
lsof -i :3001

# 杀死占用进程
sudo kill -9 <PID>
```

#### 3. 权限问题
```bash
# 检查文件权限
ls -la uploads/ logs/

# 修复权限
sudo chown -R $USER:$USER uploads/ logs/
chmod 755 uploads/ logs/
```

#### 4. 内存不足
```bash
# 检查内存使用
free -h

# 调整PM2配置中的max_memory_restart
```

### 日志分析
```bash
# 查看错误日志
grep "ERROR" logs/error.log

# 查看最近的错误
tail -n 50 logs/error.log | grep "ERROR"

# 实时监控错误
tail -f logs/error.log | grep --color "ERROR"
```

## 🔄 更新部署

### Docker方式更新
```bash
# 拉取最新代码
git pull origin main

# 重新部署
./deploy.sh prod restart
```

### PM2方式更新
```bash
# 拉取最新代码
git pull origin main

# 重新构建
pnpm install
pnpm build

# 重启服务
pm2 restart mengtu-backend
```

## 📞 技术支持

如果遇到部署问题，请检查：
1. 服务器环境是否满足要求
2. 环境变量配置是否正确
3. 数据库连接是否正常
4. 端口是否被占用
5. 日志中的错误信息

更多技术支持，请联系开发团队。
