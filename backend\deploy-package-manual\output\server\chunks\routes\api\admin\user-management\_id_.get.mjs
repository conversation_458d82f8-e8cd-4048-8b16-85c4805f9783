import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, j as getRouter<PERSON>aram, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import { c as validateId } from '../../../../_/validators.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__get = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.USER_LIST);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u67E5\u770B\u7528\u6237\u8BE6\u60C5"
      });
    }
    const userId = validateId(getRouterParam(event, "id"));
    if (!userId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u7528\u6237ID"
      });
    }
    const users = await query(
      `SELECT id, username, email, phone, user_type, real_name, company_name,
              id_card, business_license, status, created_at, updated_at, last_login_at
       FROM users
       WHERE id = ?`,
      [userId]
    );
    if (users.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u7528\u6237\u4E0D\u5B58\u5728"
      });
    }
    const user = users[0];
    await logAuditAction({
      action: "ADMIN_VIEW_USER_DETAIL",
      description: `\u7BA1\u7406\u5458\u67E5\u770B\u7528\u6237\u8BE6\u60C5: ${user.username}`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: { targetUserId: userId, targetUsername: user.username }
    });
    return {
      success: true,
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        phone: user.phone || null,
        avatar: user.avatar,
        userType: user.user_type,
        realName: user.real_name,
        companyName: user.company_name,
        idCard: user.id_card,
        businessLicense: user.business_license,
        status: user.status === 1 ? "active" : "inactive",
        registeredAt: user.created_at,
        updatedAt: user.updated_at,
        lastLoginAt: user.last_login_at
      }
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u7528\u6237\u8BE6\u60C5\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      userId: getRouterParam(event, "id"),
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { _id__get as default };
//# sourceMappingURL=_id_.get.mjs.map
