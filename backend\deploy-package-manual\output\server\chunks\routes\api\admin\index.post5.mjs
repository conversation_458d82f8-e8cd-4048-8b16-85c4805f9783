import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, r as readBody, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../_/nitro.mjs';
import { b as validateFundData } from '../../../_/validators.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const body = await readBody(event);
    const validation = validateFundData(body, false);
    if (!validation.isValid) {
      const errorMessages = Object.values(validation.errors).join(", ");
      throw createError({
        statusCode: 400,
        statusMessage: errorMessages
      });
    }
    const {
      code,
      title,
      description,
      type,
      risk,
      min_investment,
      period,
      target_size,
      raised_amount,
      expected_return,
      min_holding_period,
      risk_description,
      establish_date,
      exit_date,
      manager,
      trustee,
      redemption_policy,
      investment_strategy,
      is_published,
      highlights,
      documents,
      faqs,
      timelines,
      fees,
      historicalPerformance,
      usagePlans,
      usagePlanDescription,
      successCases
    } = body;
    const existingFund = await query(
      "SELECT id FROM funds WHERE code = ?",
      [code]
    );
    if (existingFund.length > 0) {
      throw createError({
        statusCode: 409,
        statusMessage: "\u57FA\u91D1\u4EE3\u7801\u5DF2\u5B58\u5728"
      });
    }
    await query("START TRANSACTION");
    try {
      const result = await query(
        `INSERT INTO funds (
          code, title, description, type, risk, min_investment, period,
          target_size, raised_amount, expected_return, min_holding_period,
          risk_description, establish_date, exit_date, manager, trustee,
          redemption_policy, investment_strategy, is_published,
          usage_plan_description, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          code,
          title,
          description,
          type,
          risk,
          min_investment,
          period,
          target_size,
          raised_amount || 0,
          expected_return,
          min_holding_period,
          risk_description,
          establish_date,
          exit_date,
          manager,
          trustee,
          redemption_policy,
          investment_strategy,
          is_published ? 1 : 0,
          usagePlanDescription
        ]
      );
      const fundId = result.insertId;
      if (highlights && highlights.length > 0) {
        for (const highlight of highlights) {
          await query(
            "INSERT INTO fund_highlights (fund_id, content, created_at) VALUES (?, ?, NOW())",
            [fundId, highlight]
          );
        }
      }
      if (faqs && faqs.length > 0) {
        for (const faq of faqs) {
          await query(
            "INSERT INTO fund_faqs (fund_id, question, answer, created_at) VALUES (?, ?, ?, NOW())",
            [fundId, faq.question, faq.answer]
          );
        }
      }
      if (timelines && timelines.length > 0) {
        for (const timeline of timelines) {
          await query(
            "INSERT INTO fund_timelines (fund_id, stage, date, status, created_at) VALUES (?, ?, ?, ?, NOW())",
            [fundId, timeline.stage, timeline.date, timeline.status]
          );
        }
      }
      if (fees && fees.length > 0) {
        for (const fee of fees) {
          await query(
            "INSERT INTO fund_fees (fund_id, name, value, created_at) VALUES (?, ?, ?, NOW())",
            [fundId, fee.name, fee.value]
          );
        }
      }
      if (historicalPerformance && historicalPerformance.length > 0) {
        for (const performance of historicalPerformance) {
          await query(
            "INSERT INTO fund_performances (fund_id, year, return_rate, average_rate, created_at) VALUES (?, ?, ?, ?, NOW())",
            [fundId, performance.year, performance.return, performance.average || null]
          );
        }
      }
      if (usagePlans && usagePlans.length > 0) {
        for (const plan of usagePlans) {
          await query(
            "INSERT INTO fund_usage_plans (fund_id, name, percentage, description, created_at) VALUES (?, ?, ?, ?, NOW())",
            [fundId, plan.name, plan.percentage, plan.description]
          );
        }
      }
      if (successCases && successCases.length > 0) {
        for (const successCase of successCases) {
          await query(
            "INSERT INTO fund_success_cases (fund_id, title, description, return_rate, created_at) VALUES (?, ?, ?, ?, NOW())",
            [fundId, successCase.title, successCase.description, successCase.returnRate]
          );
        }
      }
      await query("COMMIT");
      await logAuditAction({
        action: "ADMIN_CREATE_FUND",
        description: `\u7BA1\u7406\u5458\u521B\u5EFA\u57FA\u91D1: ${title} (${code})`,
        userId: admin.id,
        username: admin.username,
        ip: getClientIP(event),
        userAgent: getHeader(event, "user-agent") || "",
        details: {
          fundId,
          code,
          title,
          type,
          targetSize,
          isPublished
        }
      });
      logger.info("\u7BA1\u7406\u5458\u521B\u5EFA\u57FA\u91D1\u6210\u529F", {
        adminId: admin.id,
        adminUsername: admin.username,
        fundId,
        code,
        title
      });
      return {
        success: true,
        message: "\u57FA\u91D1\u521B\u5EFA\u6210\u529F",
        data: {
          id: fundId
        }
      };
    } catch (error) {
      await query("ROLLBACK");
      throw error;
    }
  } catch (error) {
    logger.error("\u521B\u5EFA\u57FA\u91D1\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { index_post as default };
//# sourceMappingURL=index.post5.mjs.map
