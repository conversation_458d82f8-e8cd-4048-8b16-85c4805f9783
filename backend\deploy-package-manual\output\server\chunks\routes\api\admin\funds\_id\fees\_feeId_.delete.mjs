import { c as defineEvent<PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _feeId__delete = defineEventHandler(async (event) => {
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const fundId = getRouterParam(event, "id");
    const feeId = getRouterParam(event, "feeId");
    if (!fundId || isNaN(Number(fundId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u57FA\u91D1ID"
      });
    }
    if (!feeId || isNaN(Number(feeId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u8D39\u7528ID"
      });
    }
    const existingFee = await query(
      "SELECT * FROM fund_fees WHERE id = ? AND fund_id = ?",
      [feeId, fundId]
    );
    if (existingFee.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u8D39\u7528\u4E0D\u5B58\u5728"
      });
    }
    await query(
      "DELETE FROM fund_fees WHERE id = ? AND fund_id = ?",
      [feeId, fundId]
    );
    await logAuditAction({
      action: "ADMIN_DELETE_FUND_FEE",
      description: `\u7BA1\u7406\u5458\u5220\u9664\u57FA\u91D1\u8D39\u7528: \u57FA\u91D1ID=${fundId}, \u8D39\u7528ID=${feeId}`,
      userId: adminPayload.id,
      username: adminPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      metadata: {
        fundId: Number(fundId),
        feeId: Number(feeId),
        deletedName: existingFee[0].name,
        deletedValue: existingFee[0].value
      }
    });
    logger.info("\u57FA\u91D1\u8D39\u7528\u5220\u9664\u6210\u529F", {
      adminId: adminPayload.id,
      adminUsername: adminPayload.username,
      fundId: Number(fundId),
      feeId: Number(feeId),
      deletedName: existingFee[0].name
    });
    return {
      success: true,
      message: "\u57FA\u91D1\u8D39\u7528\u5220\u9664\u6210\u529F",
      data: {
        id: Number(feeId),
        fundId: Number(fundId)
      }
    };
  } catch (error) {
    logger.error("\u5220\u9664\u57FA\u91D1\u8D39\u7528\u5931\u8D25", {
      error: error.message,
      fundId: getRouterParam(event, "id"),
      feeId: getRouterParam(event, "feeId")
    });
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "\u5220\u9664\u57FA\u91D1\u8D39\u7528\u5931\u8D25"
    });
  }
});

export { _feeId__delete as default };
//# sourceMappingURL=_feeId_.delete.mjs.map
