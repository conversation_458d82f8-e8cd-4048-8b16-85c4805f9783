# 多阶段构建：构建阶段
# 为什么用多阶段：构建需要完整Node环境，运行只需要静态文件
FROM node:18-alpine AS builder

WORKDIR /app

# 安装pnpm
RUN npm install -g pnpm

# 复制依赖文件
COPY package.json pnpm-lock.yaml ./

# 安装所有依赖（包括开发依赖，因为需要构建）
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建静态文件
# 为什么generate：Nuxt3生成静态站点，性能更好
RUN pnpm generate

# 生产阶段：使用Nginx提供静态文件服务
# 为什么用Nginx：专业的Web服务器，性能优异，配置灵活
FROM nginx:alpine AS production

# 复制构建好的静态文件到Nginx目录
# /usr/share/nginx/html是Nginx默认的静态文件目录
COPY --from=builder /app/.output/public /usr/share/nginx/html

# 复制Nginx配置文件
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露80端口
# 为什么是80：HTTP标准端口
EXPOSE 80

# 启动Nginx
# daemon off让Nginx在前台运行，适合容器环境
CMD ["nginx", "-g", "daemon off;"]
