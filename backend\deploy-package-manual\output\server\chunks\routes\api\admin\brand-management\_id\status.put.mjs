import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getRouter<PERSON>ara<PERSON>, f as createError, r as readBody, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const status_put = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const brandId = getRouterParam(event, "id");
    if (!brandId || isNaN(Number(brandId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u5382\u724CID"
      });
    }
    const body = await readBody(event);
    const { status } = body;
    if (!status || !["active", "inactive"].includes(status)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u72B6\u6001\u503C\u65E0\u6548\uFF0C\u5FC5\u987B\u662F active \u6216 inactive"
      });
    }
    const existingBrand = await query(
      "SELECT id, brand_name, status FROM brands WHERE id = ?",
      [brandId]
    );
    if (existingBrand.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u5382\u724C\u4E0D\u5B58\u5728"
      });
    }
    const currentBrand = existingBrand[0];
    if (currentBrand.status === status) {
      return {
        success: true,
        message: `\u5382\u724C\u72B6\u6001\u5DF2\u7ECF\u662F${status === "active" ? "\u542F\u7528" : "\u7981\u7528"}\u72B6\u6001`,
        data: {
          id: brandId,
          brandName: currentBrand.brand_name,
          status
        }
      };
    }
    await query(
      "UPDATE brands SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
      [status, brandId]
    );
    await logAuditAction({
      action: "ADMIN_TOGGLE_BRAND_STATUS",
      description: `\u7BA1\u7406\u5458${status === "active" ? "\u542F\u7528" : "\u7981\u7528"}\u5382\u724C: ${currentBrand.brand_name}`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        brandId,
        brandName: currentBrand.brand_name,
        oldStatus: currentBrand.status,
        newStatus: status
      }
    });
    logger.info("\u7BA1\u7406\u5458\u5207\u6362\u5382\u724C\u72B6\u6001\u6210\u529F", {
      adminId: admin.id,
      adminUsername: admin.username,
      brandId,
      brandName: currentBrand.brand_name,
      oldStatus: currentBrand.status,
      newStatus: status
    });
    return {
      success: true,
      message: `\u5382\u724C${status === "active" ? "\u542F\u7528" : "\u7981\u7528"}\u6210\u529F`,
      data: {
        id: brandId,
        brandName: currentBrand.brand_name,
        status
      }
    };
  } catch (error) {
    logger.error("\u7BA1\u7406\u5458\u5207\u6362\u5382\u724C\u72B6\u6001\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      brandId: getRouterParam(event, "id"),
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u5207\u6362\u5382\u724C\u72B6\u6001\u5931\u8D25"
    });
  }
});

export { status_put as default };
//# sourceMappingURL=status.put.mjs.map
