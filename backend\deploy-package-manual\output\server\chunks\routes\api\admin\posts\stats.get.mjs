import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, q as query, f as createError } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const stats_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const totalStats = await query(`
      SELECT
        COUNT(*) as total,
        SUM(CASE WHEN status = 'published' THEN 1 ELSE 0 END) as published,
        SUM(CASE WHEN status = 'draft' THEN 1 ELSE 0 END) as draft,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'archived' THEN 1 ELSE 0 END) as archived,
        SUM(view_count) as totalViews
      FROM posts
    `);
    const todayViews = await query(`
      SELECT COALESCE(SUM(view_count), 0) as todayViews
      FROM posts
      WHERE DATE(created_at) = CURDATE()
    `);
    const stats = totalStats[0];
    const todayViewsCount = ((_a = todayViews[0]) == null ? void 0 : _a.todayViews) || 0;
    return {
      data: {
        total: stats.total || 0,
        published: stats.published || 0,
        draft: stats.draft || 0,
        pending: stats.pending || 0,
        archived: stats.archived || 0,
        totalViews: stats.totalViews || 0,
        todayViews: todayViewsCount
      }
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u63A8\u6587\u7EDF\u8BA1\u6570\u636E\u5931\u8D25:", error);
    throw createError({
      statusCode: 500,
      statusMessage: error.message || "\u83B7\u53D6\u63A8\u6587\u7EDF\u8BA1\u6570\u636E\u5931\u8D25"
    });
  }
});

export { stats_get as default };
//# sourceMappingURL=stats.get.mjs.map
