import { c as defineEvent<PERSON><PERSON><PERSON>, f as createError, q as query, l as logger, e as getClientIP } from '../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const dashboard_get = defineEventHandler(async (event) => {
  var _a, _b, _c, _d, _e, _f;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const [userStats, adminStats, auditStats] = await Promise.all([
      // 用户统计
      query("SELECT COUNT(*) as total, SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active FROM users"),
      // 管理员统计
      query("SELECT COUNT(*) as total, SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active FROM admins"),
      // 审计日志统计（最近7天）
      query(`SELECT COUNT(*) as total FROM audit_log 
             WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)`)
    ]);
    const recentAudits = await query(`
      SELECT action, description, username, created_at, ip
      FROM audit_log 
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    return {
      success: true,
      data: {
        stats: {
          users: {
            total: ((_a = userStats[0]) == null ? void 0 : _a.total) || 0,
            active: ((_b = userStats[0]) == null ? void 0 : _b.active) || 0
          },
          admins: {
            total: ((_c = adminStats[0]) == null ? void 0 : _c.total) || 0,
            active: ((_d = adminStats[0]) == null ? void 0 : _d.active) || 0
          },
          audits: {
            recentCount: ((_e = auditStats[0]) == null ? void 0 : _e.total) || 0
          }
        },
        recentAudits: recentAudits || [],
        admin: {
          id: admin.id,
          username: admin.username,
          real_name: admin.real_name,
          last_login_at: admin.last_login_at
        }
      }
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u7BA1\u7406\u5458\u4EEA\u8868\u677F\u6570\u636E\u5931\u8D25", {
      error: error.message,
      adminId: (_f = event.context.admin) == null ? void 0 : _f.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { dashboard_get as default };
//# sourceMappingURL=dashboard.get.mjs.map
