{"name": "j<PERSON><PERSON><PERSON>-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "build:prod": "vue-tsc --noEmit && vite build --mode production", "build:fast": "vite build --mode production", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>"}, "dependencies": {"@vee-validate/rules": "^4.15.0", "axios": "^1.5.0", "echarts": "^5.4.3", "pinia": "^2.3.1", "sweetalert2": "^11.21.0", "vee-validate": "^4.15.0", "vue": "^3.3.4", "vue-countup-v3": "^1.4.2", "vue-router": "^4.2.4", "vue-toastification": "^2.0.0-rc.5", "vuedraggable": "4.1.0", "yup": "^1.6.1"}, "devDependencies": {"@types/node": "^20.8.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "@vitejs/plugin-vue": "^4.3.1", "autoprefixer": "^10.4.15", "eslint": "^8.46.0", "eslint-plugin-vue": "^9.16.1", "postcss": "^8.4.28", "tailwindcss": "^3.3.3", "typescript": "~5.0.4", "vite": "^4.4.9", "vue-tsc": "^1.4.4"}}