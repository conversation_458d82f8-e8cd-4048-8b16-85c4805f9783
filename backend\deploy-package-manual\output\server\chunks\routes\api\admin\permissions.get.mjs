import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, q as query, l as logger, e as getClientIP } from '../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const permissions_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const permissions = await query(`
      SELECT 
        code,
        name,
        module,
        description,
        created_at
      FROM admin_permissions 
      ORDER BY module ASC, code ASC
    `);
    logger.info("\u83B7\u53D6\u6743\u9650\u5217\u8868\u6210\u529F", {
      adminId: adminPayload.id,
      permissionCount: permissions.length,
      ip: getClientIP(event)
    });
    return {
      success: true,
      data: permissions
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u6743\u9650\u5217\u8868\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { permissions_get as default };
//# sourceMappingURL=permissions.get.mjs.map
