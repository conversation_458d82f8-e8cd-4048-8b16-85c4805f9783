import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, g as getQuery, q as query, f as createError } from '../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const investmentRecords_get = defineEventHandler(async (event) => {
  var _a;
  try {
    console.log("\u6295\u8D44\u8BB0\u5F55API\u88AB\u8C03\u7528");
    const queryParams = getQuery(event);
    const page = Number(queryParams.page) || 1;
    const pageSize = Number(queryParams.pageSize) || 20;
    const keyword = queryParams.keyword || "";
    const status = queryParams.status || "";
    const startDate = queryParams.startDate || "";
    const endDate = queryParams.endDate || "";
    console.log("\u6295\u8D44\u8BB0\u5F55\u67E5\u8BE2\u53C2\u6570:", { page, pageSize, keyword, status, startDate, endDate });
    let whereConditions = ['uat.transaction_type = "shells_out"', 'uat.related_type = "investment"'];
    let queryValues = [];
    if (keyword) {
      whereConditions.push("(u.username LIKE ? OR uat.transaction_no LIKE ?)");
      queryValues.push(`%${keyword}%`, `%${keyword}%`);
    }
    if (status) {
      whereConditions.push("uat.status = ?");
      queryValues.push(status);
    }
    if (startDate) {
      whereConditions.push("DATE(uat.created_at) >= ?");
      queryValues.push(startDate);
    }
    if (endDate) {
      whereConditions.push("DATE(uat.created_at) <= ?");
      queryValues.push(endDate);
    }
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(" AND ")}` : "";
    const countQuery = `
      SELECT COUNT(*) as total
      FROM user_asset_transactions uat
      LEFT JOIN users u ON uat.user_id = u.id
      ${whereClause}
    `;
    console.log("\u603B\u6570\u67E5\u8BE2SQL:", countQuery);
    console.log("\u67E5\u8BE2\u53C2\u6570:", queryValues);
    const countResult = await query(countQuery, queryValues);
    const total = ((_a = countResult[0]) == null ? void 0 : _a.total) || 0;
    console.log("\u6295\u8D44\u8BB0\u5F55\u603B\u6570:", total);
    const offset = (page - 1) * pageSize;
    const recordsQuery = `
      SELECT
        uat.id,
        uat.user_id,
        u.username,
        uat.transaction_type,
        uat.amount,
        uat.balance_before,
        uat.balance_after,
        uat.related_id,
        uat.related_type,
        uat.description,
        uat.transaction_no,
        uat.status,
        DATE_FORMAT(uat.created_at, '%Y-%m-%d %H:%i:%s') as created_at,
        DATE_FORMAT(uat.updated_at, '%Y-%m-%d %H:%i:%s') as updated_at
      FROM user_asset_transactions uat
      LEFT JOIN users u ON uat.user_id = u.id
      ${whereClause}
      ORDER BY uat.created_at DESC
      LIMIT ? OFFSET ?
    `;
    const recordsQueryValues = [...queryValues, pageSize, offset];
    console.log("\u6295\u8D44\u8BB0\u5F55\u67E5\u8BE2SQL:", recordsQuery);
    console.log("\u67E5\u8BE2\u53C2\u6570:", recordsQueryValues);
    const records = await query(recordsQuery, recordsQueryValues);
    console.log(`\u67E5\u8BE2\u5230 ${records.length} \u6761\u6295\u8D44\u8BB0\u5F55`);
    return {
      success: true,
      data: {
        result: records,
        total,
        page: Number(page),
        pageSize: Number(pageSize),
        totalPages: Math.ceil(total / Number(pageSize))
      }
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u6295\u8D44\u8BB0\u5F55\u5931\u8D25:", error);
    throw createError({
      statusCode: 500,
      statusMessage: error.message || "\u83B7\u53D6\u6295\u8D44\u8BB0\u5F55\u5931\u8D25"
    });
  }
});

export { investmentRecords_get as default };
//# sourceMappingURL=investment-records.get.mjs.map
