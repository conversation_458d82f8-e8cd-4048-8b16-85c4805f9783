{"version": 3, "file": "cos-uploader.mjs", "sources": ["../../../../utils/cos-uploader.ts"], "sourcesContent": null, "names": [], "mappings": ";;;;;;AAqCA,eAAe,YAAA,GAAmC;AAChD,EAAA,IAAI;AACF,IAAA,MAAM,SAAS,MAAM,KAAA;AAAA,MACnB,iEAAA;AAAA,MACA,CAAC,cAAc;AAAA,KACjB;AAEA,IAAA,IAAI,OAAO,MAAA,GAAS,CAAA,IAAK,MAAA,CAAO,CAAC,EAAE,aAAA,EAAe;AAChD,MAAA,MAAM,cAAc,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,CAAC,EAAE,aAAa,CAAA;AAEtD,MAAA,OAAO;AAAA,QACL,QAAA,EAAU,YAAY,QAAA,IAAY,EAAA;AAAA,QAClC,SAAA,EAAW,YAAY,SAAA,IAAa,EAAA;AAAA,QACpC,MAAA,EAAQ,YAAY,MAAA,IAAU,cAAA;AAAA,QAC9B,MAAA,EAAQ,YAAY,MAAA,IAAU,EAAA;AAAA,QAC9B,SAAA,EAAW,YAAY,SAAA,IAAa;AAAA,OACtC;AAAA,IACF;AAGA,IAAA,OAAO;AAAA,MACL,QAAA,EAAU,EAAA;AAAA,MACV,SAAA,EAAW,EAAA;AAAA,MACX,MAAA,EAAQ,cAAA;AAAA,MACR,MAAA,EAAQ,EAAA;AAAA,MACR,SAAA,EAAW;AAAA,KACb;AAAA,EACF,SAAS,KAAA,EAAY;AACnB,IAAA,MAAA,CAAO,MAAM,yCAAA,EAAa,EAAE,KAAA,EAAO,KAAA,CAAM,SAAS,CAAA;AAClD,IAAA,MAAM,IAAI,MAAM,yCAAW,CAAA;AAAA,EAC7B;AACF;AAKA,eAAe,eAAA,GAAgC;AAC7C,EAAA,MAAM,SAAA,GAAY,MAAM,YAAA,EAAa;AAErC,EAAA,IAAI,CAAC,UAAU,QAAA,IAAY,CAAC,UAAU,SAAA,IAAa,CAAC,UAAU,MAAA,EAAQ;AACpE,IAAA,MAAM,IAAI,MAAM,mIAA0B,CAAA;AAAA,EAC5C;AAEA,EAAA,OAAO,IAAI,GAAA,CAAI;AAAA,IACb,UAAU,SAAA,CAAU,QAAA;AAAA,IACpB,WAAW,SAAA,CAAU,SAAA;AAAA,IACrB,iBAAA,EAAmB,CAAA;AAAA,IACnB,kBAAA,EAAoB,CAAA;AAAA,IACpB,SAAA,EAAW,OAAO,IAAA,GAAO;AAAA;AAAA,GAC1B,CAAA;AACH;AAKA,eAAsB,WAAA,CACpB,UACA,QAAA,EACuB;AACvB,EAAA,MAAM,SAAA,GAAY,MAAM,YAAA,EAAa;AACrC,EAAA,MAAM,GAAA,GAAM,MAAM,eAAA,EAAgB;AAElC,EAAA,IAAI;AAEF,IAAA,IAAI,SAAA;AAEJ,IAAA,MAAM,aAAA,GAAgB,UAAU,SAAA,IAAa,UAAA;AAE7C,IAAA,IAAI,QAAA,EAAU;AAEZ,MAAA,MAAM,kBAAA,GAAqB,QAAA,CAAS,OAAA,CAAQ,KAAA,EAAO,GAAG,CAAA;AAGtD,MAAA,IAAI,mBAAmB,UAAA,CAAW,aAAA,GAAgB,GAAG,CAAA,IAAK,uBAAuB,aAAA,EAAe;AAC9F,QAAA,SAAA,GAAY,kBAAA;AAAA,MACd,CAAA,MAAO;AACL,QAAA,SAAA,GAAY,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,aAAA,EAAe,kBAAkB,CAAA;AAAA,MAC/D;AAEA,MAAA,MAAA,CAAO,KAAA,CAAM,CAAA,wDAAA,EAAc,SAAS,CAAA,CAAA,EAAI;AAAA,QACtC,aAAA;AAAA,QACA,gBAAA,EAAkB,QAAA;AAAA,QAClB,kBAAA;AAAA,QACA,cAAA,EAAgB;AAAA,OACjB,CAAA;AAAA,IACH,CAAA,MAAO;AAEL,MAAA,MAAM,QAAA,GAAW,sBAAA,CAAuB,QAAA,CAAS,YAAY,CAAA;AAC7D,MAAA,SAAA,GAAY,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,aAAA,EAAe,QAAQ,CAAA;AACnD,MAAA,MAAA,CAAO,KAAA,CAAM,CAAA,kDAAA,EAAa,SAAS,CAAA,CAAA,EAAI;AAAA,QACrC,aAAA;AAAA,QACA,QAAA;AAAA,QACA,cAAA,EAAgB;AAAA,OACjB,CAAA;AAAA,IACH;AAEA,IAAA,MAAA,CAAO,KAAK,+CAAA,EAAc;AAAA,MACxB,cAAc,QAAA,CAAS,YAAA;AAAA,MACvB,SAAA;AAAA,MACA,QAAQ,SAAA,CAAU,MAAA;AAAA,MAClB,QAAQ,SAAA,CAAU;AAAA,KACnB,CAAA;AAGD,IAAA,MAAM,MAAA,GAAS,MAAM,GAAA,CAAI,SAAA,CAAU;AAAA,MACjC,QAAQ,SAAA,CAAU,MAAA;AAAA,MAClB,QAAQ,SAAA,CAAU,MAAA;AAAA,MAClB,GAAA,EAAK,SAAA;AAAA,MACL,IAAA,EAAM,MAAM,QAAA,CAAS,QAAA,CAAS,IAAI,CAAA;AAAA;AAAA,MAClC,aAAa,QAAA,CAAS,QAAA;AAAA,MACtB,UAAA,EAAY,CAAC,YAAA,KAAiB;AAC5B,QAAA,MAAA,CAAO,MAAM,0BAAA,EAAQ;AAAA,UACnB,OAAA,EAAS,IAAA,CAAK,KAAA,CAAM,YAAA,CAAa,UAAU,GAAG,CAAA;AAAA,UAC9C,OAAO,YAAA,CAAa;AAAA,SACrB,CAAA;AAAA,MACH;AAAA,KACD,CAAA;AAGD,IAAA,MAAM,GAAA,GAAM,WAAW,SAAA,CAAU,MAAM,QAAQ,SAAA,CAAU,MAAM,iBAAiB,SAAS,CAAA,CAAA;AAEzF,IAAA,MAAA,CAAO,KAAK,+CAAA,EAAc;AAAA,MACxB,cAAc,QAAA,CAAS,YAAA;AAAA,MACvB,SAAA;AAAA,MACA,GAAA;AAAA,MACA,MAAM,MAAA,CAAO;AAAA,KACd,CAAA;AAED,IAAA,OAAO;AAAA,MACL,GAAA;AAAA,MACA,GAAA,EAAK,SAAA;AAAA,MACL,UAAU,MAAA,CAAO;AAAA,KACnB;AAAA,EAEF,SAAS,KAAA,EAAY;AACnB,IAAA,MAAA,CAAO,MAAM,+CAAA,EAAc;AAAA,MACzB,cAAc,QAAA,CAAS,YAAA;AAAA,MACvB,OAAO,KAAA,CAAM,OAAA;AAAA,MACb,MAAM,KAAA,CAAM;AAAA,KACb,CAAA;AAED,IAAA,MAAM,IAAI,KAAA,CAAM,CAAA,6BAAA,EAAY,KAAA,CAAM,OAAO,CAAA,CAAE,CAAA;AAAA,EAC7C;AACF;;;;"}