import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, g as getQuery, q as query, f as createError } from '../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const query_params = getQuery(event);
    const {
      page = 1,
      pageSize = 20,
      status,
      search,
      author,
      isOnline,
      startDate,
      endDate,
      orderBy = "created_at",
      orderDirection = "DESC"
    } = query_params;
    let whereConditions = [];
    let queryParams = [];
    if (status) {
      whereConditions.push("status = ?");
      queryParams.push(status);
    }
    if (isOnline !== void 0 && isOnline !== "") {
      whereConditions.push("is_online = ?");
      queryParams.push(isOnline === "true" ? 1 : 0);
    }
    if (search) {
      whereConditions.push("(title LIKE ? OR content LIKE ?)");
      queryParams.push(`%${search}%`, `%${search}%`);
    }
    if (author) {
      whereConditions.push("author LIKE ?");
      queryParams.push(`%${author}%`);
    }
    if (startDate) {
      whereConditions.push("created_at >= ?");
      queryParams.push(startDate);
    }
    if (endDate) {
      whereConditions.push("created_at <= ?");
      queryParams.push(endDate + " 23:59:59");
    }
    const whereClause = whereConditions.length > 0 ? "WHERE " + whereConditions.join(" AND ") : "";
    const allowedOrderFields = ["id", "title", "status", "view_count", "created_at", "updated_at"];
    const safeOrderBy = allowedOrderFields.includes(orderBy) ? orderBy : "created_at";
    const safeOrderDirection = orderDirection === "ASC" ? "ASC" : "DESC";
    const offset = (Number(page) - 1) * Number(pageSize);
    const postsQuery = `
      SELECT p.*
      FROM posts p
      ${whereClause}
      ORDER BY p.${safeOrderBy} ${safeOrderDirection}
      LIMIT ? OFFSET ?
    `;
    const posts = await query(postsQuery, [...queryParams, Number(pageSize), offset]);
    const countQuery = `
      SELECT COUNT(p.id) as total
      FROM posts p
      ${whereClause}
    `;
    const countResult = await query(countQuery, queryParams);
    const total = ((_a = countResult[0]) == null ? void 0 : _a.total) || 0;
    const processedPosts = posts.map((post) => ({
      id: post.id,
      title: post.title,
      slug: post.slug,
      content: post.content,
      author: post.author,
      status: post.status,
      isOnline: Boolean(post.is_online),
      viewCount: post.view_count || 0,
      publishDate: post.publish_date,
      createdAt: post.created_at,
      updatedAt: post.updated_at
    }));
    const totalPages = Math.ceil(total / Number(pageSize));
    return {
      success: true,
      data: {
        list: processedPosts,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total,
          totalPages
        }
      }
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u63A8\u6587\u5217\u8868\u5931\u8D25:", error);
    throw createError({
      statusCode: 500,
      statusMessage: error.message || "\u83B7\u53D6\u63A8\u6587\u5217\u8868\u5931\u8D25"
    });
  }
});

export { index_get as default };
//# sourceMappingURL=index.get9.mjs.map
