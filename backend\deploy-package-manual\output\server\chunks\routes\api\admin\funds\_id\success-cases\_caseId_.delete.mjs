import { c as defineEvent<PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _caseId__delete = defineEventHandler(async (event) => {
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const fundId = getRouterParam(event, "id");
    const caseId = getRouterParam(event, "caseId");
    if (!fundId || isNaN(Number(fundId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u57FA\u91D1ID"
      });
    }
    if (!caseId || isNaN(Number(caseId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u6848\u4F8BID"
      });
    }
    const existingCase = await query(
      "SELECT * FROM fund_success_cases WHERE id = ? AND fund_id = ?",
      [caseId, fundId]
    );
    if (existingCase.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u6210\u529F\u6848\u4F8B\u4E0D\u5B58\u5728"
      });
    }
    await query(
      "DELETE FROM fund_success_cases WHERE id = ? AND fund_id = ?",
      [caseId, fundId]
    );
    await logAuditAction({
      action: "ADMIN_DELETE_FUND_SUCCESS_CASE",
      description: `\u7BA1\u7406\u5458\u5220\u9664\u57FA\u91D1\u6210\u529F\u6848\u4F8B: \u57FA\u91D1ID=${fundId}, \u6848\u4F8BID=${caseId}`,
      userId: adminPayload.id,
      username: adminPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      metadata: {
        fundId: Number(fundId),
        caseId: Number(caseId),
        deletedTitle: existingCase[0].title,
        deletedDescription: existingCase[0].description,
        deletedReturn: existingCase[0].return,
        deletedInvestment: existingCase[0].investment
      }
    });
    logger.info("\u57FA\u91D1\u6210\u529F\u6848\u4F8B\u5220\u9664\u6210\u529F", {
      adminId: adminPayload.id,
      adminUsername: adminPayload.username,
      fundId: Number(fundId),
      caseId: Number(caseId),
      deletedTitle: existingCase[0].title
    });
    return {
      success: true,
      message: "\u57FA\u91D1\u6210\u529F\u6848\u4F8B\u5220\u9664\u6210\u529F",
      data: {
        id: Number(caseId),
        fundId: Number(fundId)
      }
    };
  } catch (error) {
    logger.error("\u5220\u9664\u57FA\u91D1\u6210\u529F\u6848\u4F8B\u5931\u8D25", {
      error: error.message,
      fundId: getRouterParam(event, "id"),
      caseId: getRouterParam(event, "caseId")
    });
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "\u5220\u9664\u57FA\u91D1\u6210\u529F\u6848\u4F8B\u5931\u8D25"
    });
  }
});

export { _caseId__delete as default };
//# sourceMappingURL=_caseId_.delete.mjs.map
