import { c as defineEvent<PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, g as getQuery, q as query, l as logger, e as getClientIP } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const nameExists_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const query_params = getQuery(event);
    const { name, id } = query_params;
    if (!name) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u83DC\u5355\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    let sql = "SELECT id FROM admin_menus WHERE name = ?";
    let params = [name];
    if (id) {
      sql += " AND id != ?";
      params.push(id);
    }
    const result = await query(sql, params);
    return {
      success: true,
      data: result.length > 0
    };
  } catch (error) {
    logger.error("\u68C0\u67E5\u83DC\u5355\u540D\u79F0\u662F\u5426\u5B58\u5728\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { nameExists_get as default };
//# sourceMappingURL=name-exists.get.mjs.map
