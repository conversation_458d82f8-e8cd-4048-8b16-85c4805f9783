import { c as defineEvent<PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _planId__delete = defineEventHandler(async (event) => {
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const fundId = getRouterParam(event, "id");
    const planId = getRouterParam(event, "planId");
    if (!fundId || isNaN(Number(fundId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u57FA\u91D1ID"
      });
    }
    if (!planId || isNaN(Number(planId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u8BA1\u5212ID"
      });
    }
    const existingPlan = await query(
      "SELECT * FROM fund_usage_plans WHERE id = ? AND fund_id = ?",
      [planId, fundId]
    );
    if (existingPlan.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u4F7F\u7528\u8BA1\u5212\u4E0D\u5B58\u5728"
      });
    }
    await query(
      "DELETE FROM fund_usage_plans WHERE id = ? AND fund_id = ?",
      [planId, fundId]
    );
    await logAuditAction({
      action: "ADMIN_DELETE_FUND_USAGE_PLAN",
      description: `\u7BA1\u7406\u5458\u5220\u9664\u57FA\u91D1\u8D44\u91D1\u4F7F\u7528\u8BA1\u5212: \u57FA\u91D1ID=${fundId}, \u8BA1\u5212ID=${planId}`,
      userId: adminPayload.id,
      username: adminPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      metadata: {
        fundId: Number(fundId),
        planId: Number(planId),
        deletedPurpose: existingPlan[0].purpose,
        deletedAmount: existingPlan[0].amount,
        deletedPercentage: existingPlan[0].percentage
      }
    });
    logger.info("\u57FA\u91D1\u8D44\u91D1\u4F7F\u7528\u8BA1\u5212\u5220\u9664\u6210\u529F", {
      adminId: adminPayload.id,
      adminUsername: adminPayload.username,
      fundId: Number(fundId),
      planId: Number(planId),
      deletedPurpose: existingPlan[0].purpose
    });
    return {
      success: true,
      message: "\u57FA\u91D1\u8D44\u91D1\u4F7F\u7528\u8BA1\u5212\u5220\u9664\u6210\u529F",
      data: {
        id: Number(planId),
        fundId: Number(fundId)
      }
    };
  } catch (error) {
    logger.error("\u5220\u9664\u57FA\u91D1\u8D44\u91D1\u4F7F\u7528\u8BA1\u5212\u5931\u8D25", {
      error: error.message,
      fundId: getRouterParam(event, "id"),
      planId: getRouterParam(event, "planId")
    });
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "\u5220\u9664\u57FA\u91D1\u8D44\u91D1\u4F7F\u7528\u8BA1\u5212\u5931\u8D25"
    });
  }
});

export { _planId__delete as default };
//# sourceMappingURL=_planId_.delete.mjs.map
