import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, f as createError, r as readBody, q as query, m as transaction } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const investmentTiers_put = defineEventHandler(async (event) => {
  try {
    const dramaId = getRouterParam(event, "id");
    if (!dramaId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u77ED\u5267ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const id = parseInt(dramaId);
    if (isNaN(id) || id <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u77ED\u5267ID"
      });
    }
    const body = await readBody(event);
    const { investmentTiers } = body;
    console.log("\u63A5\u6536\u5230\u7684\u6295\u8D44\u6743\u76CA\u6570\u636E:", {
      body,
      investmentTiers,
      investmentTiersLength: investmentTiers == null ? void 0 : investmentTiers.length
    });
    if (!investmentTiers || !Array.isArray(investmentTiers)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6295\u8D44\u6743\u76CA\u6570\u636E\u683C\u5F0F\u9519\u8BEF"
      });
    }
    const dramaExists = await query(
      "SELECT id FROM drama_series WHERE id = ?",
      [id]
    );
    if (dramaExists.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u77ED\u5267\u4E0D\u5B58\u5728"
      });
    }
    const result = await transaction(async (connection) => {
      await connection.execute(
        "DELETE FROM drama_investment_tiers WHERE drama_id = ?",
        [id]
      );
      for (const tier of investmentTiers) {
        await connection.execute(`
          INSERT INTO drama_investment_tiers (
            drama_id,
            tier_name,
            min_amount,
            max_amount,
            benefits,
            return_rate,
            limited_quantity,
            sold_quantity,
            sort_order,
            is_active
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          id,
          tier.tierName || "",
          tier.minAmount || 0,
          tier.maxAmount || null,
          tier.benefits || "",
          tier.returnRate || null,
          tier.limitedQuantity || null,
          tier.soldQuantity || 0,
          tier.sortOrder || 0,
          tier.isActive !== void 0 ? tier.isActive ? 1 : 0 : 1
        ]);
      }
      return {
        dramaId: id,
        investmentTiers
      };
    });
    return {
      success: true,
      message: "\u6295\u8D44\u6743\u76CA\u66F4\u65B0\u6210\u529F",
      data: result
    };
  } catch (error) {
    console.error("\u66F4\u65B0\u6295\u8D44\u6743\u76CA\u5931\u8D25:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { investmentTiers_put as default };
//# sourceMappingURL=investment-tiers.put.mjs.map
