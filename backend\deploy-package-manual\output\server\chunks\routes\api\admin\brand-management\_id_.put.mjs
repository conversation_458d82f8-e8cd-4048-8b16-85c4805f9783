import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, f as createError, r as readBody, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__put = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const brandId = getRouterParam(event, "id");
    if (!brandId || isNaN(Number(brandId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u5382\u724CID"
      });
    }
    const body = await readBody(event);
    const {
      brandName,
      brandLogo,
      creditCode,
      companyName,
      companyType,
      registeredCapital,
      legalRepresentative,
      establishmentDate,
      businessTerm,
      registeredAddress,
      businessScope,
      registrationAuthority,
      licenseIssueDate,
      businessLicensePhoto,
      status
    } = body;
    if (!brandName || !brandLogo || !companyName || !businessLicensePhoto) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5382\u724C\u540D\u79F0\u3001\u5382\u724C\u56FE\u6807\u3001\u516C\u53F8\u540D\u79F0\u548C\u8425\u4E1A\u6267\u7167\u7167\u7247\u4E3A\u5FC5\u586B\u9879"
      });
    }
    const formatDateForDB = (dateStr) => {
      if (!dateStr) return null;
      try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) return null;
        return date.toISOString().split("T")[0];
      } catch {
        return null;
      }
    };
    if (creditCode && !/^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/.test(creditCode)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u7EDF\u4E00\u793E\u4F1A\u4FE1\u7528\u4EE3\u7801\u683C\u5F0F\u4E0D\u6B63\u786E"
      });
    }
    const existingBrand = await query(
      "SELECT id, brand_name FROM brands WHERE id = ?",
      [brandId]
    );
    if (existingBrand.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u5382\u724C\u4E0D\u5B58\u5728"
      });
    }
    const duplicateName = await query(
      "SELECT id FROM brands WHERE brand_name = ? AND id != ?",
      [brandName, brandId]
    );
    if (duplicateName.length > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5382\u724C\u540D\u79F0\u5DF2\u5B58\u5728"
      });
    }
    if (creditCode) {
      const duplicateCreditCode = await query(
        "SELECT id FROM brands WHERE credit_code = ? AND id != ?",
        [creditCode, brandId]
      );
      if (duplicateCreditCode.length > 0) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u7EDF\u4E00\u793E\u4F1A\u4FE1\u7528\u4EE3\u7801\u5DF2\u5B58\u5728"
        });
      }
    }
    const formattedEstablishmentDate = formatDateForDB(establishmentDate);
    const formattedLicenseIssueDate = formatDateForDB(licenseIssueDate);
    await query(
      `UPDATE brands SET
        brand_name = ?, brand_logo = ?, credit_code = ?, company_name = ?, company_type = ?,
        registered_capital = ?, legal_representative = ?, establishment_date = ?, business_term = ?,
        registered_address = ?, business_scope = ?, registration_authority = ?, license_issue_date = ?,
        business_license_photo = ?, status = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?`,
      [
        brandName,
        brandLogo,
        creditCode || null,
        companyName,
        companyType || null,
        registeredCapital || null,
        legalRepresentative || null,
        formattedEstablishmentDate,
        businessTerm || null,
        registeredAddress || null,
        businessScope || null,
        registrationAuthority || null,
        formattedLicenseIssueDate,
        businessLicensePhoto,
        status,
        brandId
      ]
    );
    await logAuditAction({
      action: "ADMIN_UPDATE_BRAND",
      description: `\u7BA1\u7406\u5458\u66F4\u65B0\u5382\u724C: ${brandName}`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        brandId,
        brandName,
        companyName,
        creditCode,
        oldBrandName: existingBrand[0].brand_name
      }
    });
    logger.info("\u7BA1\u7406\u5458\u66F4\u65B0\u5382\u724C\u6210\u529F", {
      adminId: admin.id,
      adminUsername: admin.username,
      brandId,
      brandName,
      companyName
    });
    return {
      success: true,
      message: "\u5382\u724C\u66F4\u65B0\u6210\u529F",
      data: {
        id: brandId,
        brandName,
        companyName
      }
    };
  } catch (error) {
    logger.error("\u7BA1\u7406\u5458\u66F4\u65B0\u5382\u724C\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      brandId: getRouterParam(event, "id"),
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u66F4\u65B0\u5382\u724C\u5931\u8D25"
    });
  }
});

export { _id__put as default };
//# sourceMappingURL=_id_.put.mjs.map
