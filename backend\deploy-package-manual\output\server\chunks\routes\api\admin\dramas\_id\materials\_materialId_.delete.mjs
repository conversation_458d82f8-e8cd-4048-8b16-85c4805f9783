import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, f as createError, q as query, l as logger, e as getClientIP } from '../../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _materialId__delete = defineEventHandler(async (event) => {
  var _a, _b;
  try {
    const dramaId = getRouterParam(event, "id");
    const materialId = getRouterParam(event, "materialId");
    if (!dramaId || !materialId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u77ED\u5267ID\u548C\u7D20\u6750ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const dramaIdNum = parseInt(dramaId);
    const materialIdNum = parseInt(materialId);
    if (isNaN(dramaIdNum) || dramaIdNum <= 0 || isNaN(materialIdNum) || materialIdNum <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684ID\u683C\u5F0F"
      });
    }
    const materialCheck = await query(`
      SELECT id, title, type FROM drama_materials 
      WHERE id = ? AND drama_id = ?
    `, [materialIdNum, dramaIdNum]);
    if (materialCheck.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u7D20\u6750\u4E0D\u5B58\u5728\u6216\u4E0D\u5C5E\u4E8E\u8BE5\u77ED\u5267"
      });
    }
    const material = materialCheck[0];
    await query(`
      DELETE FROM drama_materials WHERE id = ? AND drama_id = ?
    `, [materialIdNum, dramaIdNum]);
    logger.info("\u5220\u9664\u77ED\u5267\u7D20\u6750\u6210\u529F", {
      adminId: ((_a = event.context.admin) == null ? void 0 : _a.id) || "unknown",
      dramaId: dramaIdNum,
      materialId: materialIdNum,
      materialTitle: material.title,
      materialType: material.type,
      ip: getClientIP(event)
    });
    return {
      success: true,
      data: {
        message: "\u7D20\u6750\u5220\u9664\u6210\u529F"
      }
    };
  } catch (error) {
    logger.error("\u5220\u9664\u77ED\u5267\u7D20\u6750\u5931\u8D25", {
      error: error.message,
      dramaId: getRouterParam(event, "id"),
      materialId: getRouterParam(event, "materialId"),
      adminId: (_b = event.context.admin) == null ? void 0 : _b.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { _materialId__delete as default };
//# sourceMappingURL=_materialId_.delete.mjs.map
