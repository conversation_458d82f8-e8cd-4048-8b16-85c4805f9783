import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, Z as verifyUserAccessToken, f as createError, g as getQuery, q as query } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const records_get = defineEventHandler(async (event) => {
  try {
    console.log("Investment records API called");
    const userPayload = verifyUserAccessToken(event);
    if (!userPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u7528\u6237\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"
      });
    }
    const userId = userPayload.id;
    const queryParams = getQuery(event);
    const page = parseInt(queryParams.page) || 1;
    const pageSize = parseInt(queryParams.pageSize) || 10;
    const offset = (page - 1) * pageSize;
    const countQuery = `
      SELECT COUNT(*) as total
      FROM user_investments
      WHERE user_id = ?
    `;
    const countResult = await query(countQuery, [userId]);
    const total = countResult[0].total;
    const recordsQuery = `
      SELECT
        ui.id,
        ui.project_id,
        ui.project_name,
        ui.investment_amount,
        ui.expected_return_rate,
        ui.expected_return_amount,
        COALESCE(ui.actual_return_amount, 0) as actual_return_amount,
        DATE_FORMAT(ui.investment_date, '%Y-%m-%d %H:%i') as investment_date,
        DATE_FORMAT(ui.start_date, '%Y-%m-%d') as start_date,
        DATE_FORMAT(ui.end_date, '%Y-%m-%d') as end_date,
        CASE
          WHEN ui.project_status = 'active' THEN '\u8FDB\u884C\u4E2D'
          WHEN ui.project_status = 'completed' THEN '\u5DF2\u5B8C\u6210'
          WHEN ui.project_status = 'paused' THEN '\u6682\u505C'
          ELSE '\u672A\u77E5'
        END as project_status,
        CASE
          WHEN ui.investment_status = 'active' THEN '\u6709\u6548'
          WHEN ui.investment_status = 'completed' THEN '\u5DF2\u5B8C\u6210'
          WHEN ui.investment_status = 'cancelled' THEN '\u5DF2\u53D6\u6D88'
          ELSE '\u672A\u77E5'
        END as investment_status,
        ui.progress,
        DATEDIFF(ui.end_date, NOW()) as remaining_days,
        COALESCE(uat.transaction_no, CONCAT('INV', LPAD(ui.id, 6, '0'))) as investment_no
      FROM user_investments ui
      LEFT JOIN user_asset_transactions uat ON (
        uat.related_type = 'investment'
        AND uat.related_id = ui.id
        AND uat.transaction_type = 'shells_out'
        AND uat.user_id = ui.user_id
      )
      WHERE ui.user_id = ?
      ORDER BY ui.investment_date DESC
      LIMIT ? OFFSET ?
    `;
    const recordsRows = await query(recordsQuery, [userId, pageSize, offset]);
    const records = recordsRows.map((record) => ({
      id: record.id,
      projectId: record.project_id,
      projectName: record.project_name,
      investmentAmount: parseFloat(record.investment_amount),
      expectedReturnRate: parseFloat(record.expected_return_rate),
      expectedReturnAmount: parseFloat(record.expected_return_amount),
      actualReturnAmount: parseFloat(record.actual_return_amount),
      investmentDate: record.investment_date,
      startDate: record.start_date,
      endDate: record.end_date,
      projectStatus: record.project_status,
      investmentStatus: record.investment_status,
      progress: parseFloat(record.progress),
      remainingDays: record.remaining_days > 0 ? record.remaining_days : 0,
      // 计算收益率
      returnRate: record.investment_amount > 0 ? parseFloat((record.actual_return_amount / record.investment_amount * 100).toFixed(2)) : 0,
      // 使用真实的投资编号（从交易记录表获取）
      investmentNo: record.investment_no
    }));
    return {
      success: true,
      data: {
        records,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize)
        }
      }
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u6295\u8D44\u8BB0\u5F55\u5931\u8D25:", error);
    return {
      success: false,
      message: "\u83B7\u53D6\u6295\u8D44\u8BB0\u5F55\u5931\u8D25",
      error: error.message
    };
  }
});

export { records_get as default };
//# sourceMappingURL=records.get.mjs.map
