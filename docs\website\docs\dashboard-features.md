# 个人中心页面功能说明

## 概述

个人中心页面已按照需求进行了全面优化，实现了贝壳钻石虚拟货币系统和新的左右分栏布局。

## 主要功能

### 1. 整体布局结构

- **左右分栏布局**：左侧显示个人信息卡片，右侧显示数据统计区域
- **响应式设计**：支持不同屏幕尺寸的自适应显示
- **现代化UI**：使用Tailwind CSS实现美观的界面设计

### 2. 左侧个人信息卡片

- **用户头像**：支持头像显示和错误处理
- **用户基本信息**：姓名、等级、最后登录时间
- **等级徽章**：根据投资金额动态显示用户等级
  - 钻石投资人：≥1,000,000贝壳
  - 白金投资人：≥500,000贝壳
  - 金牌投资人：≥100,000贝壳
  - 银牌投资人：≥50,000贝壳
  - 普通投资人：<50,000贝壳

### 3. 右侧数据卡片（4个卡片）

#### 3.1 总投资贝壳卡片
- 显示用户总投资的贝壳数量
- 包含"充值"按钮，支持贝壳充值操作
- 支持多种支付方式：支付宝、微信支付、银行卡

#### 3.2 已消耗贝壳卡片
- 显示已消耗的贝壳总数
- 显示投资的短剧数目统计
- 实时更新投资数据

#### 3.3 累计收益钻石卡片
- 显示用户总共收益的钻石数量
- 包含"提现"按钮，支持钻石提现功能
- 支持银行卡提现，需要交易密码验证

#### 3.4 收益率卡片
- 计算公式：收益率 = 钻石收益 ÷ 已消耗的贝壳数
- 显示实际收益率百分比
- 自动处理除零异常情况

### 4. 图表区域优化

#### 4.1 收益趋势图表（双Y轴）
- **左侧Y轴**：显示总消耗贝壳数据
- **右侧Y轴**：显示收益钻石数据
- **时间维度**：支持月度趋势展示
- **交互功能**：鼠标悬停显示详细数据

#### 4.2 投资分布图表
- 显示用户投资短剧的占比分布情况
- 以饼图形式展示各短剧项目的投资占比
- 支持颜色区分和图例显示

### 5. 项目进度管理

- **项目列表**：显示用户投资的所有短剧项目
- **进度图表**：横向条形图显示项目完成进度
- **详细信息**：投资贝壳、收益钻石、项目状态等
- **状态标识**：不同颜色标识项目状态（进行中、已完成、筹备中、已暂停）

### 6. 充值功能

#### 6.1 充值对话框
- **金额输入**：支持自定义充值金额
- **支付方式选择**：支付宝、微信支付、银行卡
- **支付处理**：二维码支付或跳转支付页面
- **状态轮询**：自动检测支付状态

#### 6.2 支付流程
1. 用户选择充值金额和支付方式
2. 系统生成支付订单
3. 显示支付二维码或跳转支付页面
4. 轮询检查支付状态
5. 支付成功后更新用户贝壳余额

### 7. 提现功能

#### 7.1 提现对话框
- **余额显示**：显示可提现钻石数量
- **金额输入**：支持自定义提现金额，不能超过可用余额
- **银行卡管理**：支持添加和选择银行卡
- **安全验证**：需要输入交易密码

#### 7.2 银行卡管理
- **添加银行卡**：支持添加新的银行卡
- **信息验证**：验证银行卡号、持卡人姓名、身份证号等
- **默认设置**：支持设置默认银行卡
- **信息脱敏**：银行卡号中间部分用*号替代

### 8. 通知系统

- **通知列表**：显示系统通知、项目进展、收益发放等消息
- **未读标识**：红色数字标识未读通知数量
- **分类显示**：不同类型通知使用不同图标和颜色
- **操作功能**：支持标记已读和全部已读

### 9. 数据格式化

- **数字格式化**：千分位分隔符显示
- **百分比格式化**：保留两位小数
- **银行卡脱敏**：中间部分用*号替代
- **状态颜色**：根据状态自动应用对应颜色

## 技术实现

### 前端技术栈
- **Vue 3**：使用Composition API开发
- **TypeScript**：提供类型安全
- **Tailwind CSS**：响应式样式设计
- **ECharts**：图表渲染
- **Ant Design Vue**：UI组件库
- **SweetAlert2**：消息提示

### 后端API接口
- `GET /api/users/dashboard`：获取仪表板数据
- `POST /api/users/wallet/recharge`：贝壳充值
- `POST /api/users/wallet/withdraw`：钻石提现
- `GET /api/users/wallet/payment-methods`：获取支付方式
- `GET /api/users/wallet/bank-cards`：获取银行卡列表
- `POST /api/users/wallet/bank-cards`：添加银行卡

### 数据结构
- **虚拟货币系统**：贝壳（投资）和钻石（收益）
- **用户资产概览**：总投资、已消耗、累计收益、收益率等
- **项目数据**：投资金额、收益情况、进度状态等
- **趋势数据**：支持双Y轴的时间序列数据

## 安全特性

- **用户认证**：JWT令牌验证
- **数据脱敏**：敏感信息自动脱敏显示
- **交易密码**：提现操作需要密码验证
- **参数验证**：前后端双重参数验证
- **错误处理**：统一的错误处理机制

## 性能优化

- **懒加载**：图表组件按需渲染
- **数据缓存**：避免重复请求
- **响应式设计**：适配不同设备
- **图表优化**：支持窗口大小变化时重新渲染

## 用户体验

- **加载状态**：显示加载动画
- **错误提示**：友好的错误消息
- **操作反馈**：及时的操作结果反馈
- **空状态处理**：无数据时的友好提示
- **移动端适配**：支持移动设备访问
