import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, r as readBody, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const investment_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u4FEE\u6539\u6295\u8D44\u8BBE\u7F6E"
      });
    }
    const body = await readBody(event);
    const { minAmount, maxAmount, minReturnRate, platformFee } = body;
    if (typeof minAmount !== "number" || minAmount < 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6700\u5C0F\u6295\u8D44\u91D1\u989D\u5FC5\u987B\u662F\u975E\u8D1F\u6570"
      });
    }
    if (typeof maxAmount !== "number" || maxAmount < minAmount) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6700\u5927\u6295\u8D44\u91D1\u989D\u5FC5\u987B\u5927\u4E8E\u7B49\u4E8E\u6700\u5C0F\u6295\u8D44\u91D1\u989D"
      });
    }
    if (typeof minReturnRate !== "number" || minReturnRate < 0 || minReturnRate > 100) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6700\u4F4E\u6536\u76CA\u7387\u5FC5\u987B\u57280-100\u4E4B\u95F4"
      });
    }
    if (typeof platformFee !== "number" || platformFee < 0 || platformFee > 100) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5E73\u53F0\u8D39\u7387\u5FC5\u987B\u57280-100\u4E4B\u95F4"
      });
    }
    const investmentSettings = {
      minAmount: Number(minAmount),
      maxAmount: Number(maxAmount),
      minReturnRate: Number(minReturnRate),
      platformFee: Number(platformFee)
    };
    const settingsJson = JSON.stringify(investmentSettings);
    const existingSettings = await query(
      "SELECT id FROM system_settings WHERE setting_key = ?",
      ["investment_settings"]
    );
    if (existingSettings.length > 0) {
      await query(
        "UPDATE system_settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?",
        [settingsJson, "investment_settings"]
      );
    } else {
      await query(
        "INSERT INTO system_settings (setting_key, setting_value, created_at, updated_at) VALUES (?, ?, NOW(), NOW())",
        ["investment_settings", settingsJson]
      );
    }
    await logAuditAction({
      action: "ADMIN_UPDATE_INVESTMENT_SETTINGS",
      description: "\u7BA1\u7406\u5458\u66F4\u65B0\u6295\u8D44\u8BBE\u7F6E",
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        updatedSettings: investmentSettings
      }
    });
    logger.info("\u7BA1\u7406\u5458\u66F4\u65B0\u6295\u8D44\u8BBE\u7F6E\u6210\u529F", {
      adminId: admin.id,
      adminUsername: admin.username,
      settings: investmentSettings
    });
    return {
      success: true,
      message: "\u6295\u8D44\u8BBE\u7F6E\u66F4\u65B0\u6210\u529F",
      data: investmentSettings
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u6295\u8D44\u8BBE\u7F6E\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { investment_post as default };
//# sourceMappingURL=investment.post.mjs.map
