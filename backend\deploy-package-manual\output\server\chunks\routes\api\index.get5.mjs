import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, q as query, f as createError } from '../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_get = defineEventHandler(async (event) => {
  try {
    const platforms = await query(`
      SELECT
        id,
        platform_name,
        platform_logo_url,
        platform_type,
        platform_domain
      FROM drama_platforms
      WHERE is_active = 1
      ORDER BY platform_name ASC
    `);
    return {
      success: true,
      message: "\u83B7\u53D6\u5E73\u53F0\u5217\u8868\u6210\u529F",
      data: platforms
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u516C\u5F00\u5E73\u53F0\u5217\u8868\u5931\u8D25:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "\u83B7\u53D6\u5E73\u53F0\u5217\u8868\u5931\u8D25"
    });
  }
});

export { index_get as default };
//# sourceMappingURL=index.get5.mjs.map
