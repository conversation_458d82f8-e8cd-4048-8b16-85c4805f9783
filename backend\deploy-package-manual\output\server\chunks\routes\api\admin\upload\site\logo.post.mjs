import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, n as readMultipartFormData, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const logo_post = defineEventHandler(async (event) => {
  var _a, _b;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u4E0A\u4F20Logo"
      });
    }
    const formData = await readMultipartFormData(event);
    if (!formData || formData.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u8BF7\u9009\u62E9\u8981\u4E0A\u4F20\u7684\u6587\u4EF6"
      });
    }
    const file = formData.find((item) => item.name === "file");
    if (!file || !file.data) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u672A\u627E\u5230\u4E0A\u4F20\u7684\u6587\u4EF6"
      });
    }
    const allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"];
    if (!allowedTypes.includes(file.type || "")) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u53EA\u652F\u6301 JPEG\u3001PNG\u3001GIF\u3001WebP \u683C\u5F0F\u7684\u56FE\u7247"
      });
    }
    const maxSize = 2 * 1024 * 1024;
    if (file.data.length > maxSize) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6587\u4EF6\u5927\u5C0F\u4E0D\u80FD\u8D85\u8FC72MB"
      });
    }
    const ext = ((_a = file.filename) == null ? void 0 : _a.split(".").pop()) || "jpg";
    const filename = `logo_${Date.now()}.${ext}`;
    const uploadPath = `uploads/site/${filename}`;
    const fileUrl = `/uploads/site/${filename}`;
    await logAuditAction({
      action: "ADMIN_UPLOAD_SITE_LOGO",
      description: "\u7BA1\u7406\u5458\u4E0A\u4F20\u7F51\u7AD9Logo",
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: { filename, size: file.data.length, type: file.type }
    });
    return {
      success: true,
      data: {
        url: fileUrl,
        filename,
        size: file.data.length
      }
    };
  } catch (error) {
    logger.error("\u4E0A\u4F20\u7F51\u7AD9Logo\u5931\u8D25", {
      error: error.message,
      adminId: (_b = event.context.admin) == null ? void 0 : _b.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { logo_post as default };
//# sourceMappingURL=logo.post.mjs.map
