import { c as defineEvent<PERSON>and<PERSON>, g as getQuery, q as query, f as createError } from '../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_get = defineEventHandler(async (event) => {
  try {
    const query_params = getQuery(event);
    const { limit = 20 } = query_params;
    const limitNum = Math.min(Number(limit) || 20, 100);
    const brands = await query(
      `SELECT 
        id,
        brand_name,
        brand_logo,
        company_name,
        created_at
      FROM brands 
      WHERE status = 'active'
      ORDER BY created_at DESC 
      LIMIT ?`,
      [limitNum]
    );
    const formattedBrands = brands.map((brand) => ({
      id: brand.id,
      name: brand.brand_name,
      logo: brand.brand_logo,
      companyName: brand.company_name,
      createdAt: brand.created_at
    }));
    return {
      success: true,
      data: formattedBrands,
      total: formattedBrands.length
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u5382\u724C\u5217\u8868\u5931\u8D25:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "\u83B7\u53D6\u5382\u724C\u5217\u8868\u5931\u8D25"
    });
  }
});

export { index_get as default };
//# sourceMappingURL=index.get2.mjs.map
