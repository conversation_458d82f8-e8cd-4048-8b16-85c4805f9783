import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, Z as verifyUserAccessToken, f as createError, j as getRouter<PERSON>aram, U as findUserById, l as logger, e as getClientIP } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _orderId__get = defineEventHandler(async (event) => {
  var _a;
  try {
    const userPayload = verifyUserAccessToken(event);
    if (!userPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u7528\u6237\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"
      });
    }
    const orderId = getRouterParam(event, "orderId");
    if (!orderId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u8BA2\u5355ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const user = await findUserById(userPayload.id);
    if (!user) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u7528\u6237\u4E0D\u5B58\u5728"
      });
    }
    if (user.status !== 1) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u7528\u6237\u8D26\u6237\u5DF2\u88AB\u7981\u7528"
      });
    }
    const mockOrderStatus = {
      status: orderId.includes("SUCCESS") ? "success" : orderId.includes("FAILED") ? "failed" : orderId.includes("CANCELLED") ? "cancelled" : "pending",
      amount: 1e5,
      // 模拟充值金额
      createdAt: (/* @__PURE__ */ new Date()).toISOString(),
      paidAt: orderId.includes("SUCCESS") ? (/* @__PURE__ */ new Date()).toISOString() : void 0
    };
    logger.info("\u67E5\u8BE2\u5145\u503C\u8BA2\u5355\u72B6\u6001", {
      userId: user.id,
      orderId,
      status: mockOrderStatus.status,
      ip: getClientIP(event)
    });
    return {
      success: true,
      data: mockOrderStatus
    };
  } catch (error) {
    logger.error("\u67E5\u8BE2\u5145\u503C\u8BA2\u5355\u72B6\u6001\u5931\u8D25", {
      error: error.message,
      userId: (_a = event.context.user) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { _orderId__get as default };
//# sourceMappingURL=_orderId_.get.mjs.map
