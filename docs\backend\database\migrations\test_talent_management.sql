-- 测试演艺经纪管理功能的数据
USE mengtu;

-- 插入一些测试艺人数据
INSERT INTO actors (name, avatar_url, bio, tags, role_type, sort_order, is_active, created_at, updated_at) VALUES
('张艺谋', 'https://example.com/zhangym.jpg', '著名导演，擅长大制作影片', '导演,大片,视觉', '导演', 1, 1, NOW(), NOW()),
('徐峥', 'https://example.com/xuzheng.jpg', '知名演员兼导演，喜剧片专家', '喜剧,导演,演员', '导演', 2, 1, NOW(), NOW()),
('宁浩', 'https://example.com/ninghao.jpg', '新生代导演，黑色幽默风格', '黑色幽默,新导演', '导演', 3, 1, NOW(), NOW()),
('王家卫', 'https://example.com/wjw.jpg', '文艺片导演，独特美学风格', '文艺,美学,港片', '导演', 4, 1, NOW(), NOW()),
('陈可辛', 'https://example.com/ckx.jpg', '资深制片人兼导演', '制片,导演,商业片', '制片人', 5, 1, NOW(), NOW()),
('江志强', 'https://example.com/jzq.jpg', '知名制片人，多部大片制片', '制片,大片,商业', '制片人', 6, 1, NOW(), NOW()),
('韩三平', 'https://example.com/hsp.jpg', '资深出品人，中影集团', '出品,中影,大制作', '出品人', 7, 1, NOW(), NOW()),
('于冬', 'https://example.com/yudong.jpg', '博纳影业总裁，知名出品人', '出品,博纳,商业片', '出品人', 8, 1, NOW(), NOW()),
('芦苇', 'https://example.com/luwei.jpg', '著名编剧，多部经典作品', '编剧,经典,文学', '编剧', 9, 1, NOW(), NOW()),
('刘震云', 'https://example.com/lzy.jpg', '知名作家编剧', '编剧,作家,文学', '编剧', 10, 1, NOW(), NOW()),
('易小星', 'https://example.com/yxx.jpg', '网络电影导演', '网络电影,新媒体', '导演', 11, 1, NOW(), NOW()),
('白一骢', 'https://example.com/byc.jpg', '知名制片人，网剧专家', '制片,网剧,新媒体', '制片人', 12, 1, NOW(), NOW());

-- 查看插入结果
SELECT id, name, role_type, tags, is_active, created_at 
FROM actors 
WHERE role_type IN ('导演', '制片人', '出品人', '编剧')
ORDER BY sort_order;

-- 统计各身份类型数量
SELECT role_type, COUNT(*) as count 
FROM actors 
GROUP BY role_type 
ORDER BY count DESC;
