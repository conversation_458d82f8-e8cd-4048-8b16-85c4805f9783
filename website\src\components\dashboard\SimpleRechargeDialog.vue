<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'
import Swal from 'sweetalert2'
import { getContactSettings, type ContactSettings } from '../../services/contactService'

import {
  AgreementType,
  shouldDefaultCheck,
  setAgreementAgreed,
  markAgreementCompleted
} from '../../utils/agreementStorage'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'success'])

// 路由实例
const router = useRouter()

// 充值金额
const amount = ref<number | null>(null)
// 自定义金额输入
const customAmount = ref<string>('')
// 加载状态
const loading = ref<boolean>(false)
// 支付信息
const paymentInfo = ref<any>(null)
// 联系方式设置
const contactSettings = ref<ContactSettings>({
  onlineQrCode: '',
  paymentQrCode: '',
  contactText: '',
  contactAddress: '',
  contactEmail: '',
  contactPhone: ''
})
// 协议相关
const agreeRechargeTerms = ref(false)
const showAgreementError = ref(false)
const agreementContainer = ref(null)

// 预设充值金额选项
const presetAmounts = [
  { label: '1万贝壳', value: 10000 },
  { label: '5万贝壳', value: 50000 },
  { label: '10万贝壳', value: 100000 },
  { label: '20万贝壳', value: 200000 },
  { label: '50万贝壳', value: 500000 },
  { label: '100万贝壳', value: 1000000 }
]

// 计算最终充值金额
const finalAmount = computed(() => {
  if (amount.value !== null) {
    return amount.value
  }
  if (customAmount.value) {
    const parsed = parseFloat(customAmount.value)
    return isNaN(parsed) ? 0 : parsed
  }
  return 0
})

// 选择预设金额
const selectAmount = (value: number) => {
  amount.value = value
  customAmount.value = ''
}

// 选择自定义金额
const selectCustomAmount = () => {
  amount.value = null
}

// 关闭弹窗
const closeDialog = () => {
  // 重置表单
  amount.value = null
  customAmount.value = ''
  loading.value = false
  paymentInfo.value = null
  agreeRechargeTerms.value = false
  showAgreementError.value = false
  emit('close')
}

// 提交充值请求
const submitRecharge = async () => {
  const rechargeAmount = finalAmount.value

  if (!rechargeAmount || rechargeAmount <= 0) {
    Swal.fire({
      title: '请输入有效的充值金额',
      icon: 'warning'
    })
    return
  }

  if (rechargeAmount < 100) {
    Swal.fire({
      title: '充值金额不能少于100贝壳',
      icon: 'warning'
    })
    return
  }

  // 验证是否同意充值协议
  if (!agreeRechargeTerms.value) {
    showAgreementError.value = true
    return
  }

  try {
    loading.value = true
    
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'https://api.qinghee.com.cn/api'
    const response = await axios.post(`${apiBaseUrl}/users/wallet/recharge`, {
      amount: rechargeAmount
    })

    if (response.data.success) {
      // 保存支付信息，直接显示待支付界面
      paymentInfo.value = response.data.data
      // 标记充值协议已完成（用户已提交充值请求）
      markAgreementCompleted(AgreementType.RECHARGE)
      // 触发成功事件
      emit('success', response.data.data)
    } else {
      throw new Error(response.data.message || '充值失败')
    }
  } catch (error: any) {
    console.error('充值请求失败:', error)
    Swal.fire({
      title: '充值失败',
      text: error.response?.data?.message || error.message || '请稍后再试',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 加载联系方式设置
const loadContactSettings = async () => {
  try {
    const settings = await getContactSettings()
    if (settings) {
      contactSettings.value = settings
    }
  } catch (error) {
    console.error('获取联系方式设置失败:', error)
  }
}

// 组件挂载时加载联系方式设置和初始化协议状态
onMounted(() => {
  loadContactSettings()
  initAgreementStatus()
})

// 复制订单号
const copyOrderId = async () => {
  if (paymentInfo.value?.orderId) {
    try {
      await navigator.clipboard.writeText(paymentInfo.value.orderId)
      Swal.fire({
        title: '复制成功！',
        text: '订单号已复制到剪贴板',
        icon: 'success',
        timer: 1500,
        showConfirmButton: false
      })
    } catch (error) {
      console.error('复制失败:', error)
      Swal.fire({
        title: '复制失败',
        text: '请手动复制订单号',
        icon: 'error'
      })
    }
  }
}

// 打开协议页面
const openAgreement = (type: string) => {
  // 在新标签页中打开协议页面
  const routeUrl = router.resolve({ name: 'agreement', params: { type } })
  window.open(routeUrl.href, '_blank')
}

// 清除协议错误提示
const clearAgreementError = () => {
  showAgreementError.value = false
}

// 初始化协议状态
const initAgreementStatus = async () => {
  try {
    const shouldCheck = await shouldDefaultCheck(AgreementType.RECHARGE)
    agreeRechargeTerms.value = shouldCheck
  } catch (error) {
    console.error('初始化协议状态失败:', error)
    agreeRechargeTerms.value = false
  }
}

// 监听协议勾选状态变化
const handleAgreementChange = () => {
  setAgreementAgreed(AgreementType.RECHARGE, agreeRechargeTerms.value)
  clearAgreementError()
}
</script>

<template>
  <!-- 充值弹窗 -->
  <div v-if="visible" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
      <!-- 弹窗标题 -->
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-lg font-semibold text-gray-900">充值投资贝壳</h3>
        <button @click="closeDialog" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- 充值表单 -->
      <div v-if="!paymentInfo">
        <!-- 预设金额选择 -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-3">选择充值金额</label>
        <div class="grid grid-cols-2 gap-3">
          <button
            v-for="preset in presetAmounts"
            :key="preset.value"
            @click="selectAmount(preset.value)"
            :class="[
              'p-3 border rounded-lg text-center transition-colors',
              amount === preset.value
                ? 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-300 hover:border-gray-400'
            ]"
          >
            {{ preset.label }}
          </button>
        </div>
      </div>

      <!-- 自定义金额输入 -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-2">或输入自定义金额</label>
        <div class="relative">
          <input
            v-model="customAmount"
            @focus="selectCustomAmount"
            type="number"
            placeholder="请输入充值金额"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            min="100"
            step="100"
          />
          <span class="absolute right-3 top-2 text-gray-500">贝壳</span>
        </div>
        <p class="text-xs text-gray-500 mt-1">最低充值金额：100贝壳</p>
      </div>

      <!-- 充值金额确认 -->
      <div v-if="finalAmount > 0" class="mb-6 p-4 bg-blue-50 rounded-lg">
        <div class="flex justify-between items-center">
          <span class="text-sm text-gray-600">充值金额：</span>
          <span class="text-lg font-semibold text-blue-600">{{ finalAmount.toLocaleString() }} 贝壳</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex space-x-3">
        <button
          @click="closeDialog"
          class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          :disabled="loading"
        >
          取消
        </button>
        <button
          @click="submitRecharge"
          :disabled="loading || finalAmount <= 0"
          class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          <span v-if="loading" class="flex items-center justify-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            充值中...
          </span>
          <span v-else>确认充值</span>
        </button>
      </div>

      <!-- 充值协议勾选框 -->
      <div class="mt-4">
        <div
          ref="agreementContainer"
          class="flex items-center justify-center space-x-2"
          :class="{ 'shake-animation': showAgreementError }"
        >
          <input
            type="checkbox"
            id="agreeRechargeTerms"
            v-model="agreeRechargeTerms"
            class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            @change="handleAgreementChange"
          >
          <label for="agreeRechargeTerms" class="text-sm text-gray-600 leading-relaxed cursor-pointer whitespace-nowrap">
            我已阅读并同意<button
              type="button"
              @click="openAgreement('recharge-agreement')"
              class="text-blue-600 hover:text-blue-800 hover:underline font-medium"
            >《充值协议》</button>
          </label>
        </div>

        <!-- 协议错误提示 -->
        <div v-if="showAgreementError" class="text-center mt-1">
          <span class="text-red-500 text-xs">请先同意《充值协议》</span>
        </div>
      </div>

        <!-- 充值说明 -->
        <div class="mt-4 text-xs text-gray-500">
          <p>• 贝壳和人民币的比例为1：1</p>
          <p>• 充值后的贝壳可用于投资短剧项目</p>
          <p>• 暂时只支持线下充值的形式，添加客服微信好友充值(工作时间：24小时)</p>
          <p>• 充值记录将保存在您的账户中</p>
        </div>
      </div>

      <!-- 待支付订单界面 -->
      <div v-else class="text-center">
        <div class="mb-6">
          <h3 class="text-lg font-semibold mb-4 text-gray-900">请完成支付</h3>
          <div class="bg-blue-50 rounded-lg p-4 mb-4">
            <p class="text-sm text-gray-600 mb-1">订单号</p>
            <p class="text-lg font-mono text-blue-600 mb-3">{{ paymentInfo?.orderId }}</p>
            <p class="text-sm text-gray-600 mb-1">充值金额</p>
            <p class="text-2xl font-bold text-blue-600">{{ finalAmount?.toLocaleString() }} 贝壳</p>
          </div>
        </div>

        <!-- 线下支付二维码 -->
        <div class="mb-6">
          <div class="bg-gray-100 rounded-lg p-6 mb-4">
            <div class="w-48 h-48 mx-auto bg-white rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
              <img
                v-if="contactSettings.paymentQrCode"
                :src="contactSettings.paymentQrCode"
                alt="线下支付二维码"
                class="w-full h-full object-contain rounded-lg"
              />
              <div v-else class="text-center text-gray-500">
                <svg class="w-12 h-12 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
                <p class="text-sm">客服微信二维码</p>
              </div>
            </div>
          </div>
          <div class="text-sm text-red-600 space-y-2 text-left">
            <p class="font-medium">支付步骤：</p>
            <p>1. 扫描上方二维码添加客服微信</p>
            <p>2. 发送订单号：{{ paymentInfo?.orderId }}</p>
            <p>3. 告知充值金额：{{ finalAmount?.toLocaleString() }} 贝壳</p>
            <p>4. 您可以在充值记录中查看自己订单的状态</p>
          </div>
        </div>

        <!-- 客服信息 -->
        <div class="bg-green-50 rounded-lg p-4 mb-6">
          <div class="flex items-center justify-center mb-2">
            <svg class="w-5 h-5 text-green-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span class="text-sm font-medium text-green-800">客服工作时间：24小时</span>
          </div>
          <p class="text-xs text-green-700">支付完成后，贝壳将自动充值到您的账户</p>
        </div>

        <!-- 操作按钮 -->
        <div class="flex space-x-3">
          <button
            @click="closeDialog"
            class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            关闭
          </button>
          <button
            @click="copyOrderId"
            class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            复制订单号
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 确保数字输入框样式正确 */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

/* 抖动动画 */
.shake-animation {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}
</style>
