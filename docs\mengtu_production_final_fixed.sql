-- 剧投投生产环境数据库完整版本（已修复外键约束顺序）
-- MySQL dump 10.13  Distrib 5.6.46, for Win64 (x86_64)
--
-- 原始主机: 127.0.0.5    数据库: mengtu
-- 生产环境数据库: mengtu
-- ------------------------------------------------------
-- 服务器版本: 5.6.46-log
--
-- 说明：此文件包含完整的表结构和数据，已按照外键依赖关系重新排序
-- 执行方式：直接导入到生产环境的 mengtu 数据库
-- 特点：使用 DROP TABLE IF EXISTS，会完全替换现有表结构
-- 修复：解决了 #1215 外键约束错误，确保被引用的表在引用它的表之前创建
--
-- 使用方法：
-- 1. 在宝塔面板 phpMyAdmin 中选择 mengtu 数据库
-- 2. 点击"导入"选项卡
-- 3. 选择此文件并执行
-- 4. 所有表将被重新创建并填充数据

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- ========================================
-- 第一层：无依赖的基础表
-- ========================================

--
-- Table structure for table `actors`
--

DROP TABLE IF EXISTS `actors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `actors` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '艺人姓名',
  `avatar_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像图片URL',
  `bio` text COLLATE utf8mb4_unicode_ci COMMENT '简介JSON格式，包含description、gender、age、height、weight和experience等信息',
  `tags` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签，逗号分隔，如：歌手,演员,模特',
  `role_type` enum('出品人','联合出品人','总制片人','制片人','联合制片人','导演','监制','编剧','统筹','演员') COLLATE utf8mb4_unicode_ci DEFAULT '演员',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `is_active` tinyint(4) DEFAULT '1' COMMENT '是否展示，1展示，0隐藏',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sort_active` (`sort_order`,`is_active`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admin_permissions`
--

DROP TABLE IF EXISTS `admin_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限码',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限名称',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '权限描述',
  `module` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '所属模块',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `idx_code` (`code`),
  KEY `idx_module` (`module`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='后台管理权限码表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admin_roles`
--

DROP TABLE IF EXISTS `admin_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
  `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色代码',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '角色描述',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `code` (`code`),
  KEY `idx_code` (`code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='后台管理角色表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admins`
--

DROP TABLE IF EXISTS `admins`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admins` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '管理员用户名',
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号',
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `real_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '真实姓名',
  `avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像URL',
  `home_path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '首页路径',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `reset_token` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '密码重置令牌',
  `reset_token_expiry` timestamp NULL DEFAULT NULL COMMENT '重置令牌过期时间',
  `tokens_invalidated_at` timestamp NULL DEFAULT NULL COMMENT '令牌失效时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `phone` (`phone`),
  KEY `idx_username` (`username`),
  KEY `idx_email` (`email`),
  KEY `idx_phone` (`phone`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='后台管理员表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `audit_log`
--

DROP TABLE IF EXISTS `audit_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `audit_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned DEFAULT NULL,
  `username` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_type` enum('admin','investor') COLLATE utf8mb4_unicode_ci NOT NULL,
  `action` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_action` (`action`(191)),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=452 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `audit_logs`
--

DROP TABLE IF EXISTS `audit_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `audit_logs` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned DEFAULT NULL COMMENT '鐢ㄦ埛ID',
  `username` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '鐢ㄦ埛鍚',
  `user_type` enum('admin','user') COLLATE utf8mb4_unicode_ci DEFAULT 'admin' COMMENT '鐢ㄦ埛绫诲瀷',
  `action` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '鎿嶄綔鍔ㄤ綔',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '鎿嶄綔鎻忚堪',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP鍦板潃',
  `user_agent` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '鐢ㄦ埛浠g悊',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '鍒涘缓鏃堕棿',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_action` (`action`(191)),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审计日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `banners`
--

DROP TABLE IF EXISTS `banners`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `banners` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '轮播图标题',
  `subtitle` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '轮播图副标题',
  `image_url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '图片URL',
  `link_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '点击跳转链接',
  `link_type` enum('internal','external') COLLATE utf8mb4_unicode_ci DEFAULT 'internal' COMMENT '链接类型：internal内部链接，external外部链接',
  `sort_order` int(10) unsigned DEFAULT '0' COMMENT '排序顺序，数字越小越靠前',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用：1启用，0禁用',
  `start_time` datetime DEFAULT NULL COMMENT '开始显示时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束显示时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_time_range` (`start_time`,`end_time`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='轮播图管理表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `brands`
--

DROP TABLE IF EXISTS `brands`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `brands` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `brand_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '厂牌名称',
  `brand_logo` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '厂牌图标URL',
  `credit_code` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '统一社会信用代码',
  `company_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公司名称',
  `legal_representative` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '法定代表人',
  `registered_capital` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '注册资本',
  `establishment_date` date DEFAULT NULL COMMENT '成立日期',
  `business_scope` text COLLATE utf8mb4_unicode_ci COMMENT '经营范围',
  `registered_address` text COLLATE utf8mb4_unicode_ci COMMENT '注册地址',
  `contact_phone` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系邮箱',
  `website_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '官网地址',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '厂牌简介',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用：1启用，0禁用',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `credit_code` (`credit_code`),
  KEY `idx_brand_name` (`brand_name`(100)),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='厂牌信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `drama_series` (重要：必须在 drama_documents 之前创建)
--

DROP TABLE IF EXISTS `drama_series`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `drama_series` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '鐭?墽鍞?竴鏍囪瘑ID',
  `title` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '鐭?墽鏍囬?/鍚嶇О',
  `cover` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '鐭?墽灏侀潰鍥剧墖URL',
  `tags` text COLLATE utf8mb4_unicode_ci COMMENT '鐭?墽鏍囩?锛屽?涓?爣绛剧敤閫楀彿鍒嗛殧锛岄渶瑕佷粠鏍囩?閰嶇疆涓??鎷',
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '鐭?墽绠?粙鎻忚堪',
  `episodes` int(11) DEFAULT '12' COMMENT '鎬婚泦鏁',
  `episode_length` int(11) DEFAULT '5' COMMENT '鍗曢泦鏃堕暱锛堝垎閽燂級',
  `target_platform` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '鐩?爣鎾?斁骞冲彴锛岄渶瑕佷粠骞冲彴閰嶇疆涓??鎷',
  `projected_views` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '棰勮?鎾?斁閲',
  `cast` text COLLATE utf8mb4_unicode_ci COMMENT '婕斿憳闃靛?锛屽?涓?紨鍛樼敤閫楀彿鍒嗛殧',
  `is_online` tinyint(1) DEFAULT '1' COMMENT '鏄?惁涓婄嚎鏄剧ず锛?(涓婄嚎)銆?(涓嬬嚎)',
  `creator_id` int(11) DEFAULT NULL COMMENT '鍒涘缓鑰呯敤鎴稩D',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '鍒涘缓鏃堕棿',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '鏇存柊鏃堕棿',
  PRIMARY KEY (`id`),
  KEY `idx_drama_series_is_online` (`is_online`),
  KEY `idx_drama_series_creator_id` (`creator_id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='鐭?墽鍩虹?淇℃伅琛';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `drama_tags`
--

DROP TABLE IF EXISTS `drama_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `drama_tags` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标签名称',
  `font_color` varchar(7) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '#000000' COMMENT '字体颜色',
  `background_color` varchar(7) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '#ffffff' COMMENT '背景颜色',
  `click_count` int(11) NOT NULL DEFAULT '0' COMMENT '点击数',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_click_count` (`click_count`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短剧标签表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `drama_platforms`
--

DROP TABLE IF EXISTS `drama_platforms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `drama_platforms` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `platform_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `platform_logo_url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `company_name` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_id` bigint(20) unsigned DEFAULT NULL,
  `contact_person` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `platform_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `is_active` tinyint(1) DEFAULT '1',
  `sort_order` int(11) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_platform_name` (`platform_name`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短剧播放平台表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `funds`
--

DROP TABLE IF EXISTS `funds`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `funds` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '基金代码',
  `title` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '基金名称',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '基金简介',
  `type` enum('equity','debt','mixed') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '基金类型:股权型/债权型/混合型',
  `risk_level` enum('low','medium','high') COLLATE utf8mb4_unicode_ci DEFAULT 'medium' COMMENT '风险等级:低风险/中风险/高风险',
  `min_investment` decimal(15,2) DEFAULT '1000.00' COMMENT '最小投资金额',
  `max_investment` decimal(15,2) DEFAULT NULL COMMENT '最大投资金额',
  `expected_return` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '预期收益率',
  `investment_period` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '投资期限',
  `fund_manager` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '基金经理',
  `management_fee` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '管理费率',
  `performance_fee` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业绩报酬',
  `status` enum('募集中','运行中','清算中','已结束') COLLATE utf8mb4_unicode_ci DEFAULT '募集中' COMMENT '基金状态',
  `launch_date` date DEFAULT NULL COMMENT '成立日期',
  `maturity_date` date DEFAULT NULL COMMENT '到期日期',
  `total_size` decimal(15,2) DEFAULT NULL COMMENT '基金规模',
  `current_size` decimal(15,2) DEFAULT '0.00' COMMENT '当前规模',
  `nav` decimal(8,4) DEFAULT '1.0000' COMMENT '单位净值',
  `cumulative_nav` decimal(8,4) DEFAULT '1.0000' COMMENT '累计净值',
  `is_featured` tinyint(1) DEFAULT '0' COMMENT '是否推荐',
  `sort_order` int(10) unsigned DEFAULT '0' COMMENT '排序',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_featured` (`is_featured`),
  KEY `idx_sort` (`sort_order`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='基金产品表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `news_categories`
--

DROP TABLE IF EXISTS `news_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `news_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `slug` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'URL别名',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '分类描述',
  `parent_id` int(11) DEFAULT NULL COMMENT '父分类ID',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `idx_parent` (`parent_id`),
  KEY `idx_sort` (`sort_order`),
  KEY `idx_slug` (`slug`),
  CONSTRAINT `news_categories_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `news_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻分类表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `news_tags`
--

DROP TABLE IF EXISTS `news_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `news_tags` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标签名称',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻标签表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `posts`
--

DROP TABLE IF EXISTS `posts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `posts` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '推文ID',
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '推文标题',
  `slug` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '唯一标识（用于URL）',
  `content` longtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '推文内容',
  `author` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '作者/操作人',
  `excerpt` text COLLATE utf8mb4_unicode_ci COMMENT '摘要',
  `featured_image` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '特色图片',
  `status` enum('draft','published','archived') COLLATE utf8mb4_unicode_ci DEFAULT 'draft' COMMENT '状态：草稿/已发布/已归档',
  `is_featured` tinyint(1) DEFAULT '0' COMMENT '是否置顶',
  `view_count` int(11) DEFAULT '0' COMMENT '浏览次数',
  `like_count` int(11) DEFAULT '0' COMMENT '点赞次数',
  `comment_count` int(11) DEFAULT '0' COMMENT '评论次数',
  `published_at` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `idx_status` (`status`),
  KEY `idx_featured` (`is_featured`),
  KEY `idx_published` (`published_at`),
  KEY `idx_view_count` (`view_count`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推文表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `system_settings`
--

DROP TABLE IF EXISTS `system_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `system_settings` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '设置键名',
  `setting_value` text COLLATE utf8mb4_unicode_ci COMMENT '设置JSON值',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`),
  KEY `idx_setting_key` (`setting_key`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `token_blacklist`
--

DROP TABLE IF EXISTS `token_blacklist`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `token_blacklist` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `token_hash` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `exp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `token_hash` (`token_hash`),
  KEY `idx_exp` (`exp`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='JWT令牌黑名单';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password_hash` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码哈希',
  `phone` varchar(15) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号码',
  `email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '电子邮箱',
  `real_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '真实姓名',
  `id_card` varchar(18) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号',
  `avatar_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像URL',
  `gender` enum('male','female','other') COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '性别',
  `birth_date` date DEFAULT NULL COMMENT '出生日期',
  `address` text COLLATE utf8mb4_unicode_ci COMMENT '地址',
  `bio` text COLLATE utf8mb4_unicode_ci COMMENT '个人简介',
  `status` enum('active','inactive','suspended','deleted') COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT '账户状态',
  `email_verified` tinyint(1) DEFAULT '0' COMMENT '邮箱是否验证',
  `phone_verified` tinyint(1) DEFAULT '0' COMMENT '手机是否验证',
  `kyc_status` enum('pending','approved','rejected') COLLATE utf8mb4_unicode_ci DEFAULT 'pending' COMMENT 'KYC认证状态',
  `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后登录IP',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `phone` (`phone`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `id_card` (`id_card`),
  KEY `idx_status` (`status`),
  KEY `idx_kyc_status` (`kyc_status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

-- ========================================
-- 第二层：依赖第一层表的表
-- ========================================

--
-- Table structure for table `admin_menus`
--

DROP TABLE IF EXISTS `admin_menus`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_menus` (
  `id` int(11) NOT NULL,
  `pid` int(11) DEFAULT NULL COMMENT '父菜单ID',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单名称',
  `path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '路由路径',
  `component` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组件路径',
  `type` enum('catalog','menu','button','embedded','link') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单类型',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `auth_code` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限码',
  `icon` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图标',
  `meta` text COLLATE utf8mb4_unicode_ci COMMENT '元数据(JSON格式)',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_pid` (`pid`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_auth_code` (`auth_code`),
  CONSTRAINT `admin_menus_ibfk_1` FOREIGN KEY (`pid`) REFERENCES `admin_menus` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='后台管理菜单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admin_role_permissions`
--

DROP TABLE IF EXISTS `admin_role_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_role_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL,
  `permission_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_role_permission` (`role_id`,`permission_code`),
  KEY `permission_code` (`permission_code`),
  CONSTRAINT `admin_role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `admin_roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `admin_role_permissions_ibfk_2` FOREIGN KEY (`permission_code`) REFERENCES `admin_permissions` (`code`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admin_role_relations`
--

DROP TABLE IF EXISTS `admin_role_relations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_role_relations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_admin_role` (`admin_id`,`role_id`),
  KEY `role_id` (`role_id`),
  CONSTRAINT `admin_role_relations_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE,
  CONSTRAINT `admin_role_relations_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `admin_roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员角色关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `drama_additional_info`
--

DROP TABLE IF EXISTS `drama_additional_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `drama_additional_info` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '鍏朵粬淇℃伅ID',
  `drama_id` int(11) NOT NULL COMMENT '鍏宠仈鐨勭煭鍓?D',
  `risk_management` text COLLATE utf8mb4_unicode_ci COMMENT '椋庨櫓绠＄悊鎺?柦锛堢粦瀹氬埌椋庨櫓鎻愮ず锛屾煡鐪嬩紒涓氫俊鎭?級',
  `confirmed_resources` text COLLATE utf8mb4_unicode_ci COMMENT '宸茬'璁よ祫婧',
  `investment_tiers` text COLLATE utf8mb4_unicode_ci COMMENT '鎶曡祫妗ｄ綅淇℃伅',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '鍒涘缓鏃堕棿',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '鏇存柊鏃堕棿',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_drama_additional_info` (`drama_id`),
  KEY `idx_drama_additional_info_drama_id` (`drama_id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='鐭?墽鍏朵粬淇℃伅琛';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `drama_documents` (现在可以安全创建，因为 drama_series 已存在)
--

DROP TABLE IF EXISTS `drama_documents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `drama_documents` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '文档ID',
  `drama_id` int(11) NOT NULL COMMENT '关联的短剧ID',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文档名称',
  `file_url` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件URL地址',
  `file_type` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件类型（PDF、DOC、DOCX等）',
  `file_size` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件大小（格式化后的字符串）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_drama_id` (`drama_id`),
  KEY `idx_drama_name` (`drama_id`,`name`(100)),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_drama_documents_drama_id` FOREIGN KEY (`drama_id`) REFERENCES `drama_series` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短剧文档表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `drama_funding_info`
--

DROP TABLE IF EXISTS `drama_funding_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `drama_funding_info` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '鍕熻祫淇℃伅ID',
  `drama_id` int(11) NOT NULL COMMENT '鍏宠仈鐨勭煭鍓?D',
  `funding_goal` decimal(15,2) DEFAULT NULL COMMENT '鍕熻祫鐩?爣閲戦?锛堝厓锛',
  `current_funding` decimal(15,2) DEFAULT '0.00' COMMENT '褰撳墠宸插嫙璧勯噾棰濓紙鍏冿級',
  `funding_end_date` datetime DEFAULT NULL COMMENT '鍕熻祫缁撴潫鏃堕棿',
  `funding_progress` decimal(5,2) DEFAULT '0.00' COMMENT '鍕熻祫杩涘害锛堢櫨鍒嗘瘮锛',
  `investor_count` int(11) DEFAULT '0' COMMENT '鎶曡祫浜烘暟',
  `min_investment_amount` decimal(15,2) DEFAULT '1000.00' COMMENT '鏈?皬鎶曡祫閲戦?',
  `max_investment_amount` decimal(15,2) DEFAULT NULL COMMENT '鏈?ぇ鎶曡祫閲戦?',
  `expected_return_rate` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '棰勬湡鏀剁泭鐜',
  `investment_period` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '鎶曡祫鍛ㄦ湡',
  `risk_level` enum('low','medium','high') COLLATE utf8mb4_unicode_ci DEFAULT 'medium' COMMENT '椋庨櫓绛夌骇',
  `funding_status` enum('preparing','active','paused','completed','failed') COLLATE utf8mb4_unicode_ci DEFAULT 'preparing' COMMENT '鍕熻祫鐘舵?',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '鍒涘缓鏃堕棿',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '鏇存柊鏃堕棿',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_drama_funding_info` (`drama_id`),
  KEY `idx_drama_funding_info_drama_id` (`drama_id`),
  KEY `idx_drama_funding_info_status` (`funding_status`),
  KEY `idx_drama_funding_info_end_date` (`funding_end_date`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='鐭?墽鍕熻祫淇℃伅琛';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `drama_investment_tiers`
--

DROP TABLE IF EXISTS `drama_investment_tiers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `drama_investment_tiers` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '投资档位ID',
  `drama_id` int(11) NOT NULL COMMENT '关联的短剧ID',
  `tier_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '妗ｄ綅鍚嶇О',
  `min_amount` decimal(15,2) NOT NULL COMMENT '最小投资金额',
  `max_amount` decimal(15,2) DEFAULT NULL COMMENT '最大投资金额',
  `expected_return` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '预期收益',
  `benefits` text COLLATE utf8mb4_unicode_ci COMMENT '投资权益',
  `available_shares` int(11) DEFAULT NULL COMMENT '可投资份额',
  `sold_shares` int(11) DEFAULT '0' COMMENT '已售份额',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_drama_investment_tiers_drama_id` (`drama_id`),
  KEY `idx_drama_investment_tiers_active` (`is_active`),
  KEY `idx_drama_investment_tiers_sort` (`sort_order`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短剧投资档位表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `drama_materials`
--

DROP TABLE IF EXISTS `drama_materials`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `drama_materials` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `drama_id` int(11) NOT NULL COMMENT '关联的短剧ID',
  `title` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '素材标题',
  `url` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '素材URL',
  `thumbnail` text COLLATE utf8mb4_unicode_ci COMMENT '缩略图URL（视频素材使用）',
  `type` enum('image','video','document','audio','other') COLLATE utf8mb4_unicode_ci DEFAULT 'image' COMMENT '素材类型',
  `file_size` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件大小',
  `duration` int(11) DEFAULT NULL COMMENT '时长（秒，视频/音频使用）',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '素材描述',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_drama_id` (`drama_id`),
  CONSTRAINT `fk_materials_drama` FOREIGN KEY (`drama_id`) REFERENCES `drama_series` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短剧项目素材';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `drama_production_schedule`
--

DROP TABLE IF EXISTS `drama_production_schedule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `drama_production_schedule` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '鍒朵綔杩涘害ID',
  `drama_id` int(11) NOT NULL COMMENT '鍏宠仈鐨勭煭鍓?D',
  `schedule_pre_production` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '鍓嶆湡鍒朵綔绛瑰?鏃堕棿瀹夋帓锛屽寘鍚?捣姝㈡椂闂',
  `schedule_filming` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '鎷嶆憚鏃堕棿瀹夋帓锛屽寘鍚?捣姝㈡椂闂',
  `schedule_post_production` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '鍚庢湡鍒朵綔鏃堕棿瀹夋帓锛屽寘鍚?捣姝㈡椂闂',
  `schedule_distribution` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '鍙戣?鏃堕棿瀹夋帓锛屽寘鍚?捣姝㈡椂闂',
  `current_phase` enum('pre_production','filming','post_production','distribution','completed') COLLATE utf8mb4_unicode_ci DEFAULT 'pre_production' COMMENT '褰撳墠闃舵?',
  `overall_progress` decimal(5,2) DEFAULT '0.00' COMMENT '鎬讳綋杩涘害锛堢櫨鍒嗘瘮锛',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '鍒涘缓鏃堕棿',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '鏇存柊鏃堕棿',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_drama_production_schedule` (`drama_id`),
  KEY `idx_drama_production_schedule_drama_id` (`drama_id`),
  KEY `idx_drama_production_schedule_phase` (`current_phase`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='鐭?墽鍒朵綔杩涘害琛';
/*!40101 SET character_set_client = @saved_cs_client */;
