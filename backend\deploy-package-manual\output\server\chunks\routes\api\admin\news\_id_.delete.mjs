import { c as defineEvent<PERSON>and<PERSON>, j as getRouter<PERSON>aram, f as createError, q as query, m as transaction, o as logAdminAction, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__delete = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const newsId = getRouterParam(event, "id");
    if (!newsId || isNaN(parseInt(newsId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u65B0\u95FBID"
      });
    }
    const newsIdNum = parseInt(newsId);
    const existingNews = await query(
      "SELECT id, title, status, cover_image_url FROM news WHERE id = ?",
      [newsIdNum]
    );
    if (!existingNews || existingNews.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u65B0\u95FB\u4E0D\u5B58\u5728"
      });
    }
    const news = existingNews[0];
    await transaction(async (connection) => {
      await connection.execute(
        "DELETE FROM news_tag_relations WHERE news_id = ?",
        [newsIdNum]
      );
      await connection.execute(
        "DELETE FROM news_read_logs WHERE news_id = ?",
        [newsIdNum]
      );
      await connection.execute(
        "DELETE FROM news_seo WHERE news_id = ?",
        [newsIdNum]
      );
      await connection.execute(
        "DELETE FROM news WHERE id = ?",
        [newsIdNum]
      );
    });
    await logAdminAction(admin.id, "news:delete", "\u5220\u9664\u65B0\u95FB", {
      newsId: newsIdNum,
      title: news.title,
      status: news.status
    });
    return {
      success: true,
      message: "\u65B0\u95FB\u5220\u9664\u6210\u529F",
      data: {
        id: newsIdNum,
        title: news.title
      }
    };
  } catch (error) {
    logger.error("\u5220\u9664\u65B0\u95FB\u5931\u8D25", {
      error: error.message,
      stack: error.stack,
      newsId: getRouterParam(event, "id"),
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u5220\u9664\u65B0\u95FB\u5931\u8D25"
    });
  }
});

export { _id__delete as default };
//# sourceMappingURL=_id_.delete.mjs.map
