import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, j as getRouter<PERSON>aram, q as query, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__get = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const fundId = getRouterParam(event, "id");
    if (!fundId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u57FA\u91D1ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const funds = await query(
      `SELECT id, code, title, description, type, risk, min_investment, period,
              target_size, raised_amount, expected_return, min_holding_period,
              risk_description, establish_date, exit_date, manager, trustee,
              redemption_policy, investment_strategy, is_published,
              created_at, updated_at
       FROM funds 
       WHERE id = ?`,
      [fundId]
    );
    if (funds.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u57FA\u91D1\u4E0D\u5B58\u5728"
      });
    }
    const fund = funds[0];
    const [highlights, documents, faqs, timelines, fees, performances, usagePlans, usagePlanDescriptions, successCases] = await Promise.all([
      // 1. 基金亮点表 (fund_highlights)
      // 字段: id, fund_id, content, sort_order, created_at
      query("SELECT id, fund_id, content, sort_order, created_at FROM fund_highlights WHERE fund_id = ? ORDER BY sort_order, id", [fund.id]),
      // 2. 基金文档表 (fund_documents)
      // 字段: id, fund_id, name, file_url, file_type, file_size, created_at
      query("SELECT id, fund_id, name, file_url, file_type, file_size, created_at FROM fund_documents WHERE fund_id = ? ORDER BY id", [fund.id]),
      // 3. 常见问题表 (fund_faqs)
      // 字段: id, fund_id, question, answer, sort_order, created_at
      query("SELECT id, fund_id, question, answer, sort_order, created_at FROM fund_faqs WHERE fund_id = ? ORDER BY sort_order, id", [fund.id]),
      // 4. 时间线表 (fund_timelines)
      // 字段: id, fund_id, stage, date, status, sort_order, created_at
      query("SELECT id, fund_id, stage, date, status, sort_order, created_at FROM fund_timelines WHERE fund_id = ? ORDER BY sort_order, id", [fund.id]),
      // 5. 费用结构表 (fund_fees)
      // 字段: id, fund_id, name, value, sort_order, created_at
      query("SELECT id, fund_id, name, value, sort_order, created_at FROM fund_fees WHERE fund_id = ? ORDER BY sort_order, id", [fund.id]),
      // 6. 历史业绩表 (fund_performances)
      // 字段: id, fund_id, year, return, average, created_at
      query("SELECT id, fund_id, year, `return`, average, created_at FROM fund_performances WHERE fund_id = ? ORDER BY year DESC", [fund.id]),
      // 7. 资金使用计划表 (fund_usage_plans)
      // 字段: id, fund_id, purpose, amount, percentage, description, sort_order, created_at, updated_at
      query("SELECT id, fund_id, purpose, amount, percentage, description, sort_order, created_at, updated_at FROM fund_usage_plans WHERE fund_id = ? ORDER BY sort_order, id", [fund.id]),
      // 8. 资金使用计划描述表 (fund_usage_plan_descriptions)
      // 字段: id, fund_id, description, created_at, updated_at
      query("SELECT id, fund_id, description, created_at, updated_at FROM fund_usage_plan_descriptions WHERE fund_id = ? ORDER BY id", [fund.id]),
      // 9. 成功案例表 (fund_success_cases)
      // 字段: id, fund_id, title, description, return_rate, investment_amount, recovery_period, sort_order, created_at, updated_at
      query("SELECT id, fund_id, title, description, return_rate, investment_amount, recovery_period, sort_order, created_at, updated_at FROM fund_success_cases WHERE fund_id = ? ORDER BY sort_order, id", [fund.id])
    ]);
    const formattedFund = {
      // 基金主表数据 (funds)
      id: fund.id,
      code: fund.code,
      title: fund.title,
      description: fund.description,
      type: fund.type,
      risk: fund.risk,
      minInvestment: fund.min_investment,
      period: fund.period,
      targetSize: fund.target_size,
      raisedAmount: fund.raised_amount,
      expectedReturn: fund.expected_return,
      minHoldingPeriod: fund.min_holding_period,
      riskDescription: fund.risk_description,
      establishDate: fund.establish_date,
      exitDate: fund.exit_date,
      manager: fund.manager,
      trustee: fund.trustee,
      redemptionPolicy: fund.redemption_policy,
      investmentStrategy: fund.investment_strategy,
      isPublished: fund.is_published,
      createdAt: fund.created_at,
      updatedAt: fund.updated_at,
      // 关联数据 - 严格按照数据库字段映射
      // 1. 基金亮点 (fund_highlights)
      highlights: highlights.map((h) => ({
        id: h.id,
        fundId: h.fund_id,
        content: h.content,
        sortOrder: h.sort_order,
        createdAt: h.created_at
      })),
      // 2. 基金文档 (fund_documents)
      documents: documents.map((d) => ({
        id: d.id,
        fundId: d.fund_id,
        name: d.name,
        fileUrl: d.file_url,
        fileType: d.file_type,
        fileSize: d.file_size,
        createdAt: d.created_at
      })),
      // 3. 常见问题 (fund_faqs)
      faqs: faqs.map((f) => ({
        id: f.id,
        fundId: f.fund_id,
        question: f.question,
        answer: f.answer,
        sortOrder: f.sort_order,
        createdAt: f.created_at
      })),
      // 4. 时间线 (fund_timelines)
      timelines: timelines.map((t) => ({
        id: t.id,
        fundId: t.fund_id,
        stage: t.stage,
        date: t.date,
        status: t.status,
        sortOrder: t.sort_order,
        createdAt: t.created_at
      })),
      // 5. 费用结构 (fund_fees)
      fees: fees.map((f) => ({
        id: f.id,
        fundId: f.fund_id,
        name: f.name,
        value: f.value,
        sortOrder: f.sort_order,
        createdAt: f.created_at
      })),
      // 6. 历史业绩 (fund_performances)
      performances: performances.map((p) => ({
        id: p.id,
        fundId: p.fund_id,
        year: p.year,
        return: p.return,
        average: p.average,
        createdAt: p.created_at
      })),
      // 7. 资金使用计划 (fund_usage_plans)
      usagePlans: usagePlans.map((u) => ({
        id: u.id,
        fundId: u.fund_id,
        purpose: u.purpose || u.description,
        // 优先使用purpose字段，fallback到description
        amount: u.amount,
        percentage: u.percentage,
        description: u.description,
        sortOrder: u.sort_order,
        createdAt: u.created_at,
        updatedAt: u.updated_at
      })),
      // 8. 资金使用计划描述 (fund_usage_plan_descriptions)
      usagePlanDescriptions: usagePlanDescriptions.map((d) => ({
        id: d.id,
        fundId: d.fund_id,
        description: d.description,
        createdAt: d.created_at,
        updatedAt: d.updated_at
      })),
      // 9. 成功案例 (fund_success_cases)
      successCases: successCases.map((s) => ({
        id: s.id,
        fundId: s.fund_id,
        title: s.title,
        description: s.description,
        return: s.return_rate,
        // 映射为前端期望的字段名
        investment: s.investment_amount,
        // 映射为前端期望的字段名
        recoveryPeriod: s.recovery_period,
        returnRate: s.return_rate,
        // 保留原字段名用于兼容
        investmentAmount: s.investment_amount,
        // 保留原字段名用于兼容
        sortOrder: s.sort_order,
        createdAt: s.created_at,
        updatedAt: s.updated_at
      }))
    };
    return {
      success: true,
      data: formattedFund
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u57FA\u91D1\u8BE6\u60C5\u5931\u8D25", {
      error: error.message,
      fundId: getRouterParam(event, "id"),
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { _id__get as default };
//# sourceMappingURL=_id_.get.mjs.map
