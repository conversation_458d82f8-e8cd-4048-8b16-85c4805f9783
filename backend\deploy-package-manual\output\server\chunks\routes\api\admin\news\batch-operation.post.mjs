import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, r as readBody, f as createError, q as query, m as transaction, o as logAdminAction, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const batchOperation_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const body = await readBody(event);
    const { ids, action } = body;
    if (!Array.isArray(ids) || ids.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u8BF7\u9009\u62E9\u8981\u64CD\u4F5C\u7684\u65B0\u95FB"
      });
    }
    if (!action || typeof action !== "string") {
      throw createError({
        statusCode: 400,
        statusMessage: "\u8BF7\u6307\u5B9A\u64CD\u4F5C\u7C7B\u578B"
      });
    }
    const validActions = ["delete", "publish", "unpublish", "archive", "feature", "unfeature"];
    if (!validActions.includes(action)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u64CD\u4F5C\u7C7B\u578B"
      });
    }
    const newsIds = ids.filter((id) => !isNaN(parseInt(id))).map((id) => parseInt(id));
    if (newsIds.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u65B0\u95FBID"
      });
    }
    const existingNews = await query(
      `SELECT id, title FROM news WHERE id IN (${newsIds.map(() => "?").join(",")})`,
      newsIds
    );
    if (existingNews.length !== newsIds.length) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u90E8\u5206\u65B0\u95FB\u4E0D\u5B58\u5728"
      });
    }
    let updateQuery = "";
    let updateParams = [];
    let actionDescription = "";
    switch (action) {
      case "delete":
        await transaction(async (connection) => {
          await connection.execute(
            `DELETE FROM news_tag_relations WHERE news_id IN (${newsIds.map(() => "?").join(",")})`,
            newsIds
          );
          await connection.execute(
            `DELETE FROM news_read_logs WHERE news_id IN (${newsIds.map(() => "?").join(",")})`,
            newsIds
          );
          await connection.execute(
            `DELETE FROM news_seo WHERE news_id IN (${newsIds.map(() => "?").join(",")})`,
            newsIds
          );
          await connection.execute(
            `DELETE FROM news WHERE id IN (${newsIds.map(() => "?").join(",")})`,
            newsIds
          );
        });
        actionDescription = "\u6279\u91CF\u5220\u9664\u65B0\u95FB";
        break;
      case "publish":
        updateQuery = `
          UPDATE news 
          SET status = 'published', 
              publish_date = COALESCE(publish_date, NOW()),
              updated_at = NOW()
          WHERE id IN (${newsIds.map(() => "?").join(",")})
        `;
        updateParams = newsIds;
        actionDescription = "\u6279\u91CF\u53D1\u5E03\u65B0\u95FB";
        break;
      case "unpublish":
        updateQuery = `
          UPDATE news 
          SET status = 'draft', 
              publish_date = NULL,
              updated_at = NOW()
          WHERE id IN (${newsIds.map(() => "?").join(",")})
        `;
        updateParams = newsIds;
        actionDescription = "\u6279\u91CF\u4E0B\u7EBF\u65B0\u95FB";
        break;
      case "archive":
        updateQuery = `
          UPDATE news 
          SET status = 'archived',
              updated_at = NOW()
          WHERE id IN (${newsIds.map(() => "?").join(",")})
        `;
        updateParams = newsIds;
        actionDescription = "\u6279\u91CF\u5F52\u6863\u65B0\u95FB";
        break;
      case "feature":
        updateQuery = `
          UPDATE news 
          SET is_featured = 1,
              updated_at = NOW()
          WHERE id IN (${newsIds.map(() => "?").join(",")})
        `;
        updateParams = newsIds;
        actionDescription = "\u6279\u91CF\u8BBE\u4E3A\u63A8\u8350";
        break;
      case "unfeature":
        updateQuery = `
          UPDATE news 
          SET is_featured = 0,
              updated_at = NOW()
          WHERE id IN (${newsIds.map(() => "?").join(",")})
        `;
        updateParams = newsIds;
        actionDescription = "\u6279\u91CF\u53D6\u6D88\u63A8\u8350";
        break;
    }
    if (updateQuery) {
      await query(updateQuery, updateParams);
    }
    await logAdminAction(admin.id, "news:batch_operation", actionDescription, {
      action,
      newsIds,
      newsCount: newsIds.length,
      newsTitles: existingNews.map((news) => news.title)
    });
    return {
      success: true,
      message: `${actionDescription}\u6210\u529F`,
      data: {
        action,
        affectedCount: newsIds.length,
        newsIds
      }
    };
  } catch (error) {
    logger.error("\u6279\u91CF\u64CD\u4F5C\u65B0\u95FB\u5931\u8D25", {
      error: error.message,
      stack: error.stack,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u6279\u91CF\u64CD\u4F5C\u5931\u8D25"
    });
  }
});

export { batchOperation_post as default };
//# sourceMappingURL=batch-operation.post.mjs.map
