import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, Z as verifyUserAccessToken, f as createError, r as readBody, U as findUserById, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const bankCards_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const userPayload = verifyUserAccessToken(event);
    if (!userPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u7528\u6237\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"
      });
    }
    const body = await readBody(event);
    const { bankName, cardNumber, cardHolder, idCard, phone } = body;
    if (!bankName || !cardNumber || !cardHolder || !idCard || !phone) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u8BF7\u586B\u5199\u5B8C\u6574\u7684\u94F6\u884C\u5361\u4FE1\u606F"
      });
    }
    if (!/^\d{16,19}$/.test(cardNumber)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u94F6\u884C\u5361\u53F7\u683C\u5F0F\u4E0D\u6B63\u786E"
      });
    }
    if (!/^\d{17}[\dXx]$/.test(idCard)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u8EAB\u4EFD\u8BC1\u53F7\u683C\u5F0F\u4E0D\u6B63\u786E"
      });
    }
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u624B\u673A\u53F7\u683C\u5F0F\u4E0D\u6B63\u786E"
      });
    }
    const user = await findUserById(userPayload.id);
    if (!user) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u7528\u6237\u4E0D\u5B58\u5728"
      });
    }
    if (user.status !== 1) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u7528\u6237\u8D26\u6237\u5DF2\u88AB\u7981\u7528"
      });
    }
    const newCardId = `card_${Date.now()}`;
    logger.info("\u7528\u6237\u6DFB\u52A0\u94F6\u884C\u5361", {
      userId: user.id,
      cardId: newCardId,
      bankName,
      cardNumber: cardNumber.replace(/(\d{4})\d{8,11}(\d{4})/, "$1****$2"),
      // 脱敏显示
      cardHolder,
      ip: getClientIP(event)
    });
    return {
      success: true,
      data: {
        id: newCardId,
        bankName,
        cardNumber,
        cardHolder,
        isDefault: false
      }
    };
  } catch (error) {
    logger.error("\u6DFB\u52A0\u94F6\u884C\u5361\u5931\u8D25", {
      error: error.message,
      userId: (_a = event.context.user) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { bankCards_post as default };
//# sourceMappingURL=bank-cards.post.mjs.map
