import { c as defineEvent<PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, q as query, r as readBody, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _caseId__put = defineEventHandler(async (event) => {
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const fundId = getRouterParam(event, "id");
    const caseId = getRouterParam(event, "caseId");
    if (!fundId || isNaN(Number(fundId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u57FA\u91D1ID"
      });
    }
    if (!caseId || isNaN(Number(caseId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u6848\u4F8BID"
      });
    }
    const existingCase = await query(
      "SELECT * FROM fund_success_cases WHERE id = ? AND fund_id = ?",
      [caseId, fundId]
    );
    if (existingCase.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u6210\u529F\u6848\u4F8B\u4E0D\u5B58\u5728"
      });
    }
    const body = await readBody(event);
    const { title, description, return: returnValue, investment, recoveryPeriod, sortOrder } = body;
    if (!title || !title.trim() || !description || !description.trim()) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6807\u9898\u548C\u63CF\u8FF0\u4E3A\u5FC5\u586B\u9879"
      });
    }
    await query(
      "UPDATE fund_success_cases SET title = ?, description = ?, return_rate = ?, investment_amount = ?, recovery_period = ?, sort_order = ? WHERE id = ? AND fund_id = ?",
      [title.trim(), description.trim(), returnValue || null, investment || null, recoveryPeriod || null, Number(sortOrder) || 1, caseId, fundId]
    );
    await logAuditAction({
      action: "ADMIN_UPDATE_FUND_SUCCESS_CASE",
      description: `\u7BA1\u7406\u5458\u66F4\u65B0\u57FA\u91D1\u6210\u529F\u6848\u4F8B: \u57FA\u91D1ID=${fundId}, \u6848\u4F8BID=${caseId}`,
      userId: adminPayload.id,
      username: adminPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      metadata: {
        fundId: Number(fundId),
        caseId: Number(caseId),
        oldTitle: existingCase[0].title,
        newTitle: title.trim(),
        oldDescription: existingCase[0].description,
        newDescription: description.trim(),
        oldReturn: existingCase[0].return,
        newReturn: returnValue || null,
        oldInvestment: existingCase[0].investment,
        newInvestment: investment || null,
        sortOrder: Number(sortOrder) || 1
      }
    });
    logger.info("\u57FA\u91D1\u6210\u529F\u6848\u4F8B\u66F4\u65B0\u6210\u529F", {
      adminId: adminPayload.id,
      adminUsername: adminPayload.username,
      fundId: Number(fundId),
      caseId: Number(caseId),
      title: title.trim()
    });
    return {
      success: true,
      message: "\u57FA\u91D1\u6210\u529F\u6848\u4F8B\u66F4\u65B0\u6210\u529F",
      data: {
        id: Number(caseId),
        fundId: Number(fundId),
        title: title.trim(),
        description: description.trim(),
        return: returnValue || null,
        investment: investment || null,
        sortOrder: Number(sortOrder) || 1,
        updatedAt: (/* @__PURE__ */ new Date()).toISOString()
      }
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u57FA\u91D1\u6210\u529F\u6848\u4F8B\u5931\u8D25", {
      error: error.message,
      fundId: getRouterParam(event, "id"),
      caseId: getRouterParam(event, "caseId")
    });
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "\u66F4\u65B0\u57FA\u91D1\u6210\u529F\u6848\u4F8B\u5931\u8D25"
    });
  }
});

export { _caseId__put as default };
//# sourceMappingURL=_caseId_.put.mjs.map
