import { c as defineEvent<PERSON>and<PERSON>, q as query, f as createError } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const selector_get = defineEventHandler(async (event) => {
  try {
    const tags = await query(`
      SELECT id, name, font_color as fontColor, background_color as backgroundColor
      FROM drama_tags
      ORDER BY name ASC
    `);
    return {
      success: true,
      message: "\u83B7\u53D6\u6807\u7B7E\u9009\u62E9\u5668\u6570\u636E\u6210\u529F",
      data: {
        result: tags
      }
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u6807\u7B7E\u9009\u62E9\u5668\u6570\u636E\u5931\u8D25:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "\u83B7\u53D6\u6807\u7B7E\u9009\u62E9\u5668\u6570\u636E\u5931\u8D25"
    });
  }
});

export { selector_get as default };
//# sourceMappingURL=selector.get.mjs.map
