import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, f as createError, r as readBody, q as query } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const additionalInfo_put = defineEventHandler(async (event) => {
  try {
    const dramaId = getRouterParam(event, "id");
    if (!dramaId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u77ED\u5267ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const id = parseInt(dramaId);
    if (isNaN(id) || id <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u77ED\u5267ID"
      });
    }
    const body = await readBody(event);
    const { additionalInfo } = body;
    console.log("\u63A5\u6536\u5230\u7684\u5176\u4ED6\u4FE1\u606F\u6570\u636E:", {
      body,
      additionalInfo
    });
    if (!additionalInfo) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5176\u4ED6\u4FE1\u606F\u6570\u636E\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const dramaExists = await query(
      "SELECT id FROM drama_series WHERE id = ?",
      [id]
    );
    if (dramaExists.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u77ED\u5267\u4E0D\u5B58\u5728"
      });
    }
    const existingInfo = await query(
      "SELECT id FROM drama_additional_info WHERE drama_id = ?",
      [id]
    );
    if (existingInfo.length > 0) {
      await query(`
        UPDATE drama_additional_info 
        SET 
          risk_management = ?,
          confirmed_resources = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE drama_id = ?
      `, [
        additionalInfo.riskManagement ? JSON.stringify(additionalInfo.riskManagement) : null,
        additionalInfo.confirmedResources ? JSON.stringify(additionalInfo.confirmedResources) : null,
        id
      ]);
    } else {
      await query(`
        INSERT INTO drama_additional_info (
          drama_id,
          risk_management,
          confirmed_resources
        ) VALUES (?, ?, ?)
      `, [
        id,
        additionalInfo.riskManagement ? JSON.stringify(additionalInfo.riskManagement) : null,
        additionalInfo.confirmedResources ? JSON.stringify(additionalInfo.confirmedResources) : null
      ]);
    }
    return {
      success: true,
      message: "\u5176\u4ED6\u4FE1\u606F\u66F4\u65B0\u6210\u529F",
      data: {
        dramaId: id,
        additionalInfo: {
          riskManagement: additionalInfo.riskManagement,
          confirmedResources: additionalInfo.confirmedResources
        }
      }
    };
  } catch (error) {
    console.error("\u66F4\u65B0\u5176\u4ED6\u4FE1\u606F\u5931\u8D25:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { additionalInfo_put as default };
//# sourceMappingURL=additional-info.put.mjs.map
