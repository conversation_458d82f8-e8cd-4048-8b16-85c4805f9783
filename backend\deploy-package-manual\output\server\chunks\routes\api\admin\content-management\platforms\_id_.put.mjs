import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, r as readBody, f as createError, q as query } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__put = defineEventHandler(async (event) => {
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const platformId = getRouterParam(event, "id");
    const body = await readBody(event);
    const {
      platform_name,
      platform_logo_url,
      company_id,
      platform_domain,
      mini_program_name,
      mini_program_link,
      mini_program_qrcode_url,
      platform_type,
      description,
      is_active
    } = body;
    if (!platformId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5E73\u53F0ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    if (!platform_name) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5E73\u53F0\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    if (!platform_logo_url) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5E73\u53F0Logo\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const existingPlatform = await query(
      "SELECT id, platform_name FROM drama_platforms WHERE id = ?",
      [platformId]
    );
    if (existingPlatform.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u5E73\u53F0\u4E0D\u5B58\u5728"
      });
    }
    const currentPlatform = existingPlatform[0];
    const duplicatePlatform = await query(
      "SELECT id FROM drama_platforms WHERE platform_name = ? AND id != ?",
      [platform_name, platformId]
    );
    if (duplicatePlatform.length > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5E73\u53F0\u540D\u79F0\u5DF2\u5B58\u5728"
      });
    }
    await query(
      `UPDATE drama_platforms
       SET platform_name = ?,
           platform_logo_url = ?,
           company_id = ?,
           platform_domain = ?,
           mini_program_name = ?,
           mini_program_link = ?,
           mini_program_qrcode_url = ?,
           platform_type = ?,
           description = ?,
           is_active = ?,
           updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`,
      [
        platform_name,
        platform_logo_url,
        company_id || null,
        platform_domain || null,
        mini_program_name || null,
        mini_program_link || null,
        mini_program_qrcode_url || null,
        platform_type || "content",
        description || null,
        is_active,
        platformId
      ]
    );
    return {
      success: true,
      message: "\u5E73\u53F0\u66F4\u65B0\u6210\u529F",
      data: {
        id: platformId,
        platform_name,
        platform_logo_url,
        company_id,
        platform_domain,
        mini_program_name,
        mini_program_link,
        mini_program_qrcode_url,
        platform_type,
        description,
        is_active
      }
    };
  } catch (error) {
    console.error("\u66F4\u65B0\u5E73\u53F0\u5931\u8D25:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u66F4\u65B0\u5E73\u53F0\u5931\u8D25: " + error.message
    });
  }
});

export { _id__put as default };
//# sourceMappingURL=_id_.put.mjs.map
