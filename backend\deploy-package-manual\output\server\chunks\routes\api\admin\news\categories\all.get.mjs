import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, q as query, l as logger, e as getClientIP, f as createError } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const all_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const categoriesQuery = `
      SELECT 
        id,
        name,
        slug,
        description,
        parent_id,
        sort_order
      FROM news_categories
      WHERE is_active = 1
      ORDER BY sort_order ASC, id ASC
    `;
    const categories = await query(categoriesQuery);
    const formattedCategories = categories.map((category) => ({
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      parentId: category.parent_id,
      sortOrder: category.sort_order
    }));
    return {
      success: true,
      data: formattedCategories
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u6240\u6709\u65B0\u95FB\u5206\u7C7B\u5931\u8D25", {
      error: error.message,
      stack: error.stack,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u83B7\u53D6\u5206\u7C7B\u5217\u8868\u5931\u8D25"
    });
  }
});

export { all_get as default };
//# sourceMappingURL=all.get.mjs.map
