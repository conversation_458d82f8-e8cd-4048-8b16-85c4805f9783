import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, q as query, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const investment_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u8BBF\u95EE\u6295\u8D44\u8BBE\u7F6E"
      });
    }
    const result = await query(
      "SELECT setting_value FROM system_settings WHERE setting_key = ?",
      ["investment_settings"]
    );
    let investmentSettings = {};
    if (result.length > 0 && result[0].setting_value) {
      try {
        investmentSettings = JSON.parse(result[0].setting_value);
      } catch (error) {
        logger.error("\u89E3\u6790\u6295\u8D44\u8BBE\u7F6EJSON\u5931\u8D25", { error: error.message });
      }
    }
    if (Object.keys(investmentSettings).length === 0) {
      investmentSettings = {
        minAmount: 1e4,
        maxAmount: 1e6,
        minReturnRate: 8,
        platformFee: 2
      };
    }
    return {
      success: true,
      data: investmentSettings
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u6295\u8D44\u8BBE\u7F6E\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { investment_get as default };
//# sourceMappingURL=investment.get.mjs.map
