# 个人中心页面测试指南

## 测试前准备

### 1. 启动开发服务器
```bash
cd website
pnpm dev
```

### 2. 启动后端服务器
```bash
cd backend
pnpm dev
```

### 3. 访问个人中心
- 首先需要登录系统
- 登录成功后访问：`http://localhost:3000/dashboard`

## 功能测试清单

### 1. 页面布局测试
- [ ] 左右分栏布局正确显示
- [ ] 左侧个人信息卡片显示完整
- [ ] 右侧数据统计区域显示正确
- [ ] 响应式设计在不同屏幕尺寸下正常工作

### 2. 个人信息卡片测试
- [ ] 用户头像正确显示
- [ ] 用户姓名和等级正确显示
- [ ] 等级徽章颜色和文字正确
- [ ] 最后登录时间显示

### 3. 数据卡片测试
- [ ] 总投资贝壳数量显示
- [ ] 已消耗贝壳数量显示
- [ ] 累计收益钻石数量显示
- [ ] 收益率计算和显示正确
- [ ] 数字格式化（千分位分隔符）正确

### 4. 充值功能测试
- [ ] 点击"充值"按钮打开充值对话框
- [ ] 充值金额输入验证
- [ ] 支付方式选择功能
- [ ] 支付流程模拟
- [ ] 充值成功后数据更新

### 5. 提现功能测试
- [ ] 点击"提现"按钮打开提现对话框
- [ ] 可用余额显示正确
- [ ] 提现金额验证（不能超过余额）
- [ ] 银行卡选择功能
- [ ] 添加银行卡功能
- [ ] 交易密码验证
- [ ] 提现申请提交

### 6. 图表功能测试
- [ ] 收益趋势图表正确渲染
- [ ] 双Y轴显示（贝壳和钻石）
- [ ] 投资分布饼图正确显示
- [ ] 图表交互功能（鼠标悬停）
- [ ] 窗口大小变化时图表自适应

### 7. 项目进度测试
- [ ] 项目列表正确显示
- [ ] 项目进度条图表渲染
- [ ] 项目状态颜色标识
- [ ] 项目数据格式化
- [ ] 空状态处理

### 8. 通知系统测试
- [ ] 通知列表显示
- [ ] 未读通知数量标识
- [ ] 通知分类图标和颜色
- [ ] 标记已读功能
- [ ] 全部标记已读功能
- [ ] 空状态处理

### 9. 加载状态测试
- [ ] 页面初始加载动画
- [ ] API请求加载状态
- [ ] 错误状态处理
- [ ] 网络异常处理

### 10. 交互功能测试
- [ ] 按钮点击响应
- [ ] 表单输入验证
- [ ] 对话框打开关闭
- [ ] 路由跳转功能
- [ ] 退出登录功能

## 数据验证测试

### 1. 模拟数据验证
- 总投资贝壳：1,500,000
- 已消耗贝壳：1,200,000
- 累计收益钻石：180,000
- 收益率：15.00%
- 投资项目数：3

### 2. 计算验证
- 收益率 = 180,000 ÷ 1,200,000 × 100% = 15.00% ✓

### 3. 图表数据验证
- 收益趋势：6个月数据点
- 投资分布：3个项目占比
- 项目进度：3个项目进度条

## 错误场景测试

### 1. 网络错误
- [ ] 断网情况下的错误处理
- [ ] API请求超时处理
- [ ] 服务器错误响应处理

### 2. 数据异常
- [ ] 空数据处理
- [ ] 异常数据格式处理
- [ ] 除零错误处理

### 3. 用户操作错误
- [ ] 无效输入验证
- [ ] 权限不足处理
- [ ] 重复操作防护

## 性能测试

### 1. 加载性能
- [ ] 页面首次加载时间 < 3秒
- [ ] 图表渲染时间 < 1秒
- [ ] API响应时间 < 2秒

### 2. 内存使用
- [ ] 页面内存占用合理
- [ ] 图表组件内存释放
- [ ] 无内存泄漏

### 3. 响应性能
- [ ] 按钮点击响应 < 100ms
- [ ] 表单输入响应流畅
- [ ] 滚动性能良好

## 兼容性测试

### 1. 浏览器兼容性
- [ ] Chrome 最新版本
- [ ] Firefox 最新版本
- [ ] Safari 最新版本
- [ ] Edge 最新版本

### 2. 设备兼容性
- [ ] 桌面端（1920x1080）
- [ ] 平板端（768x1024）
- [ ] 手机端（375x667）

### 3. 操作系统兼容性
- [ ] Windows
- [ ] macOS
- [ ] iOS
- [ ] Android

## 测试结果记录

### 通过的测试项
- 记录通过的功能测试项

### 发现的问题
- 记录发现的bug和问题
- 问题的重现步骤
- 问题的严重程度

### 改进建议
- 用户体验改进建议
- 性能优化建议
- 功能增强建议

## 测试完成标准

- [ ] 所有核心功能正常工作
- [ ] 无阻塞性bug
- [ ] 性能指标达标
- [ ] 用户体验良好
- [ ] 兼容性测试通过

## 注意事项

1. **测试环境**：确保在干净的测试环境中进行测试
2. **数据备份**：测试前备份重要数据
3. **版本记录**：记录测试的代码版本
4. **问题反馈**：及时反馈发现的问题
5. **文档更新**：根据测试结果更新相关文档
