import { c as defineEvent<PERSON><PERSON><PERSON>, n as readMultipartFormData, f as createError, l as logger, h as logAuditAction, i as getHeader, e as getClientIP } from '../../../../_/nitro.mjs';
import { writeFile } from 'fs/promises';
import { u as uploadToCOS } from '../../../../_/cos-uploader.mjs';
import { g as generateUniqueFilename, a as getUploadPath, d as deleteFile } from '../../../../_/file-utils.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';
import 'cos-nodejs-sdk-v5';
import 'path';
import 'fs';
import 'crypto';

const platformImage_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const formData = await readMultipartFormData(event);
    if (!formData || formData.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u672A\u9009\u62E9\u6587\u4EF6"
      });
    }
    const fileData = formData.find((item) => item.name === "file");
    if (!fileData || !fileData.data || !fileData.filename) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6587\u4EF6\u6570\u636E\u65E0\u6548"
      });
    }
    const allowedTypes = [".jpg", ".jpeg", ".png", ".gif", ".webp"];
    const fileExt = (_a = fileData.filename.split(".").pop()) == null ? void 0 : _a.toLowerCase();
    if (!fileExt || !allowedTypes.includes(`.${fileExt}`)) {
      throw createError({
        statusCode: 400,
        statusMessage: `\u5E73\u53F0\u56FE\u7247\u53EA\u652F\u6301\u56FE\u7247\u683C\u5F0F: ${allowedTypes.join(", ")}`
      });
    }
    const maxSize = 2 * 1024 * 1024;
    if (fileData.data.length > maxSize) {
      throw createError({
        statusCode: 400,
        statusMessage: `\u5E73\u53F0\u56FE\u7247\u5927\u5C0F\u8D85\u8FC7\u9650\u5236\uFF0C\u6700\u5927\u5141\u8BB8 2MB`
      });
    }
    const uniqueFilename = generateUniqueFilename(fileData.filename);
    const tempPath = await getUploadPath(uniqueFilename, "temp");
    await writeFile(tempPath, fileData.data);
    logger.info("\u5F00\u59CB\u4E0A\u4F20\u5E73\u53F0\u56FE\u7247", {
      originalName: fileData.filename,
      fileName: uniqueFilename,
      fileSize: fileData.data.length,
      adminId: admin.id
    });
    try {
      const destPath = `mengtutv/platforms/${uniqueFilename}`;
      const uploadResult = await uploadToCOS({
        path: tempPath,
        originalname: fileData.filename,
        mimetype: fileData.type || "image/jpeg"
      }, destPath);
      await deleteFile(tempPath);
      await logAuditAction({
        action: "ADMIN_UPLOAD_PLATFORM_IMAGE",
        description: `\u7BA1\u7406\u5458\u4E0A\u4F20\u5E73\u53F0\u56FE\u7247: ${fileData.filename}`,
        userId: admin.id,
        username: admin.username,
        ip: getClientIP(event),
        userAgent: getHeader(event, "user-agent") || "",
        details: {
          originalName: fileData.filename,
          fileName: uniqueFilename,
          fileSize: fileData.data.length,
          url: uploadResult.url
        }
      });
      return {
        success: true,
        message: "\u5E73\u53F0\u56FE\u7247\u4E0A\u4F20\u6210\u529F",
        data: {
          url: uploadResult.url,
          fileName: uniqueFilename,
          originalName: fileData.filename,
          size: fileData.data.length,
          type: fileData.type
        }
      };
    } catch (uploadError) {
      await deleteFile(tempPath).catch(() => {
      });
      logger.error("\u5E73\u53F0\u56FE\u7247\u4E0A\u4F20\u5931\u8D25", {
        originalName: fileData.filename,
        error: uploadError.message,
        adminId: admin.id
      });
      throw createError({
        statusCode: 500,
        statusMessage: "\u56FE\u7247\u4E0A\u4F20\u5931\u8D25: " + uploadError.message
      });
    }
  } catch (error) {
    console.error("\u5E73\u53F0\u56FE\u7247\u4E0A\u4F20\u5931\u8D25:", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u56FE\u7247\u4E0A\u4F20\u5931\u8D25: " + error.message
    });
  }
});

export { platformImage_post as default };
//# sourceMappingURL=platform-image.post.mjs.map
