import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, f as createError, q as query, k as getPool, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__delete = defineEventHandler(async (event) => {
  try {
    const dramaId = getRouterParam(event, "id");
    if (!dramaId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u77ED\u5267ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const id = parseInt(dramaId);
    if (isNaN(id) || id <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u77ED\u5267ID"
      });
    }
    const existingDrama = await query(`
      SELECT id, title FROM drama_series WHERE id = ?
    `, [id]);
    if (existingDrama.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u77ED\u5267\u4E0D\u5B58\u5728"
      });
    }
    const drama = existingDrama[0];
    const connection = await getPool().getConnection();
    await connection.beginTransaction();
    try {
      await connection.execute("DELETE FROM drama_materials WHERE drama_id = ?", [id]);
      await connection.execute("DELETE FROM drama_investment_tiers WHERE drama_id = ?", [id]);
      await connection.execute("DELETE FROM drama_additional_info WHERE drama_id = ?", [id]);
      await connection.execute("DELETE FROM drama_funding_info WHERE drama_id = ?", [id]);
      await connection.execute("DELETE FROM drama_production_schedule WHERE drama_id = ?", [id]);
      await connection.execute("DELETE FROM drama_production_team WHERE drama_id = ?", [id]);
      await connection.execute("DELETE FROM drama_series WHERE id = ?", [id]);
      await connection.commit();
      logger.info("\u5220\u9664\u77ED\u5267\u6210\u529F", {
        // adminId: admin.id,
        dramaId: id,
        dramaTitle: drama.title,
        ip: getClientIP(event)
      });
      return {
        success: true,
        data: {
          message: "\u77ED\u5267\u5220\u9664\u6210\u529F"
        }
      };
    } catch (dbError) {
      await connection.rollback();
      throw dbError;
    } finally {
      connection.release();
    }
  } catch (error) {
    logger.error("\u5220\u9664\u77ED\u5267\u5931\u8D25", {
      error: error.message,
      dramaId: getRouterParam(event, "id"),
      // adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    if (error.code === "ER_ROW_IS_REFERENCED_2") {
      throw createError({
        statusCode: 400,
        statusMessage: "\u8BE5\u77ED\u5267\u5B58\u5728\u5173\u8054\u6570\u636E\uFF0C\u65E0\u6CD5\u5220\u9664"
      });
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { _id__delete as default };
//# sourceMappingURL=_id_.delete.mjs.map
