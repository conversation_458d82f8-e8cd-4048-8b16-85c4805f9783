import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as get<PERSON><PERSON>er<PERSON><PERSON><PERSON>, f as createError, q as query, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import { c as validateId } from '../../../../_/validators.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__get = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const artistId = validateId(getRouterParam(event, "id"));
    if (!artistId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u827A\u4EBAID"
      });
    }
    const artists = await query(
      `SELECT id, name, avatar_url, bio, tags, role_type, sort_order, is_active, created_at, updated_at
       FROM actors
       WHERE id = ?`,
      [artistId]
    );
    if (artists.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u827A\u4EBA\u4E0D\u5B58\u5728"
      });
    }
    const artist = artists[0];
    return {
      success: true,
      data: {
        id: artist.id,
        name: artist.name,
        avatarUrl: artist.avatar_url,
        bio: artist.bio,
        tags: artist.tags,
        roleType: artist.role_type,
        sortOrder: artist.sort_order,
        status: artist.is_active === 1 ? "active" : "inactive",
        createdAt: artist.created_at,
        updatedAt: artist.updated_at
      }
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u827A\u4EBA\u8BE6\u60C5\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      artistId: getRouterParam(event, "id"),
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { _id__get as default };
//# sourceMappingURL=_id_.get.mjs.map
