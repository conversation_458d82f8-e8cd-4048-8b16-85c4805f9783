import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, Z as verifyUserAccessToken, f as createError, g as getQuery, q as query } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const records_get = defineEventHandler(async (event) => {
  try {
    console.log("Recharge records API called");
    const userPayload = verifyUserAccessToken(event);
    if (!userPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u7528\u6237\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"
      });
    }
    const userId = userPayload.id;
    const queryParams = getQuery(event);
    const page = parseInt(queryParams.page) || 1;
    const pageSize = parseInt(queryParams.pageSize) || 10;
    const offset = (page - 1) * pageSize;
    const countQuery = `
      SELECT COUNT(*) as total
      FROM user_asset_transactions
      WHERE user_id = ? 
        AND transaction_type = 'shells_in' 
        AND related_type = 'recharge'
    `;
    const countResult = await query(countQuery, [userId]);
    const total = countResult[0].total;
    const recordsQuery = `
      SELECT
        id,
        amount,
        balance_before,
        balance_after,
        transaction_no,
        description,
        status,
        DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s') as created_at,
        DATE_FORMAT(updated_at, '%Y-%m-%d %H:%i:%s') as updated_at
      FROM user_asset_transactions
      WHERE user_id = ? 
        AND transaction_type = 'shells_in' 
        AND related_type = 'recharge'
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    const recordsRows = await query(recordsQuery, [userId, pageSize, offset]);
    const records = recordsRows.map((record) => ({
      id: record.id,
      transactionNo: record.transaction_no,
      // 只显示TXN开头的交易号
      amount: parseFloat(record.amount),
      balanceBefore: parseFloat(record.balance_before),
      balanceAfter: parseFloat(record.balance_after),
      description: record.description,
      status: record.status,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
      completedAt: record.status === "completed" ? record.updated_at : null,
      // 模拟支付方式（实际应该从订单表或支付记录表获取）
      paymentMethod: getRandomPaymentMethod(),
      failReason: record.status === "failed" ? "\u652F\u4ED8\u8D85\u65F6" : null
    }));
    return {
      success: true,
      data: {
        records,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize)
        }
      }
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u5145\u503C\u8BB0\u5F55\u5931\u8D25:", error);
    return {
      success: false,
      message: "\u83B7\u53D6\u5145\u503C\u8BB0\u5F55\u5931\u8D25",
      error: error.message
    };
  }
});
function getRandomPaymentMethod() {
  const methods = ["alipay", "wechat", "bank"];
  return methods[Math.floor(Math.random() * methods.length)];
}

export { records_get as default };
//# sourceMappingURL=records.get.mjs.map
