<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 pt-16">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden z-0">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-primary opacity-10 rounded-full"></div>
      <div class="absolute top-1/4 -left-20 w-60 h-60 bg-secondary opacity-10 rounded-full"></div>
      <div class="absolute bottom-20 right-1/3 w-40 h-40 bg-primary opacity-10 rounded-full"></div>
    </div>
    
    <div class="relative z-10 bg-white rounded-xl shadow-xl p-8 w-full max-w-md">
      <!-- Header: Logo, Title and Subtitle -->
      <div class="text-center mb-8">
        <!-- Logo -->
        <div class="flex justify-center mb-4">
          <div class="flex items-center">
            <svg class="h-10 w-auto mr-2" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stop-color="#8667F0" />
                  <stop offset="100%" stop-color="#6039E4" />
                </linearGradient>
              </defs>
              <rect width="100" height="100" rx="20" fill="url(#logoGradient)"/>
              <path d="M30 70V30H45C50.5228 30 55 34.4772 55 40C55 45.5228 50.5228 50 45 50H35V70H30Z" fill="white"/>
              <path d="M60 30H65V70H60V30Z" fill="white"/>
              <path d="M75 30H80V70H75V30Z" fill="white"/>
            </svg>
            <span class="text-xl font-bold text-gradient">剧投投</span>
          </div>
        </div>
        <h1 class="text-3xl font-bold text-gray-800 mb-2">用户注册</h1>
        <p class="text-gray-600">创建账户，开启您的投资旅程</p>
      </div>

      <!-- Registration Form -->
      <form @submit.prevent="handleRegister" class="space-y-6">
        <div>
          <input
            type="tel"
            id="phone"
            v-model="phone"
            class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
            placeholder="手机号码"
            required
            pattern="^1[3-9]\d{9}$"
            maxlength="11"
          >
        </div>

        <div>
          <input
            type="text"
            id="username"
            v-model="username"
            class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
            placeholder="用户名"
            required
          >
        </div>

        <!-- 用户类型选择 -->
        <div>
          <div class="grid grid-cols-3 gap-3">
            <button
              type="button"
              @click="selectUserType('investor')"
              :class="[
                'px-4 py-3 border rounded-lg text-sm font-medium transition-all duration-200',
                userType === 'investor'
                  ? 'border-primary bg-primary text-white'
                  : 'border-gray-300 bg-white text-gray-700 hover:border-primary hover:text-primary'
              ]"
            >
              投资者
            </button>
            <button
              type="button"
              @click="selectUserType('producer')"
              :class="[
                'px-4 py-3 border rounded-lg text-sm font-medium transition-all duration-200',
                userType === 'producer'
                  ? 'border-primary bg-primary text-white'
                  : 'border-gray-300 bg-white text-gray-700 hover:border-primary hover:text-primary'
              ]"
            >
              承制厂牌
            </button>
            <button
              type="button"
              @click="selectUserType('fund_manager')"
              :class="[
                'px-4 py-3 border rounded-lg text-sm font-medium transition-all duration-200',
                userType === 'fund_manager'
                  ? 'border-primary bg-primary text-white'
                  : 'border-gray-300 bg-white text-gray-700 hover:border-primary hover:text-primary'
              ]"
            >
              基金管理人
            </button>
          </div>
          <!-- 用户类型说明 -->
          <div v-if="userType" class="mt-2 text-xs text-gray-500 text-center">
            <span v-if="userType === 'investor'">个人或机构投资者，参与短剧项目投资</span>
            <span v-if="userType === 'producer'">短剧制作公司，发布项目寻求投资</span>
            <span v-if="userType === 'fund_manager'">基金管理人，管理投资基金</span>
          </div>
        </div>

        <div>
          <input
            type="email"
            id="email"
            v-model="email"
            class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
            placeholder="电子邮箱（可选）"
          >
        </div>

        <div>
          <input 
            type="password" 
            id="password" 
            v-model="password" 
            class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary" 
            placeholder="密码 (至少8位，包含小写字母和数字)"
            required
            minlength="8"
            @input="checkPasswordStrength"
          >
          
          <!-- 密码强度进度条 -->
          <div class="mt-2">
            <div class="flex justify-between mb-1">
              <span class="text-xs font-medium text-gray-600">密码强度</span>
              <span class="text-xs font-medium" :class="passwordStrengthTextColor">{{ passwordStrengthText }}</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="h-2 rounded-full transition-all duration-300"
                :class="passwordStrengthBarColor"
                :style="{ width: passwordStrength + '%' }"></div>
            </div>
            <div class="mt-1">
              <ul class="text-xs text-gray-500 space-y-1">
                <li :class="{ 'text-green-500': hasMinLength }">✓ 至少8个字符</li>
                <li :class="{ 'text-green-500': hasLowerCase }">✓ 包含小写字母 (a-z)</li>
                <li :class="{ 'text-green-500': hasNumber }">✓ 包含数字 (0-9)</li>
              </ul>
            </div>
          </div>
        </div>

        <div>
          <input 
            type="password" 
            id="confirmPassword" 
            v-model="confirmPassword" 
            class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary" 
            placeholder="确认密码"
            required
          >
        </div>

        <!-- 错误消息显示 -->
        <div v-if="errorMessage" class="text-red-500 text-sm">
          {{ errorMessage }}
        </div>

        <!-- Register Button -->
        <button
          type="submit"
          class="w-full bg-gradient-primary text-white font-medium py-3 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-70 disabled:cursor-not-allowed"
          :disabled="isSubmitting || !isPasswordValid"
        >
          <span v-if="isSubmitting">注册中...</span>
          <span v-else>创建账户</span>
        </button>

        <!-- 用户协议和隐私政策勾选框 -->
        <div class="mt-4">
          <div
            ref="agreementContainer"
            class="flex items-center justify-center space-x-2"
            :class="{ 'shake-animation': showAgreementError }"
          >
            <input
              type="checkbox"
              id="agreeTerms"
              v-model="agreeTerms"
              class="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"
              @change="clearAgreementError"
            >
            <label for="agreeTerms" class="text-sm text-gray-600 leading-relaxed cursor-pointer whitespace-nowrap">
              注册即同意<button
                type="button"
                @click="openAgreement('user-agreement')"
                class="text-primary hover:text-primary-dark hover:underline font-medium"
              >《用户协议》</button><button
                type="button"
                @click="openAgreement('privacy-policy')"
                class="text-primary hover:text-primary-dark hover:underline font-medium"
              >《隐私政策》</button>和<button
                type="button"
                @click="openAgreement('terms-of-service')"
                class="text-primary hover:text-primary-dark hover:underline font-medium"
              >《服务条款》</button>
            </label>
          </div>

          <!-- 协议错误提示 -->
          <div v-if="showAgreementError" class="text-center mt-1">
            <span class="text-red-500 text-xs">请先同意《用户协议》、《隐私政策》和《服务条款》</span>
          </div>
        </div>
      </form>

      <!-- Link to Login -->
      <div class="mt-8 text-center">
        <p class="text-sm text-gray-600">
          已有账户？
          <RouterLink to="/login" class="font-medium text-primary hover:text-primary-dark hover:underline ml-1">
            立即登录
          </RouterLink>
        </p>
      </div>

      <!-- Footer -->
      <div class="mt-10 text-center">
        <p class="text-xs text-gray-500">剧投投 - 创造精彩，共享未来</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { RouterLink, useRouter } from 'vue-router';
import axios from 'axios';
import { Form, Field, ErrorMessage } from 'vee-validate';
import { defineRule } from 'vee-validate';
import { required, email as emailRule, min, confirmed } from '@vee-validate/rules';
import { getCompleteApiUrl } from '../utils/environmentConfig';
import { getUserAgreement, getPrivacyPolicy, getAgreementByIdentifier } from '../api/agreement';
import Swal from 'sweetalert2';


// 注册验证规则
defineRule('required', required);
defineRule('email', emailRule);
defineRule('min', min);
defineRule('confirmed', confirmed);

const router = useRouter();
const username = ref('');
const email = ref('');
const phone = ref('');
const password = ref('');
const confirmPassword = ref('');
const userType = ref(''); // 默认未选择
const agreeTerms = ref(false);
const isSubmitting = ref(false);
const errorMessage = ref('');
const showAgreementError = ref(false);
const agreementContainer = ref(null);

// 密码强度相关
const passwordStrength = ref(0);
const hasMinLength = ref(false);
const hasLowerCase = ref(false);
const hasNumber = ref(false);

// 获取API基础URL
const apiUrl = getCompleteApiUrl();

// 选择用户类型
const selectUserType = (type) => {
  userType.value = type;
};



// 检查密码强度
const checkPasswordStrength = () => {
  const value = password.value;

  // 检查各项条件
  hasMinLength.value = value.length >= 8;
  hasLowerCase.value = /[a-z]/.test(value);
  hasNumber.value = /[0-9]/.test(value);

  // 计算强度（基于3个条件，每个条件约33.33%）
  let strength = 0;

  if (value.length >= 8) strength += 34;
  if (/[a-z]/.test(value)) strength += 33;
  if (/[0-9]/.test(value)) strength += 33;

  passwordStrength.value = strength;
};

// 密码强度文本
const passwordStrengthText = computed(() => {
  if (passwordStrength.value === 0) return '未输入';
  if (passwordStrength.value < 50) return '弱';
  if (passwordStrength.value < 75) return '中';
  if (passwordStrength.value < 100) return '强';
  return '非常强';
});

// 密码强度颜色
const passwordStrengthBarColor = computed(() => {
  if (passwordStrength.value < 50) return 'bg-red-500';
  if (passwordStrength.value < 75) return 'bg-yellow-500';
  if (passwordStrength.value < 100) return 'bg-green-500';
  return 'bg-green-600';
});

// 密码强度文本颜色
const passwordStrengthTextColor = computed(() => {
  if (passwordStrength.value === 0) return 'text-gray-500';
  if (passwordStrength.value < 50) return 'text-red-500';
  if (passwordStrength.value < 75) return 'text-yellow-500';
  if (passwordStrength.value < 100) return 'text-green-500';
  return 'text-green-600';
});

// 密码是否有效（满足所有条件）
const isPasswordValid = computed(() => {
  return hasMinLength.value && hasLowerCase.value && hasNumber.value;
});

const handleRegister = async () => {
  try {
    errorMessage.value = '';

    // 验证是否选择了用户类型
    if (!userType.value) {
      errorMessage.value = '请选择用户类型';
      return;
    }

    // 验证是否同意用户协议和隐私政策
    if (!agreeTerms.value) {
      showAgreementError.value = true;
      return;
    }
    
    if (password.value !== confirmPassword.value) {
      errorMessage.value = '两次输入的密码不一致，请检查';
      return;
    }
    
    // 强化密码验证
    if (!isPasswordValid.value) {
      errorMessage.value = '密码不符合要求，请确保包含小写字母和数字，且长度至少为8位';
      return;
    }
    
    // 验证手机号格式
    if (phone.value) {
      const phonePattern = /^1[3-9]\d{9}$/;
      if (!phonePattern.test(phone.value)) {
        errorMessage.value = '请输入正确的手机号码';
        return;
      }
    }
    
    // 验证邮箱格式（如果提供了邮箱）
    if (email.value && email.value.trim()) {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailPattern.test(email.value.trim())) {
        errorMessage.value = '请输入正确的邮箱地址';
        return;
      }
    }

    isSubmitting.value = true;
    
    // 准备注册数据
    const registerData = {
      username: username.value,
      password: password.value,
      email: email.value && email.value.trim() ? email.value.trim() : undefined,
      phone: phone.value || undefined,
      user_type: userType.value
    };

    // 发送注册请求
    const response = await axios.post(`${apiUrl}/auth/register`, registerData);
    
    // 注册成功
    if (response.data.success) {
      alert('注册成功！即将跳转到登录页面。');
      router.push('/login');
    }
  } catch (error) {
    console.error('注册失败:', error);
    
    // 处理错误响应
    if (error.response) {
      // 409状态码表示冲突(用户名、邮箱或手机号已存在)
      if (error.response.status === 409) {
        errorMessage.value = error.response.data.message || '该用户名、邮箱或手机号已被注册';
      } else {
        errorMessage.value = error.response.data.message || '注册失败，请稍后重试';
      }
    } else {
      errorMessage.value = '网络错误，无法连接到服务器';
    }
  } finally {
    isSubmitting.value = false;
  }
};

// 打开协议页面
const openAgreement = (type: string) => {
  // 在新标签页中打开协议页面
  const routeUrl = router.resolve({ name: 'agreement', params: { type } });
  window.open(routeUrl.href, '_blank');
};

// 清除协议错误提示
const clearAgreementError = () => {
  showAgreementError.value = false;
};
</script>

<style scoped>
.text-gradient {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary;
}

.bg-gradient-primary {
  @apply bg-gradient-to-r from-primary to-secondary hover:from-primary-dark hover:to-secondary-dark;
}

/* Custom checkbox style */
.form-checkbox {
  appearance: none;
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border: 1px solid #D1D5DB;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  display: inline-block;
  position: relative;
  vertical-align: middle;
}



.form-checkbox:checked {
  background-color: #8667F0; /* 直接使用紫色色值 */
  border-color: #8667F0; /* 直接使用紫色色值 */
}

.form-checkbox:checked::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 1px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.form-checkbox:focus {
  outline: none;
  box-shadow: 0 0 0 2px theme('colors.gray.200');
}

/* 抖动动画 */
.shake-animation {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}


</style> 