import COS from 'cos-nodejs-sdk-v5';
import path from 'path';
import { readFile } from 'fs/promises';
import { g as generateUniqueFilename } from './file-utils.mjs';
import { l as logger, q as query } from './nitro.mjs';

async function getCOSConfig() {
  try {
    const result = await query(
      "SELECT setting_value FROM system_settings WHERE setting_key = ?",
      ["cos_settings"]
    );
    if (result.length > 0 && result[0].setting_value) {
      const cosSettings = JSON.parse(result[0].setting_value);
      return {
        secretId: cosSettings.secretId || "",
        secretKey: cosSettings.secretKey || "",
        region: cosSettings.region || "ap-guangzhou",
        bucket: cosSettings.bucket || "",
        directory: cosSettings.directory || "mengtutv"
      };
    }
    return {
      secretId: "",
      secretKey: "",
      region: "ap-guangzhou",
      bucket: "",
      directory: "mengtutv"
    };
  } catch (error) {
    logger.error("\u83B7\u53D6COS\u914D\u7F6E\u5931\u8D25", { error: error.message });
    throw new Error("\u83B7\u53D6COS\u914D\u7F6E\u5931\u8D25");
  }
}
async function createCOSClient() {
  const cosConfig = await getCOSConfig();
  if (!cosConfig.secretId || !cosConfig.secretKey || !cosConfig.bucket) {
    throw new Error("COS\u914D\u7F6E\u4E0D\u5B8C\u6574\uFF0C\u8BF7\u68C0\u67E5\u7CFB\u7EDF\u8BBE\u7F6E\u4E2D\u7684\u5BF9\u8C61\u5B58\u50A8\u914D\u7F6E");
  }
  return new COS({
    SecretId: cosConfig.secretId,
    SecretKey: cosConfig.secretKey,
    FileParallelLimit: 3,
    ChunkParallelLimit: 8,
    ChunkSize: 1024 * 1024 * 8
    // 8MB
  });
}
async function uploadToCOS(fileInfo, destPath) {
  const cosConfig = await getCOSConfig();
  const cos = await createCOSClient();
  try {
    let objectKey;
    const baseDirectory = cosConfig.directory || "mengtutv";
    if (destPath) {
      const normalizedDestPath = destPath.replace(/\\/g, "/");
      if (normalizedDestPath.startsWith(baseDirectory + "/") || normalizedDestPath === baseDirectory) {
        objectKey = normalizedDestPath;
      } else {
        objectKey = path.posix.join(baseDirectory, normalizedDestPath);
      }
      logger.debug(`\u4F7F\u7528\u63D0\u4F9B\u7684\u76EE\u6807\u8DEF\u5F84: ${objectKey}`, {
        baseDirectory,
        originalDestPath: destPath,
        normalizedDestPath,
        finalObjectKey: objectKey
      });
    } else {
      const filename = generateUniqueFilename(fileInfo.originalname);
      objectKey = path.posix.join(baseDirectory, filename);
      logger.debug(`\u751F\u6210\u9ED8\u8BA4\u76EE\u6807\u8DEF\u5F84: ${objectKey}`, {
        baseDirectory,
        filename,
        finalObjectKey: objectKey
      });
    }
    logger.info("\u5F00\u59CB\u4E0A\u4F20\u6587\u4EF6\u5230COS", {
      originalName: fileInfo.originalname,
      objectKey,
      bucket: cosConfig.bucket,
      region: cosConfig.region
    });
    const result = await cos.putObject({
      Bucket: cosConfig.bucket,
      Region: cosConfig.region,
      Key: objectKey,
      Body: await readFile(fileInfo.path),
      // 读取文件内容
      ContentType: fileInfo.mimetype,
      onProgress: (progressData) => {
        logger.debug("\u4E0A\u4F20\u8FDB\u5EA6", {
          percent: Math.round(progressData.percent * 100),
          speed: progressData.speed
        });
      }
    });
    const url = `https://${cosConfig.bucket}.cos.${cosConfig.region}.myqcloud.com/${objectKey}`;
    logger.info("\u6587\u4EF6\u4E0A\u4F20\u5230COS\u6210\u529F", {
      originalName: fileInfo.originalname,
      objectKey,
      url,
      etag: result.ETag
    });
    return {
      url,
      key: objectKey,
      location: result.Location
    };
  } catch (error) {
    logger.error("\u4E0A\u4F20\u6587\u4EF6\u5230COS\u5931\u8D25", {
      originalName: fileInfo.originalname,
      error: error.message,
      code: error.code
    });
    throw new Error(`COS\u4E0A\u4F20\u5931\u8D25: ${error.message}`);
  }
}

export { uploadToCOS as u };
//# sourceMappingURL=cos-uploader.mjs.map
