-- 用户资产表（已优化，修复主外键类型匹配问题）
CREATE TABLE IF NOT EXISTS user_assets (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    shells_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT '贝壳余额',
    diamonds_balance DECIMAL(15,2) DEFAULT 0.00 COMMENT '钻石余额',
    total_invested_shells DECIMAL(15,2) DEFAULT 0.00 COMMENT '总投资贝壳数',
    total_earned_diamonds DECIMAL(15,2) DEFAULT 0.00 COMMENT '总收益钻石数',
    frozen_shells DECIMAL(15,2) DEFAULT 0.00 COMMENT '冻结贝壳数',
    frozen_diamonds DECIMAL(15,2) DEFAULT 0.00 COMMENT '冻结钻石数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    CONSTRAINT fk_user_assets_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_assets (user_id),
    INDEX idx_user_assets_user_id (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户资产表';

-- 用户投资记录表（已优化）
CREATE TABLE IF NOT EXISTS user_investments (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    project_id BIGINT UNSIGNED NOT NULL COMMENT '项目ID',
    project_name VARCHAR(255) NOT NULL COMMENT '项目名称',
    investment_amount DECIMAL(15,2) NOT NULL COMMENT '投资金额（贝壳）',
    investment_date DATE NOT NULL COMMENT '投资日期',
    expected_return_rate DECIMAL(5,2) DEFAULT 0.00 COMMENT '预期收益率(%)',
    expected_return_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '预期收益金额（钻石）',
    actual_return_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT '实际收益金额（钻石）',
    project_status ENUM('active', 'completed', 'paused', 'cancelled') DEFAULT 'active' COMMENT '项目状态',
    investment_status ENUM('active', 'completed', 'cancelled') DEFAULT 'active' COMMENT '投资状态',
    start_date DATE COMMENT '项目开始日期',
    end_date DATE COMMENT '项目结束日期',
    progress INT DEFAULT 0 COMMENT '项目进度(0-100)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    CONSTRAINT fk_user_investments_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_investments_user_id (user_id),
    INDEX idx_user_investments_project_id (project_id),
    INDEX idx_user_investments_date (investment_date),
    INDEX idx_user_investments_status (investment_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户投资记录表';

-- 用户收益记录表（已优化）
CREATE TABLE IF NOT EXISTS user_returns (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    investment_id BIGINT UNSIGNED NOT NULL COMMENT '投资记录ID',
    return_type ENUM('monthly', 'quarterly', 'final', 'bonus') DEFAULT 'monthly' COMMENT '收益类型',
    return_amount DECIMAL(15,2) NOT NULL COMMENT '收益金额（钻石）',
    return_date DATE NOT NULL COMMENT '收益日期',
    return_period VARCHAR(50) COMMENT '收益期间',
    description TEXT COMMENT '收益描述',
    status ENUM('pending', 'paid', 'cancelled') DEFAULT 'pending' COMMENT '收益状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    CONSTRAINT fk_user_returns_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_returns_investment_id FOREIGN KEY (investment_id) REFERENCES user_investments(id) ON DELETE CASCADE,
    INDEX idx_user_returns_user_id (user_id),
    INDEX idx_user_returns_investment_id (investment_id),
    INDEX idx_user_returns_date (return_date),
    INDEX idx_user_returns_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收益记录表';

-- 用户通知表（已优化）
CREATE TABLE IF NOT EXISTS user_notifications (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    type ENUM('system', 'project', 'return', 'investment', 'security') DEFAULT 'system' COMMENT '通知类型',
    title VARCHAR(255) NOT NULL COMMENT '通知标题',
    content TEXT COMMENT '通知内容',
    related_id BIGINT UNSIGNED COMMENT '关联ID（如投资ID、项目ID等）',
    related_type VARCHAR(50) COMMENT '关联类型',
    is_read TINYINT(1) DEFAULT 0 COMMENT '是否已读',
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal' COMMENT '优先级',
    expires_at TIMESTAMP NULL COMMENT '过期时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    CONSTRAINT fk_user_notifications_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_notifications_user_id (user_id),
    INDEX idx_user_notifications_type (type),
    INDEX idx_user_notifications_read (is_read),
    INDEX idx_user_notifications_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户通知表';

-- 用户资产变动记录表（已优化）
CREATE TABLE IF NOT EXISTS user_asset_transactions (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    transaction_type ENUM('shells_in', 'shells_out', 'diamonds_in', 'diamonds_out') NOT NULL COMMENT '交易类型',
    amount DECIMAL(15,2) NOT NULL COMMENT '交易金额',
    balance_before DECIMAL(15,2) NOT NULL COMMENT '交易前余额',
    balance_after DECIMAL(15,2) NOT NULL COMMENT '交易后余额',
    related_id BIGINT UNSIGNED COMMENT '关联ID',
    related_type VARCHAR(50) COMMENT '关联类型（investment, return, recharge, withdraw等）',
    description VARCHAR(500) COMMENT '交易描述',
    transaction_no VARCHAR(100) UNIQUE COMMENT '交易流水号',
    status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '交易状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    CONSTRAINT fk_user_asset_transactions_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_asset_transactions_user_id (user_id),
    INDEX idx_user_asset_transactions_type (transaction_type),
    INDEX idx_user_asset_transactions_no (transaction_no),
    INDEX idx_user_asset_transactions_status (status),
    INDEX idx_user_asset_transactions_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户资产变动记录表';
