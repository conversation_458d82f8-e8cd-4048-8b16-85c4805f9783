import { createRouter, createWebHistory, RouteRecordRaw, NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import { useAuthStore } from '../store/auth'

// 路由元信息类型定义
declare module 'vue-router' {
  interface RouteMeta {
    requiresAuth?: boolean
    requiresKYC?: boolean
    title?: string
    description?: string
  }
}

// 路由配置
const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'home',
    component: () => import('../views/HomeView.vue'),
    meta: {
      title: '首页',
      description: '剧投投投资平台首页'
    }
  },
  {
    path: '/investment',
    name: 'investment',
    component: () => import('../views/InvestmentView.vue'),
    meta: {
      title: '投资机会',
      description: '查看最新的投资机会'
    }
  },
  {
    path: '/team',
    name: 'team',
    component: () => import('../views/TeamView.vue'),
    meta: {
      title: '团队介绍',
      description: '了解我们的专业团队'
    }
  },
  {
    path: '/agency',
    name: 'agency',
    component: () => import('../views/AgencyView.vue'),
    meta: {
      title: '代理合作',
      description: '代理合作相关信息'
    }
  },
  {
    path: '/news',
    name: 'news',
    component: () => import('../views/NewsView.vue'),
    meta: {
      title: '新闻动态',
      description: '最新的行业新闻和公司动态'
    }
  },
  {
    path: '/news/:id',
    name: 'news-detail',
    component: () => import('../views/NewsDetailView.vue'),
    props: true,
    meta: {
      title: '新闻详情',
      description: '查看新闻详细内容'
    }
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: () => import('../views/DashboardView.vue'),
    meta: {
      requiresAuth: true,
      title: '用户中心',
      description: '个人投资管理中心'
    }
  },
  {
    path: '/investment-records',
    name: 'investment-records',
    component: () => import('../views/InvestmentRecordsView.vue'),
    meta: {
      requiresAuth: true,
      title: '投资记录',
      description: '查看所有投资记录'
    }
  },
  {
    path: '/recharge-records',
    name: 'recharge-records',
    component: () => import('../views/RechargeRecordsView.vue'),
    meta: {
      requiresAuth: true,
      title: '充值记录',
      description: '查看所有充值记录'
    }
  },
  {
    path: '/project/:id',
    name: 'project-detail',
    component: () => import('../views/ProjectDetailView.vue'),
    props: true,
    meta: {
      title: '项目详情',
      description: '查看项目详细信息'
    }
  },
  {
    path: '/projects',
    name: 'projects',
    component: () => import('../views/ProjectsView.vue'),
    meta: {
      title: '项目列表',
      description: '浏览所有可投资项目'
    }
  },
  {
    path: '/funds',
    name: 'funds',
    component: () => import('../views/FundsView.vue'),
    meta: {
      title: '基金产品',
      description: '查看所有基金产品'
    }
  },
  {
    path: '/funds/recruit',
    name: 'funds-recruit',
    component: () => import('../views/funds/FundsRecruitView.vue'),
    meta: {
      title: '基金募集',
      description: '基金募集相关信息'
    }
  },
  {
    path: '/funds/:id',
    name: 'fund-detail',
    component: () => import('../views/funds/FundDetailView.vue'),
    props: true,
    meta: {
      title: '基金详情',
      description: '查看基金详细信息'
    }
  },
  {
    path: '/funds/disclosure',
    name: 'funds-disclosure',
    component: () => import('../views/funds/FundsDisclosureView.vue'),
    meta: { 
      requiresAuth: true,
      title: '信息披露',
      description: '基金信息披露'
    }
  },
  {
    path: '/funds/apply',
    name: 'funds-apply',
    component: () => import('../views/funds/FundsApplyView.vue'),
    meta: { 
      requiresAuth: true, 
      requiresKYC: true,
      title: '基金申购',
      description: '申购基金产品'
    }
  },
  {
    path: '/studio',
    name: 'studio',
    component: () => import('../views/StudioView.vue'),
    meta: {
      title: '工作室',
      description: '工作室相关信息'
    }
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('../views/LoginView.vue'),
    meta: {
      title: '用户登录',
      description: '登录您的账户'
    }
  },
  {
    path: '/register',
    name: 'register',
    component: () => import('../views/RegisterView.vue').catch(() => import('../views/RegisterView.vue')),
    meta: {
      title: '用户注册',
      description: '注册新账户'
    }
  },
  {
    path: '/reset-password/:token',
    name: 'reset-password',
    component: () => import('../views/ResetPasswordView.vue'),
    props: true,
    meta: {
      title: '重置密码',
      description: '重置您的账户密码'
    }
  },
  {
    path: '/agreement/:type',
    name: 'agreement',
    component: () => import('../views/AgreementView.vue'),
    props: true,
    meta: {
      title: '协议条款',
      description: '查看用户协议和隐私政策'
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局路由守卫
router.beforeEach((
  to: RouteLocationNormalized, 
  from: RouteLocationNormalized, 
  next: NavigationGuardNext
) => {
  // 初始化Pinia store
  const authStore = useAuthStore()
  
  // 检查路由是否需要认证
  if (to.matched.some(record => record.meta.requiresAuth)) {
    // 检查是否已登录
    if (!authStore.isLoggedIn) {
      // 尝试从localStorage初始化
      authStore.initAuth()
      
      // 如果仍未登录，跳转到登录页
      if (!authStore.isLoggedIn) {
        next({
          path: '/login',
          query: { redirect: to.fullPath } // 保存原始目标路由
        })
        return
      }
    }
    

    
    // 用户已认证，且满足权限要求，允许继续
    next()
  } else {
    // 不需要认证的路由直接放行
    next()
  }
})

export default router
