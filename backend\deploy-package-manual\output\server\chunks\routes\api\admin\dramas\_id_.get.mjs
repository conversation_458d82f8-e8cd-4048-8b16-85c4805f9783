import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, f as createError, q as query, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__get = defineEventHandler(async (event) => {
  var _a;
  try {
    const dramaId = getRouterParam(event, "id");
    if (!dramaId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u77ED\u5267ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const id = parseInt(dramaId);
    if (isNaN(id) || id <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u77ED\u5267ID"
      });
    }
    const dramaResult = await query(`
      SELECT
        ds.id, ds.title, ds.cover, ds.tags, ds.description, ds.episodes, ds.episode_length,
        ds.target_platform, ds.projected_views, ds.cast, ds.is_online, ds.creator_id,
        ds.created_at, ds.updated_at,
        dpt.production_company, dpt.co_production_company, dpt.executive_producer,
        dpt.co_executive_producer, dpt.chief_producer, dpt.producer, dpt.co_producer,
        dpt.director, dpt.scriptwriter, dpt.supervisor, dpt.coordinator,
        dps.schedule_pre_production, dps.schedule_filming, dps.schedule_post_production,
        dps.expected_release_date,
        dfi.funding_goal, dfi.current_funding, dfi.funding_end_date, dfi.funding_share,
        dfi.min_investment, dfi.roi, dfi.status,
        dai.risk_management, dai.confirmed_resources, dai.investment_tiers
      FROM drama_series ds
      LEFT JOIN drama_production_team dpt ON ds.id = dpt.drama_id
      LEFT JOIN drama_production_schedule dps ON ds.id = dps.drama_id
      LEFT JOIN drama_funding_info dfi ON ds.id = dfi.drama_id
      LEFT JOIN drama_additional_info dai ON ds.id = dai.drama_id
      WHERE ds.id = ?
    `, [id]);
    if (dramaResult.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u77ED\u5267\u4E0D\u5B58\u5728"
      });
    }
    const drama = dramaResult[0];
    const materialsResult = await query(`
      SELECT id, drama_id, title, url, thumbnail, type, sort_order, created_at, updated_at
      FROM drama_materials
      WHERE drama_id = ?
      ORDER BY sort_order ASC, created_at ASC
    `, [id]);
    const investmentTiersResult = await query(`
      SELECT id, drama_id, tier_name, min_amount, max_amount, benefits, return_rate,
             limited_quantity, sold_quantity, sort_order, is_active, created_at, updated_at
      FROM drama_investment_tiers
      WHERE drama_id = ?
      ORDER BY sort_order ASC, created_at ASC
    `, [id]);
    const documentsResult = await query(`
      SELECT id, drama_id, name, file_url, file_type, file_size, created_at, updated_at
      FROM drama_documents
      WHERE drama_id = ?
      ORDER BY created_at DESC
    `, [id]);
    const additionalInfoResult = await query(`
      SELECT id, drama_id, risk_management, confirmed_resources, created_at, updated_at
      FROM drama_additional_info
      WHERE drama_id = ?
    `, [id]);
    const formattedDrama = {
      id: drama.id,
      title: drama.title,
      cover: drama.cover,
      tags: drama.tags ? JSON.parse(drama.tags) : [],
      description: drama.description,
      episodes: drama.episodes,
      episodeLength: drama.episode_length,
      targetPlatform: drama.target_platform ? JSON.parse(drama.target_platform) : [],
      projectedViews: drama.projected_views,
      cast: drama.cast ? JSON.parse(drama.cast) : [],
      isOnline: drama.is_online,
      creatorId: drama.creator_id,
      createdAt: drama.created_at,
      updatedAt: drama.updated_at,
      // 制作团队信息
      productionTeam: {
        productionCompany: drama.production_company,
        coProductionCompany: drama.co_production_company,
        executiveProducer: drama.executive_producer,
        coExecutiveProducer: drama.co_executive_producer,
        chiefProducer: drama.chief_producer,
        producer: drama.producer,
        coProducer: drama.co_producer,
        director: drama.director,
        scriptwriter: drama.scriptwriter,
        supervisor: drama.supervisor,
        coordinator: drama.coordinator
      },
      // 制作进度信息
      productionSchedule: {
        preProduction: drama.schedule_pre_production,
        filming: drama.schedule_filming,
        postProduction: drama.schedule_post_production,
        expectedReleaseDate: drama.expected_release_date
      },
      // 募资信息
      fundingInfo: {
        fundingGoal: parseFloat(drama.funding_goal) || 0,
        currentFunding: parseFloat(drama.current_funding) || 0,
        fundingEndDate: drama.funding_end_date,
        fundingShare: parseFloat(drama.funding_share) || 0,
        minInvestment: parseFloat(drama.min_investment) || 0,
        roi: parseFloat(drama.roi) || 0,
        status: drama.status || "draft"
      },
      // 基础其他信息
      basicInfo: {
        riskManagement: drama.risk_management ? JSON.parse(drama.risk_management) : [],
        confirmedResources: drama.confirmed_resources ? JSON.parse(drama.confirmed_resources) : [],
        investmentTiers: drama.investment_tiers ? JSON.parse(drama.investment_tiers) : []
      },
      // 素材信息
      materials: materialsResult.map((material) => ({
        id: material.id,
        dramaId: material.drama_id,
        title: material.title,
        url: material.url,
        thumbnail: material.thumbnail,
        type: material.type,
        sortOrder: material.sort_order,
        createdAt: material.created_at,
        updatedAt: material.updated_at
      })),
      // 文档信息
      documents: documentsResult.map((document) => ({
        id: document.id,
        dramaId: document.drama_id,
        name: document.name,
        fileUrl: document.file_url,
        fileType: document.file_type,
        fileSize: document.file_size,
        createdAt: document.created_at,
        updatedAt: document.updated_at
      })),
      // 投资权益档位
      investmentTiers: investmentTiersResult.map((tier) => ({
        id: tier.id,
        dramaId: tier.drama_id,
        tierName: tier.tier_name,
        minAmount: parseFloat(tier.min_amount) || 0,
        maxAmount: tier.max_amount ? parseFloat(tier.max_amount) : null,
        benefits: tier.benefits,
        returnRate: tier.return_rate ? parseFloat(tier.return_rate) : null,
        limitedQuantity: tier.limited_quantity,
        soldQuantity: tier.sold_quantity || 0,
        sortOrder: tier.sort_order || 0,
        isActive: tier.is_active === 1,
        createdAt: tier.created_at,
        updatedAt: tier.updated_at
      })),
      // 其他信息
      additionalInfo: additionalInfoResult.length > 0 ? {
        id: additionalInfoResult[0].id,
        dramaId: additionalInfoResult[0].drama_id,
        riskManagement: additionalInfoResult[0].risk_management ? JSON.parse(additionalInfoResult[0].risk_management) : [],
        confirmedResources: additionalInfoResult[0].confirmed_resources ? JSON.parse(additionalInfoResult[0].confirmed_resources) : [],
        createdAt: additionalInfoResult[0].created_at,
        updatedAt: additionalInfoResult[0].updated_at
      } : null
    };
    return {
      success: true,
      data: formattedDrama
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u77ED\u5267\u8BE6\u60C5\u5931\u8D25", {
      error: error.message,
      dramaId: getRouterParam(event, "id"),
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { _id__get as default };
//# sourceMappingURL=_id_.get.mjs.map
