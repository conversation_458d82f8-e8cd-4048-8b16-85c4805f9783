import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, r as readBody, f as createError, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const sort_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const body = await readBody(event);
    const { orders } = body;
    if (!orders || !Array.isArray(orders) || orders.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6392\u5E8F\u6570\u636E\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    for (const order of orders) {
      if (!order.id || isNaN(Number(order.id)) || isNaN(Number(order.sortOrder))) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u6392\u5E8F\u6570\u636E\u683C\u5F0F\u9519\u8BEF"
        });
      }
    }
    const connection = await getConnection();
    await connection.beginTransaction();
    try {
      for (const order of orders) {
        await connection.execute(
          "UPDATE banners SET sort_order = ?, updated_at = NOW() WHERE id = ?",
          [Number(order.sortOrder), Number(order.id)]
        );
      }
      await connection.commit();
      await logAuditAction({
        action: "ADMIN_UPDATE_BANNER_SORT",
        description: `\u7BA1\u7406\u5458\u66F4\u65B0\u6A2A\u5E45\u6392\u5E8F`,
        userId: admin.id,
        username: admin.username,
        ip: getClientIP(event),
        userAgent: getHeader(event, "user-agent") || "",
        details: {
          orders: orders.map((o) => ({ id: Number(o.id), sortOrder: Number(o.sortOrder) }))
        }
      });
      logger.info("\u7BA1\u7406\u5458\u66F4\u65B0\u6A2A\u5E45\u6392\u5E8F\u6210\u529F", {
        adminId: admin.id,
        adminUsername: admin.username,
        updateCount: orders.length
      });
      return {
        success: true,
        message: "\u6A2A\u5E45\u6392\u5E8F\u66F4\u65B0\u6210\u529F",
        data: {
          updateCount: orders.length
        }
      };
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    logger.error("\u66F4\u65B0\u6A2A\u5E45\u6392\u5E8F\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { sort_post as default };
//# sourceMappingURL=sort.post.mjs.map
