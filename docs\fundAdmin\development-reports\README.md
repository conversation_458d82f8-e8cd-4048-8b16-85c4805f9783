# 短剧编辑功能说明

## 功能概述

基于现有的短剧详情查看功能，实现了完整的短剧编辑功能，包含以下特性：

- 保持与短剧详情页面相同的选项卡结构
- 将所有只读的表单字段改为可编辑状态
- 添加保存、取消、重置等操作按钮
- 实现表单验证和错误提示
- 每个选项卡都可以单独保存数据到对应数据库

## 页面结构

### 主页面 (index.vue)
- 管理三个视图状态：列表、详情、编辑
- 处理视图之间的切换逻辑

### 编辑页面 (edit.vue)
- 主编辑页面，包含7个选项卡
- 跟踪各个选项卡的更改状态
- 提供统一的保存和重置功能

### 编辑组件

#### 1. 基本信息编辑 (BasicInfoEdit.vue)
- 短剧标题、封面图片上传
- 状态、导演、制片人
- 集数、单集时长
- 募资相关信息
- 标签、演员阵容、目标平台
- 短剧描述

#### 2. 创作团队编辑 (TeamInfoEdit.vue)
- 导演、编剧、制片人
- 主演阵容（支持多个演员）
- 自动计算团队规模和经验

#### 3. 制作信息编辑 (ProductionInfoEdit.vue)
- 集数、单集时长、总时长计算
- 目标平台、预期发布日期
- 单集成本、预期观看量、投资回报率
- 自动计算制作类型、内容分级、制作周期

#### 4. 募资详情编辑 (FundingInfoEdit.vue)
- 目标募资金额、已募集金额
- 募资进度可视化
- 最低投资金额、剩余天数
- 预期回报率
- 自动计算募资状态、投资回报周期

#### 5. 制作排期编辑 (ScheduleInfoEdit.vue)
- 前期筹备、拍摄制作、后期制作、发行上线时间
- 总体进度可视化
- 自动计算当前阶段、预计完成时间、剩余工作日
- 项目状态和风险提示

#### 6. 项目素材编辑 (MaterialsInfoEdit.vue)
- 素材列表管理
- 支持图片和视频上传
- 素材排序、删除功能
- 视频缩略图上传

#### 7. 其他信息编辑 (OtherInfoEdit.vue)
- 确认资源、风险管理
- 投资层级管理（支持动态添加/删除）
- 详细描述、封面颜色、封面SVG
- 系统信息（只读）

## 技术特性

### 数据变化监听
- 每个编辑组件都实现了数据变化监听
- 主页面显示未保存更改的标识（*）
- 离开页面时提示未保存的更改

### 文件上传功能
- 支持封面图片上传
- 支持素材文件上传（图片/视频）
- 支持视频缩略图上传
- 上传进度显示

### 表单验证
- 必填字段验证
- 数据类型验证
- 数值范围验证

### API集成
- 扩展了后端API以支持所有字段的更新
- 动态更新语句，只更新提供的字段
- 完善的错误处理

## 使用方法

### 从列表页面编辑
1. 在短剧列表中点击"编辑"按钮
2. 进入编辑页面，选择要编辑的选项卡
3. 修改相应字段
4. 点击"保存"按钮保存更改

### 从详情页面编辑
1. 在短剧详情页面点击"编辑短剧"按钮
2. 进入编辑页面进行编辑

### 保存更改
- 每个选项卡都有独立的保存按钮
- 保存成功后会自动刷新数据
- 主页面的"保存所有更改"按钮提供批量保存提示

### 重置更改
- 每个选项卡都有重置按钮
- 重置会恢复到原始数据状态

## 样式一致性

- 使用与详情页面相同的布局结构（settings-container、a-form布局）
- 保持相同的标签列宽度（span: 4）和内容列宽度（span: 20）
- 维持相同的字体颜色和样式规范
- 编辑状态下移除input-disabled样式类

## 故障排除

### 常见问题

1. **页面无法加载**
   - 检查前端服务是否正常运行 (`pnpm dev`)
   - 检查后端服务是否正常运行
   - 查看浏览器控制台是否有错误信息

2. **图标不显示**
   - 确保使用了正确的图标导入 (`@vben/icons`)
   - 检查图标名称是否正确

3. **文件上传失败**
   - 检查对象存储配置是否正确
   - 确认文件大小和格式符合要求
   - 查看网络连接是否正常

4. **保存失败**
   - 检查表单验证是否通过
   - 确认后端API是否正常
   - 查看控制台错误信息

### 测试步骤

1. **启动服务**
   ```bash
   # 前端服务
   cd fundAdmin/apps/web-antd
   pnpm dev

   # 后端服务
   cd backend
   npm start
   ```

2. **访问页面**
   - 打开浏览器访问 `http://localhost:5666`
   - 登录管理员账户
   - 导航到"剧目管理"页面

3. **测试编辑功能**
   - 点击任意短剧的"编辑"按钮
   - 在各个选项卡中修改数据
   - 测试文件上传功能
   - 验证保存和重置功能

## 注意事项

1. 确保后端服务正常运行
2. 文件上传需要配置对象存储服务
3. 编辑权限需要管理员身份
4. 大文件上传可能需要较长时间
5. 建议在编辑前备份重要数据
6. 所有图标都使用 `@vben/icons` 包，不要使用 `@ant-design/icons-vue`
