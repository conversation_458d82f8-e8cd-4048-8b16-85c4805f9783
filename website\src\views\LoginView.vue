<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 pt-16">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 overflow-hidden z-0">
      <div class="absolute -top-40 -right-40 w-80 h-80 bg-primary opacity-10 rounded-full"></div>
      <div class="absolute top-1/4 -left-20 w-60 h-60 bg-secondary opacity-10 rounded-full"></div>
      <div class="absolute bottom-20 right-1/3 w-40 h-40 bg-primary opacity-10 rounded-full"></div>
    </div>
    
    <div class="relative z-10 bg-white rounded-xl shadow-xl p-8 w-full max-w-md">
      <!-- Logo -->
      <div class="flex justify-center mb-6">
        <div class="flex items-center">
          <svg class="h-10 w-auto mr-2" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stop-color="#8667F0" />
                <stop offset="100%" stop-color="#6039E4" />
              </linearGradient>
            </defs>
            <rect width="100" height="100" rx="20" fill="url(#logoGradient)"/>
            <path d="M30 70V30H45C50.5228 30 55 34.4772 55 40C55 45.5228 50.5228 50 45 50H35V70H30Z" fill="white"/>
            <path d="M60 30H65V70H60V30Z" fill="white"/>
            <path d="M75 30H80V70H75V30Z" fill="white"/>
          </svg>
          <span class="text-xl font-bold text-gradient">剧投投</span>
        </div>
      </div>
      
      <!-- Header: Title and Subtitle -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-2">用户登录</h1>
        <p class="text-gray-600">欢迎回来，继续您的旅程</p>
      </div>

      <!-- 主内容区：登录/重置密码 -->
      <div v-if="currentView === 'login'">
        <!-- Login Form -->
        <Form @submit="handleLogin" class="space-y-6" v-slot="{ errors }">
          <div>
            <Field 
              name="account" 
              type="text" 
              v-model="account" 
              rules="required"
              class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary" 
              placeholder="手机号/邮箱"
              :class="{ 'border-red-500': errors.account }"
            />
            <ErrorMessage name="account" class="text-red-500 text-sm mt-1" />
          </div>

          <div>
            <Field 
              name="password" 
              type="password" 
              v-model="password" 
              rules="required|min:8"
              class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary" 
              placeholder="密码"
              :class="{ 'border-red-500': errors.password }"
            />
            <ErrorMessage name="password" class="text-red-500 text-sm mt-1" />
          </div>
          
          <!-- 错误消息显示 -->
          <div v-if="errorMessage" class="text-red-500 text-sm">
            {{ errorMessage }}
          </div>
          
          <div class="flex items-center justify-between pt-2">
            <label class="flex items-center text-sm text-gray-600">
              <input 
                type="checkbox" 
                v-model="rememberMe"
                class="form-checkbox h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"
              >
              <span class="ml-2">记住我</span>
            </label>
            <button 
              type="button" 
              class="text-sm text-primary hover:text-primary-dark hover:underline"
              @click="switchToForgotPassword"
            >
              忘记密码？
            </button>
          </div>

          <!-- Login Button -->
          <button
            type="submit"
            class="w-full bg-gradient-primary text-white font-medium py-3 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-70 disabled:cursor-not-allowed"
            :disabled="isSubmitting"
          >
            <span v-if="isSubmitting">登录中...</span>
            <span v-else>登 录</span>
          </button>

          <!-- 用户协议和隐私政策勾选框 -->
          <div class="mt-4">
            <div
              ref="agreementContainer"
              class="flex items-center justify-center space-x-2"
              :class="{ 'shake-animation': showAgreementError }"
            >
              <input
                type="checkbox"
                id="agreeTerms"
                v-model="agreeTerms"
                class="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"
                @change="clearAgreementError"
              >
              <label for="agreeTerms" class="text-sm text-gray-600 leading-relaxed cursor-pointer whitespace-nowrap">
                登录即同意<button
                  type="button"
                  @click="openAgreement('user-agreement')"
                  class="text-primary hover:text-primary-dark hover:underline font-medium"
                >《用户协议》</button>和<button
                  type="button"
                  @click="openAgreement('privacy-policy')"
                  class="text-primary hover:text-primary-dark hover:underline font-medium"
                >《隐私政策》</button>
              </label>
            </div>

            <!-- 协议错误提示 -->
            <div v-if="showAgreementError" class="text-center mt-1">
              <span class="text-red-500 text-xs">请先同意《用户协议》和《隐私政策》</span>
            </div>
          </div>
        </Form>
      </div>

      <!-- 忘记密码表单 -->
      <div v-if="currentView === 'forgotPassword'" class="space-y-6">
        <Form @submit="handleForgotPassword" class="space-y-6" v-slot="{ errors }">
          <div>
            <Field 
              name="resetAccount" 
              type="text" 
              v-model="resetAccount" 
              rules="required|email"
              class="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary" 
              placeholder="注册邮箱"
              :class="{ 'border-red-500': errors.resetAccount }"
            />
            <ErrorMessage name="resetAccount" class="text-red-500 text-sm mt-1" />
          </div>
          
          <!-- 重置密码错误/成功消息 -->
          <div v-if="resetMessage" :class="[resetSuccess ? 'text-green-500' : 'text-red-500', 'text-sm']">
            {{ resetMessage }}
          </div>
          
          <!-- Submit Button -->
          <button 
            type="submit" 
            class="w-full bg-gradient-primary text-white font-medium py-3 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-70 disabled:cursor-not-allowed"
            :disabled="isResetting"
          >
            <span v-if="isResetting">发送中...</span>
            <span v-else>发送重置邮件</span>
          </button>
          
          <!-- 返回登录 -->
          <div class="text-center mt-4">
            <button 
              type="button" 
              class="text-sm text-primary hover:text-primary-dark hover:underline"
              @click="switchToLogin"
            >
              返回登录
            </button>
          </div>
        </Form>
      </div>

      <!-- Link to Register -->
      <div class="mt-8 text-center">
        <p class="text-sm text-gray-600">
          还没有账户？
          <RouterLink to="/register" class="font-medium text-primary hover:text-primary-dark hover:underline ml-1">
            立即注册
          </RouterLink>
        </p>
      </div>

      <!-- Footer -->
      <div class="mt-10 text-center">
        <p class="text-xs text-gray-500">剧投投 - 创造精彩，共享未来</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { RouterLink, useRouter, useRoute } from 'vue-router';
import axios from 'axios';
import { Form, Field, ErrorMessage, defineRule } from 'vee-validate';
import { required, email, min } from '@vee-validate/rules';
import { useAuthStore } from '../store/auth';
import Swal from 'sweetalert2';
import { getCompleteApiUrl } from '../utils/environmentConfig';
import type { LoginCredentials } from '../types';
import { getUserAgreement, getPrivacyPolicy } from '../api/agreement';

// 注册验证规则
defineRule('required', required);
defineRule('email', email);
defineRule('min', min);
defineRule('confirmed', (value: any, [other]: any[]) => {
  if (value === other) {
    return true;
  }
  return '两次输入的密码不一致';
});

const router = useRouter();
const route = useRoute();
const account = ref('');
const password = ref('');
const rememberMe = ref(false);
const isSubmitting = ref(false);
const errorMessage = ref('');
const agreeTerms = ref(false);
const showAgreementError = ref(false);
const agreementContainer = ref(null);

// 获取API基础URL
const apiUrl = getCompleteApiUrl();

// 检查是否有会话过期参数，并且不是从退出登录页面跳转过来的
onMounted(() => {
  // 检查URL参数中是否有会话过期标记
  if (route.query.expired === 'true') {
    // 检查是否是从退出登录跳转过来的
    const isFromLogout = document.referrer.includes('/dashboard') || 
                         document.referrer.includes('/admin') ||
                         sessionStorage.getItem('isLogout') === 'true';
    
    // 如果不是从退出登录跳转来的，才显示会话过期提示
    if (!isFromLogout) {
      Swal.fire({
        title: '会话已过期',
        text: '您的登录会话已过期，请重新登录',
        icon: 'info',
        confirmButtonText: '确定'
      });
    }
    
    // 清除标记
    sessionStorage.removeItem('isLogout');
  }
});

// 忘记密码相关
const currentView = ref('login'); // 'login' 或 'forgotPassword'
const resetAccount = ref('');
const isResetting = ref(false);
const resetMessage = ref('');
const resetSuccess = ref(false);

// 切换到忘记密码视图
const switchToForgotPassword = () => {
  currentView.value = 'forgotPassword';
  resetAccount.value = '';
  resetMessage.value = '';
  resetSuccess.value = false;
};

// 切换回登录视图
const switchToLogin = () => {
  currentView.value = 'login';
  errorMessage.value = '';
};

// 判断账号类型：手机号或邮箱
const getAccountType = (account: string): 'phone' | 'email' | 'unknown' => {
  // 手机号格式：1开头的11位数字
  const phonePattern = /^1[3-9]\d{9}$/;
  // 邮箱格式
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  if (phonePattern.test(account)) {
    return 'phone';
  } else if (emailPattern.test(account)) {
    return 'email';
  } else {
    return 'unknown';
  }
};

const handleLogin = async (values, { resetForm }) => {
  try {
    // 验证是否同意用户协议和隐私政策
    if (!agreeTerms.value) {
      showAgreementError.value = true;
      return;
    }

    // 验证账号格式
    const accountType = getAccountType(account.value);
    if (accountType === 'unknown') {
      errorMessage.value = '请输入正确的手机号或邮箱地址';
      return;
    }
    
    isSubmitting.value = true;
    errorMessage.value = '';
    
    // 准备登录数据 - 根据账号类型发送对应字段
    const loginData = {
      password: password.value
    };

    // 根据账号类型设置对应字段
    if (accountType === 'email') {
      loginData.email = account.value;
    } else if (accountType === 'phone') {
      loginData.phone = account.value;
    }

    console.log('=== 登录调试日志 ===');
    console.log('1. 发送登录请求');
    console.log('请求数据:', loginData);
    console.log('API地址:', `${apiUrl}/auth/login`);

    // 调用后端API
    const response = await axios.post(`${apiUrl}/auth/login`, loginData);

    console.log('2. 收到服务器响应');
    console.log('响应状态:', response.status);
    console.log('响应数据:', response.data);
    
    // 登录成功
    if (response.data.success) {
      console.log('3. 登录成功，处理用户数据');
      console.log('Token:', response.data.data.token);
      console.log('用户信息:', response.data.data.user);

      // 存储token和用户信息
      const authStore = useAuthStore();
      authStore.setToken(response.data.data.token);
      authStore.setUser(response.data.data.user);
      
      // 如果选择"记住我"，可以设置持久化存储
      if (rememberMe.value) {
        // 这里可以实现记住我功能，例如设置cookie有效期
      }
      
      console.log('4. 准备页面跳转');

      // 检查是否有重定向目标
      const redirectPath = router.currentRoute.value.query.redirect;
      if (redirectPath) {
        console.log('跳转到重定向页面:', redirectPath);
        // 跳转到原始请求的页面
        router.push(redirectPath.toString());
      } else {
        // 所有用户登录成功后都直接进入个人面板
        console.log('跳转到个人面板');
        router.push('/dashboard');
      }

      console.log('5. 登录流程完成');
    } else {
      console.log('3. 登录失败 - 服务器返回失败状态');
      console.log('失败原因:', response.data.message || '未知错误');
      errorMessage.value = response.data.message || '登录失败，请重试';
    }
  } catch (error) {
    console.log('=== 登录错误调试 ===');
    console.error('登录失败:', error);

    // 处理错误响应
    if (error.response) {
      console.log('错误类型: 服务器响应错误');
      console.log('状态码:', error.response.status);
      console.log('错误数据:', error.response.data);
      errorMessage.value = error.response.data.message || '登录失败，请稍后重试';
    } else if (error.request) {
      console.log('错误类型: 网络请求失败');
      console.log('请求对象:', error.request);
      errorMessage.value = '网络错误，无法连接到服务器';
    } else {
      console.log('错误类型: 其他错误');
      console.log('错误信息:', error.message);
      errorMessage.value = '网络错误，无法连接到服务器';
    }

    console.log('最终错误信息:', errorMessage.value);
  } finally {
    isSubmitting.value = false;
    console.log('登录请求结束，重置提交状态');
  }
};

// 处理忘记密码
const handleForgotPassword = async () => {
  try {
    isResetting.value = true;
    resetMessage.value = '';
    resetSuccess.value = false;
    
    // 调用后端API
    const response = await axios.post(`${apiUrl}/auth/forgot-password`, {
      email: resetAccount.value
    });
    
    if (response.data.success) {
      resetSuccess.value = true;
      resetMessage.value = response.data.message || `重置密码链接已发送到 ${resetAccount.value}，请查收邮件`;
      
      // 开发环境检查是否有令牌返回（仅用于测试）
      if (response.data.devToken) {
        console.log('开发环境重置令牌:', response.data.devToken);
      }
    }
  } catch (error) {
    console.error('发送重置邮件失败:', error);
    resetSuccess.value = false;
    
    if (error.response) {
      resetMessage.value = error.response.data.message || '发送失败，请稍后重试';
    } else {
      resetMessage.value = '网络错误，无法连接到服务器';
    }
  } finally {
    isResetting.value = false;
  }
};

// 打开协议页面
const openAgreement = async (type: 'user-agreement' | 'privacy-policy') => {
  try {
    let response;
    if (type === 'user-agreement') {
      response = await getUserAgreement();
    } else {
      response = await getPrivacyPolicy();
    }

    if (response.data.success && response.data.data) {
      const agreement = response.data.data;
      // 在新标签页中打开协议内容
      const newTab = window.open('', '_blank');
      if (newTab) {
        newTab.document.write(`
          <!DOCTYPE html>
          <html lang="zh-CN">
          <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${agreement.title}</title>
            <style>
              body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f9fafb;
              }
              .container {
                background: white;
                padding: 30px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              }
              h1 {
                color: #8667F0;
                border-bottom: 2px solid #8667F0;
                padding-bottom: 10px;
                margin-bottom: 20px;
              }
              .content {
                white-space: pre-wrap;
                word-wrap: break-word;
              }
              .footer {
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #e5e7eb;
                text-align: center;
                color: #6b7280;
                font-size: 14px;
              }
            </style>
          </head>
          <body>
            <div class="container">
              <h1>${agreement.title}</h1>
              <div class="content">${agreement.content}</div>
              <div class="footer">
                <p>最后更新时间：${new Date(agreement.updated_at).toLocaleDateString('zh-CN')}</p>
              </div>
            </div>
          </body>
          </html>
        `);
        newTab.document.close();
      }
    } else {
      Swal.fire({
        title: '获取失败',
        text: '无法获取协议内容，请稍后重试',
        icon: 'error',
        confirmButtonText: '确定'
      });
    }
  } catch (error) {
    console.error('获取协议内容失败:', error);
    Swal.fire({
      title: '获取失败',
      text: '无法获取协议内容，请稍后重试',
      icon: 'error',
      confirmButtonText: '确定'
    });
  }
};

// 清除协议错误提示
const clearAgreementError = () => {
  showAgreementError.value = false;
};
</script>

<style scoped>
.text-gradient {
  @apply text-primary font-bold;
}

.bg-gradient-primary {
  @apply bg-primary hover:bg-primary-dark;
}

/* Custom checkbox style */
.form-checkbox {
  appearance: none;
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border: 1px solid #D1D5DB;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  display: inline-block;
  position: relative;
  vertical-align: middle;
}

.form-checkbox:checked {
  background-color: #8667F0; /* 直接使用紫色色值 */
  border-color: #8667F0; /* 直接使用紫色色值 */
}

.form-checkbox:checked::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 1px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.form-checkbox:focus {
  outline: none;
  box-shadow: 0 0 0 2px theme('colors.gray.200');
}

/* 抖动动画 */
.shake-animation {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}
</style> 