import { c as defineEvent<PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, r as readBody, q as query, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const menus_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(adminPayload.id, PERMISSIONS.SYSTEM_MENU_CREATE);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u521B\u5EFA\u83DC\u5355"
      });
    }
    const body = await readBody(event);
    const {
      name,
      path,
      component,
      type,
      status = 1,
      authCode,
      icon,
      meta,
      sortOrder = 0,
      pid
    } = body;
    if (!name || !type) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u83DC\u5355\u540D\u79F0\u548C\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const validTypes = ["catalog", "menu", "button", "embedded", "link"];
    if (!validTypes.includes(type)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u83DC\u5355\u7C7B\u578B"
      });
    }
    const nameCheck = await query(`
      SELECT id FROM admin_menus WHERE name = ?
    `, [name]);
    if (nameCheck.length > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u83DC\u5355\u540D\u79F0\u5DF2\u5B58\u5728"
      });
    }
    if (path) {
      const pathCheck = await query(`
        SELECT id FROM admin_menus WHERE path = ?
      `, [path]);
      if (pathCheck.length > 0) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u83DC\u5355\u8DEF\u5F84\u5DF2\u5B58\u5728"
        });
      }
    }
    const result = await query(`
      INSERT INTO admin_menus (
        pid, name, path, component, type, status, auth_code, 
        icon, meta, sort_order, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      pid || null,
      name,
      path || null,
      component || null,
      type,
      status,
      authCode || null,
      icon || null,
      meta ? JSON.stringify(meta) : null,
      sortOrder
    ]);
    logger.info("\u521B\u5EFA\u83DC\u5355\u6210\u529F", {
      adminId: adminPayload.id,
      menuId: result.insertId,
      menuName: name,
      menuType: type,
      ip: getClientIP(event)
    });
    return {
      success: true,
      data: {
        id: result.insertId,
        message: "\u83DC\u5355\u521B\u5EFA\u6210\u529F"
      }
    };
  } catch (error) {
    logger.error("\u521B\u5EFA\u83DC\u5355\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { menus_post as default };
//# sourceMappingURL=menus.post.mjs.map
