-- 短剧表拆分SQL脚本
-- 将drama_series表拆分成5张独立的表
-- 执行前请备份数据库！

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- ============================================================================
-- 1. 创建新的拆分表结构
-- ============================================================================

-- 1.1 短剧制作团队表 (drama_production_team)
CREATE TABLE IF NOT EXISTS drama_production_team (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '制作团队ID',
    drama_id INT(11) NOT NULL COMMENT '关联的短剧ID',
    production_company VARCHAR(255) COMMENT '出品公司',
    co_production_company VARCHAR(255) COMMENT '联合出品公司',
    executive_producer VARCHAR(100) COMMENT '出品人',
    co_executive_producer VARCHAR(100) COMMENT '联合出品人',
    chief_producer VARCHAR(100) COMMENT '总制片人',
    producer VARCHAR(50) COMMENT '制片人',
    co_producer VARCHAR(100) COMMENT '联合制片人',
    director VARCHAR(50) COMMENT '导演姓名',
    scriptwriter VARCHAR(50) COMMENT '编剧姓名',
    supervisor VARCHAR(100) COMMENT '监制',
    coordinator VARCHAR(100) COMMENT '统筹',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_drama_production_team_drama_id (drama_id),
    INDEX idx_drama_production_team_drama_id (drama_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短剧制作团队表';

-- 1.2 短剧制作进度及周期表 (drama_production_schedule)
CREATE TABLE IF NOT EXISTS drama_production_schedule (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '制作进度ID',
    drama_id INT(11) NOT NULL COMMENT '关联的短剧ID',
    schedule_pre_production VARCHAR(500) COMMENT '前期制作筹备时间安排，包含起止时间',
    schedule_filming VARCHAR(500) COMMENT '拍摄时间安排，包含起止时间',
    schedule_post_production VARCHAR(500) COMMENT '后期制作时间安排，包含起止时间',
    expected_release_date VARCHAR(500) COMMENT '预期上线日期，包含起止时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_drama_production_schedule_drama_id (drama_id),
    INDEX idx_drama_production_schedule_drama_id (drama_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短剧制作进度及周期表';

-- 1.3 短剧募资信息表 (drama_funding_info)
CREATE TABLE IF NOT EXISTS drama_funding_info (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '募资信息ID',
    drama_id INT(11) NOT NULL COMMENT '关联的短剧ID',
    funding_goal DECIMAL(15,2) COMMENT '募资目标金额（元）',
    current_funding DECIMAL(15,2) DEFAULT 0 COMMENT '当前已募资金额（元）',
    funding_end_date DATETIME COMMENT '募资结束时间',
    funding_share DECIMAL(5,2) COMMENT '募资份额百分比',
    min_investment DECIMAL(15,2) COMMENT '最小投资金额（元）',
    expected_return DECIMAL(5,2) COMMENT '预期收益率',
    roi DECIMAL(5,2) COMMENT '投资回报率',
    status ENUM('draft', 'published', 'ended', 'archived') DEFAULT 'draft' COMMENT '项目状态：draft(草稿)、published(已发布)、ended(已结束)、archived(已归档)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_drama_funding_info_drama_id (drama_id),
    INDEX idx_drama_funding_info_drama_id (drama_id),
    INDEX idx_drama_funding_info_status (status),
    INDEX idx_drama_funding_info_funding_end_date (funding_end_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短剧募资信息表';

-- 1.4 短剧其他信息表 (drama_additional_info)
CREATE TABLE IF NOT EXISTS drama_additional_info (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '其他信息ID',
    drama_id INT(11) NOT NULL COMMENT '关联的短剧ID',
    risk_management TEXT COMMENT '风险管理措施（绑定到风险提示，查看企业信息）',
    confirmed_resources TEXT COMMENT '已确认资源',
    investment_tiers TEXT COMMENT '投资档位信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_drama_additional_info_drama_id (drama_id),
    INDEX idx_drama_additional_info_drama_id (drama_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短剧其他信息表';

-- ============================================================================
-- 2. 数据迁移：从drama_series迁移数据到新表
-- ============================================================================

-- 2.1 迁移制作团队数据
DELETE FROM drama_production_team;
INSERT INTO drama_production_team (
    drama_id, production_company, co_production_company, executive_producer, 
    co_executive_producer, chief_producer, producer, co_producer, 
    director, scriptwriter, supervisor, coordinator
)
SELECT 
    id, production_company, co_production_company, executive_producer,
    co_executive_producer, chief_producer, producer, co_producer,
    director, scriptwriter, supervisor, coordinator
FROM drama_series
WHERE id IS NOT NULL;

-- 2.2 迁移制作进度数据
DELETE FROM drama_production_schedule;
INSERT INTO drama_production_schedule (
    drama_id, schedule_pre_production, schedule_filming, 
    schedule_post_production, expected_release_date
)
SELECT 
    id, schedule_pre_production, schedule_filming,
    schedule_post_production, expected_release_date
FROM drama_series
WHERE id IS NOT NULL;

-- 2.3 迁移募资信息数据
DELETE FROM drama_funding_info;
INSERT INTO drama_funding_info (
    drama_id, funding_goal, current_funding, funding_end_date,
    funding_share, min_investment, expected_return, roi, status
)
SELECT
    id,
    funding_goal,
    current_funding,
    funding_end_date,
    funding_share,
    min_investment,
    CASE
        WHEN expected_return = '' OR expected_return IS NULL THEN NULL
        WHEN expected_return REGEXP '^[0-9]+\.?[0-9]*$' THEN CAST(expected_return AS DECIMAL(5,2))
        ELSE NULL
    END,
    CASE
        WHEN roi = '' OR roi IS NULL THEN NULL
        WHEN roi REGEXP '^[0-9]+\.?[0-9]*$' THEN CAST(roi AS DECIMAL(5,2))
        ELSE NULL
    END,
    status
FROM drama_series
WHERE id IS NOT NULL;

-- 2.4 迁移其他信息数据
DELETE FROM drama_additional_info;
INSERT INTO drama_additional_info (
    drama_id, risk_management, confirmed_resources, investment_tiers
)
SELECT 
    id, risk_management, confirmed_resources, investment_tiers
FROM drama_series
WHERE id IS NOT NULL;

-- ============================================================================
-- 3. 备份原表并创建新的drama_series表
-- ============================================================================

-- 3.1 备份原表
DROP TABLE IF EXISTS drama_series_full_backup;
CREATE TABLE drama_series_full_backup AS SELECT * FROM drama_series;

-- 3.2 创建新的drama_series表（只保留核心字段）
DROP TABLE IF EXISTS drama_series_new;
CREATE TABLE drama_series_new (
    id INT(11) AUTO_INCREMENT PRIMARY KEY COMMENT '短剧唯一标识ID',
    title VARCHAR(100) NOT NULL COMMENT '短剧标题/名称',
    cover TEXT NOT NULL COMMENT '短剧封面图片URL',
    tags TEXT COMMENT '短剧标签，多个标签用逗号分隔，需要从标签配置中选择',
    description TEXT NOT NULL COMMENT '短剧简介描述',
    episodes INT(11) DEFAULT 12 COMMENT '总集数',
    episode_length INT(11) DEFAULT 5 COMMENT '单集时长（分钟）',
    target_platform VARCHAR(255) COMMENT '目标播放平台，需要从平台配置中选择',
    projected_views VARCHAR(50) COMMENT '预计播放量',
    cast TEXT COMMENT '演员阵容，多个演员用逗号分隔',
    is_online TINYINT(1) DEFAULT 1 COMMENT '是否上线显示：1(上线)、0(下线)',
    creator_id INT(11) COMMENT '创建者用户ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_drama_series_is_online (is_online),
    INDEX idx_drama_series_creator_id (creator_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短剧基础信息表';

-- 3.3 迁移核心数据到新表
INSERT INTO drama_series_new (
    id, title, cover, tags, description, episodes, episode_length,
    target_platform, projected_views, cast, is_online, creator_id, created_at, updated_at
)
SELECT 
    id, title, cover, tags, description, episodes, episode_length,
    target_platform, projected_views, cast, is_online, creator_id, created_at, updated_at
FROM drama_series;

-- 3.4 替换原表
DROP TABLE drama_series;
RENAME TABLE drama_series_new TO drama_series;

-- ============================================================================
-- 4. 验证拆分结果
-- ============================================================================

-- 查看各表的数据统计
SELECT 'drama_series' as table_name, COUNT(*) as record_count FROM drama_series
UNION ALL
SELECT 'drama_production_team' as table_name, COUNT(*) as record_count FROM drama_production_team
UNION ALL
SELECT 'drama_production_schedule' as table_name, COUNT(*) as record_count FROM drama_production_schedule
UNION ALL
SELECT 'drama_funding_info' as table_name, COUNT(*) as record_count FROM drama_funding_info
UNION ALL
SELECT 'drama_additional_info' as table_name, COUNT(*) as record_count FROM drama_additional_info
UNION ALL
SELECT 'drama_investment_tiers' as table_name, COUNT(*) as record_count FROM drama_investment_tiers;

-- 查看新drama_series表结构
DESCRIBE drama_series;

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

COMMIT;
