import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, r as readBody, K as validationErrorResponse, L as findUserByCredentials, P as useResponseError, y as hashPassword, q as query, l as logger, h as logAuditAction, N as extractAuditInfo, V as generateAccessToken, W as generateRefreshToken, M as useResponseSuccess, O as serverErrorResponse } from '../../../_/nitro.mjs';
import { s as setRefreshTokenCookie } from '../../../_/cookie-utils.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const register_post = defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { username, email, phone, password, real_name, user_type } = body;
    if (!username || !password || !email && !phone) {
      return validationErrorResponse(
        event,
        "\u7528\u6237\u540D\u3001\u5BC6\u7801\u548C\u8054\u7CFB\u65B9\u5F0F\uFF08\u90AE\u7BB1\u6216\u624B\u673A\u53F7\uFF09\u4E0D\u80FD\u4E3A\u7A7A",
        {
          username: !username ? "\u7528\u6237\u540D\u4E0D\u80FD\u4E3A\u7A7A" : null,
          password: !password ? "\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A" : null,
          contact: !email && !phone ? "\u8BF7\u63D0\u4F9B\u90AE\u7BB1\u6216\u624B\u673A\u53F7" : null
        }
      );
    }
    if (password.length < 6) {
      return validationErrorResponse(event, "\u5BC6\u7801\u957F\u5EA6\u4E0D\u80FD\u5C11\u4E8E6\u4F4D");
    }
    const validUserTypes = ["investor", "producer", "fund_manager"];
    const userType = user_type || "investor";
    if (!validUserTypes.includes(userType)) {
      return validationErrorResponse(event, "\u65E0\u6548\u7684\u7528\u6237\u7C7B\u578B");
    }
    if (email) {
      const emailUser = await findUserByCredentials(email);
      if (emailUser) {
        return useResponseError("\u90AE\u7BB1\u5DF2\u88AB\u6CE8\u518C");
      }
    }
    if (phone) {
      const phoneUser = await findUserByCredentials(phone);
      if (phoneUser) {
        return useResponseError("\u624B\u673A\u53F7\u5DF2\u88AB\u6CE8\u518C");
      }
    }
    const hashedPassword = await hashPassword(password);
    const result = await query(
      `INSERT INTO users (username, email, phone, password_hash, real_name, user_type, status, created_at, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, 1, NOW(), NOW())`,
      [username, email || null, phone || null, hashedPassword, real_name || null, userType]
    );
    const userId = result.insertId;
    try {
      await query(
        `INSERT INTO user_assets (
          user_id, shells_balance, diamonds_balance,
          total_invested_shells, total_earned_diamonds,
          frozen_shells, frozen_diamonds, created_at, updated_at
        ) VALUES (?, 0, 0, 0, 0, 0, 0, NOW(), NOW())`,
        [userId]
      );
      logger.info("\u4E3A\u65B0\u7528\u6237\u521B\u5EFA\u8D44\u4EA7\u8BB0\u5F55\u6210\u529F", { userId, username });
    } catch (assetError) {
      logger.error("\u521B\u5EFA\u7528\u6237\u8D44\u4EA7\u8BB0\u5F55\u5931\u8D25", { userId, username, error: assetError });
    }
    await logAuditAction({
      userId,
      username,
      userType: "user",
      action: "USER_REGISTER",
      description: "\u7528\u6237\u6CE8\u518C\u6210\u529F",
      ...extractAuditInfo(event)
    });
    logger.info("\u7528\u6237\u6CE8\u518C\u6210\u529F", { userId, username });
    const user = {
      id: userId,
      username,
      email,
      phone,
      real_name,
      user_type: userType,
      status: 1,
      created_at: (/* @__PURE__ */ new Date()).toISOString(),
      updated_at: (/* @__PURE__ */ new Date()).toISOString()
    };
    const accessToken = generateAccessToken(user);
    const refreshToken = generateRefreshToken(user);
    setRefreshTokenCookie(event, refreshToken);
    return useResponseSuccess({
      token: accessToken,
      user: {
        ...user,
        accessToken,
        realName: real_name || username,
        roles: ["user"],
        homePath: "/workspace"
      }
    }, "\u6CE8\u518C\u6210\u529F");
  } catch (error) {
    logger.error("\u6CE8\u518C\u8FC7\u7A0B\u4E2D\u53D1\u751F\u9519\u8BEF", { error: (error == null ? void 0 : error.message) || error });
    await logAuditAction({
      action: "REGISTER_ERROR",
      description: `\u6CE8\u518C\u7CFB\u7EDF\u9519\u8BEF: ${(error == null ? void 0 : error.message) || error}`,
      ...extractAuditInfo(event)
    });
    return serverErrorResponse(event, "\u6CE8\u518C\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5");
  }
});

export { register_post as default };
//# sourceMappingURL=register.post.mjs.map
