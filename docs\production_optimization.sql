-- 生产环境数据库优化脚本
-- 用途：优化索引、性能配置、安全设置
-- 执行时机：在数据导入和清理后执行

USE mengtu;

-- 1. 添加缺失的索引（提高查询性能）

-- 用户表索引优化
ALTER TABLE users ADD INDEX idx_email_active (email, is_active);
ALTER TABLE users ADD INDEX idx_phone_active (phone, is_active);
ALTER TABLE users ADD INDEX idx_created_at (created_at);
ALTER TABLE users ADD INDEX idx_last_login (last_login_at);

-- 用户资产表索引
ALTER TABLE user_assets ADD INDEX idx_user_balance (user_id, available_balance);
ALTER TABLE user_assets ADD INDEX idx_updated_at (updated_at);

-- 用户投资记录索引
ALTER TABLE user_investments ADD INDEX idx_user_status (user_id, status);
ALTER TABLE user_investments ADD INDEX idx_fund_status (fund_id, status);
ALTER TABLE user_investments ADD INDEX idx_created_status (created_at, status);

-- 短剧系列索引
ALTER TABLE drama_series ADD INDEX idx_status_created (status, created_at);
ALTER TABLE drama_series ADD INDEX idx_funding_status (funding_status);
ALTER TABLE drama_series ADD INDEX idx_title_status (title, status);

-- 基金表索引
ALTER TABLE funds ADD INDEX idx_status_created (status, created_at);
ALTER TABLE funds ADD INDEX idx_fund_type_status (fund_type, status);
ALTER TABLE funds ADD INDEX idx_target_amount (target_amount);

-- 新闻表索引
ALTER TABLE news ADD INDEX idx_status_published (status, published_at);
ALTER TABLE news ADD INDEX idx_category_status (category_id, status);
ALTER TABLE news ADD INDEX idx_author_status (author_id, status);

-- 演员表索引
ALTER TABLE actors ADD INDEX idx_name_active (name, is_active);
ALTER TABLE actors ADD INDEX idx_role_active (role_type, is_active);

-- 审计日志索引
ALTER TABLE audit_logs ADD INDEX idx_user_action (user_id, action);
ALTER TABLE audit_logs ADD INDEX idx_created_at (created_at);
ALTER TABLE audit_logs ADD INDEX idx_table_action (table_name, action);

-- 2. 创建复合索引（针对常用查询）

-- 用户投资统计查询
ALTER TABLE user_investments ADD INDEX idx_user_status_amount (user_id, status, investment_amount);

-- 短剧投资统计查询  
ALTER TABLE user_investments ADD INDEX idx_fund_status_amount (fund_id, status, investment_amount);

-- 新闻阅读统计查询
ALTER TABLE news_read_logs ADD INDEX idx_news_user_date (news_id, user_id, read_at);

-- 3. 优化表结构（如果需要）

-- 确保所有表使用InnoDB引擎
ALTER TABLE users ENGINE=InnoDB;
ALTER TABLE user_assets ENGINE=InnoDB;
ALTER TABLE user_investments ENGINE=InnoDB;
ALTER TABLE drama_series ENGINE=InnoDB;
ALTER TABLE funds ENGINE=InnoDB;
ALTER TABLE news ENGINE=InnoDB;
ALTER TABLE actors ENGINE=InnoDB;

-- 4. 设置表的字符集（确保一致性）
ALTER TABLE users CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE user_assets CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE user_investments CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE drama_series CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE funds CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE news CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE actors CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 5. 创建视图（简化常用查询）

-- 用户投资统计视图
CREATE OR REPLACE VIEW user_investment_summary AS
SELECT 
    u.id as user_id,
    u.username,
    u.email,
    COUNT(ui.id) as total_investments,
    COALESCE(SUM(ui.investment_amount), 0) as total_invested,
    COALESCE(SUM(CASE WHEN ui.status = 'completed' THEN ui.investment_amount ELSE 0 END), 0) as completed_investments
FROM users u
LEFT JOIN user_investments ui ON u.id = ui.user_id
WHERE u.is_active = 1
GROUP BY u.id, u.username, u.email;

-- 基金募资统计视图
CREATE OR REPLACE VIEW fund_raising_summary AS
SELECT 
    f.id as fund_id,
    f.fund_name,
    f.target_amount,
    COUNT(ui.id) as investor_count,
    COALESCE(SUM(ui.investment_amount), 0) as raised_amount,
    ROUND((COALESCE(SUM(ui.investment_amount), 0) / f.target_amount) * 100, 2) as completion_rate
FROM funds f
LEFT JOIN user_investments ui ON f.id = ui.fund_id AND ui.status = 'completed'
WHERE f.status = 'active'
GROUP BY f.id, f.fund_name, f.target_amount;

-- 6. 创建存储过程（常用业务逻辑）

DELIMITER //

-- 用户投资存储过程
CREATE PROCEDURE ProcessUserInvestment(
    IN p_user_id INT,
    IN p_fund_id INT,
    IN p_amount DECIMAL(15,2)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 检查用户余额
    IF (SELECT available_balance FROM user_assets WHERE user_id = p_user_id) >= p_amount THEN
        -- 扣除用户余额
        UPDATE user_assets 
        SET available_balance = available_balance - p_amount,
            frozen_balance = frozen_balance + p_amount
        WHERE user_id = p_user_id;
        
        -- 创建投资记录
        INSERT INTO user_investments (user_id, fund_id, investment_amount, status, created_at)
        VALUES (p_user_id, p_fund_id, p_amount, 'pending', NOW());
        
        COMMIT;
    ELSE
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '余额不足';
    END IF;
END //

DELIMITER ;

-- 7. 性能监控查询（用于日常监控）

-- 创建性能监控表
CREATE TABLE IF NOT EXISTS performance_monitor (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,2),
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_metric_date (metric_name, recorded_at)
);

-- 8. 数据完整性检查

-- 检查孤立记录
SELECT 'user_assets without users' as check_name, COUNT(*) as count
FROM user_assets ua LEFT JOIN users u ON ua.user_id = u.id WHERE u.id IS NULL
UNION ALL
SELECT 'user_investments without users', COUNT(*)
FROM user_investments ui LEFT JOIN users u ON ui.user_id = u.id WHERE u.id IS NULL
UNION ALL
SELECT 'user_investments without funds', COUNT(*)
FROM user_investments ui LEFT JOIN funds f ON ui.fund_id = f.id WHERE f.id IS NULL;

COMMIT;
