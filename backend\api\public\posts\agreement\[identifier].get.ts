import { query } from '~/utils/database';
import { logger, getClientIP } from '~/utils/logger';

/**
 * 根据标识符获取协议内容接口（公共接口）
 * GET /api/public/posts/agreement/[identifier]
 */
export default defineEventHandler(async (event) => {
  try {
    // 获取路径参数
    const identifier = getRouterParam(event, 'identifier');
    
    if (!identifier) {
      throw createError({
        statusCode: 400,
        statusMessage: '缺少协议标识符'
      });
    }

    // 验证标识符是否有效
    const validIdentifiers = ['user-agreement', 'privacy-policy', 'recharge-agreement', 'crowdfunding-agreement'];
    if (!validIdentifiers.includes(identifier)) {
      throw createError({
        statusCode: 400,
        statusMessage: '无效的协议标识符'
      });
    }

    // 从数据库获取协议内容
    const result = await query(`
      SELECT
        id,
        title,
        content,
        slug as identifier,
        status,
        created_at,
        updated_at
      FROM posts
      WHERE slug = ?
        AND status = 'published'
        AND is_online = 1
      ORDER BY updated_at DESC
      LIMIT 1
    `, [identifier]);

    if (result.length === 0) {
      // 如果没有找到协议，返回默认内容
      const defaultContent = getDefaultAgreementContent(identifier);
      return {
        success: true,
        data: defaultContent
      };
    }

    const agreement = result[0];

    return {
      success: true,
      data: {
        id: agreement.id,
        title: agreement.title,
        content: agreement.content,
        identifier: agreement.identifier,
        status: agreement.status,
        created_at: agreement.created_at,
        updated_at: agreement.updated_at
      }
    };

  } catch (error: any) {
    logger.error('获取协议内容失败', {
      error: error.message,
      identifier: getRouterParam(event, 'identifier'),
      ip: getClientIP(event)
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});

/**
 * 获取默认协议内容
 */
function getDefaultAgreementContent(identifier: string) {
  const now = new Date().toISOString();
  
  if (identifier === 'user-agreement') {
    return {
      id: 0,
      title: '用户协议',
      content: `# 用户协议

## 1. 服务条款
欢迎使用剧投投平台。在使用我们的服务之前，请仔细阅读本用户协议。

## 2. 用户权利与义务
2.1 用户有权使用平台提供的各项服务
2.2 用户应当遵守相关法律法规
2.3 用户应当保护个人账户安全

## 3. 平台权利与义务
3.1 平台有权对用户行为进行监督
3.2 平台有义务保护用户隐私
3.3 平台有权对违规用户进行处理

## 4. 免责声明
平台对因不可抗力导致的服务中断不承担责任。

## 5. 协议修改
平台有权根据需要修改本协议，修改后的协议将在平台上公布。

本协议自用户注册之日起生效。`,
      identifier: 'user-agreement',
      status: 'published',
      created_at: now,
      updated_at: now
    };
  } else if (identifier === 'privacy-policy') {
    return {
      id: 0,
      title: '隐私政策',
      content: `# 隐私政策

## 1. 信息收集
我们会收集您在使用服务过程中提供的信息。

## 2. 信息使用
2.1 为您提供服务
2.2 改善用户体验
2.3 发送重要通知

## 3. 信息保护
3.1 我们采用行业标准的安全措施保护您的信息
3.2 未经您同意，我们不会向第三方披露您的个人信息
3.3 我们会定期审查信息安全措施

## 4. Cookie使用
我们使用Cookie来改善用户体验和分析网站使用情况。

## 5. 第三方服务
我们可能使用第三方服务来提供某些功能，这些服务有自己的隐私政策。

## 6. 政策更新
我们可能会不时更新本隐私政策，更新后的政策将在网站上发布。

如有疑问，请联系我们。`,
      identifier: 'privacy-policy',
      status: 'published',
      created_at: now,
      updated_at: now
    };
  } else if (identifier === 'recharge-agreement') {
    return {
      id: 0,
      title: '充值协议',
      content: `# 剧投投平台充值服务协议

## 第一条 协议概述

### 1.1 协议主体
本协议由大连幻象文化娱乐有限公司（以下简称"平台方"或"我们"）与使用剧投投平台充值服务的用户（以下简称"用户"或"您"）共同签署。

### 1.2 协议性质
本协议是《剧投投平台用户服务协议》的重要组成部分，与平台其他相关协议共同构成完整的法律文件。用户在进行充值操作前，应当仔细阅读并完全理解本协议的全部内容。

### 1.3 协议生效
用户点击"确认充值"按钮即表示您已阅读、理解并同意接受本协议的全部条款约束。

## 第二条 服务定义

### 2.1 充值服务
充值服务是指用户通过平台指定的支付渠道，向其平台账户充入虚拟货币"贝壳"的服务。贝壳是平台内的虚拟货币单位，仅可用于平台内的短剧投资等相关服务。

### 2.2 服务范围
- 贝壳充值服务
- 充值记录查询服务
- 充值状态跟踪服务
- 客户服务支持

## 第三条 充值规则

### 3.1 充值金额限制
- 单次最低充值金额：100贝壳
- 单次最高充值金额：1,000,000贝壳
- 单日累计充值限额：根据用户等级和风控要求动态调整
- 平台保留根据监管要求和风险控制需要调整充值限额的权利

### 3.2 充值方式
- 线下转账充值（需联系客服确认）
- 其他平台认可的合法支付方式
- 具体支付方式以平台实际提供为准

### 3.3 充值流程
1. 用户选择充值金额
2. 选择支付方式
3. 完成支付操作
4. 平台确认到账
5. 贝壳余额更新

## 第四条 用户权利与义务

### 4.1 用户权利
- 查询充值记录和账户余额
- 获得充值相关的客户服务
- 在符合平台规则的前提下使用贝壳进行投资
- 对充值异常情况要求平台协助处理

### 4.2 用户义务
- 确保充值资金来源合法
- 提供真实有效的身份信息
- 妥善保管账户信息和支付密码
- 遵守平台相关规则和法律法规

## 第五条 平台权利与义务

### 5.1 平台权利
- 对用户充值行为进行合规性审查
- 根据风控需要暂停或限制充值服务
- 对违规用户采取相应处理措施
- 根据业务需要调整充值规则

### 5.2 平台义务
- 保障充值系统的安全稳定运行
- 及时处理用户充值相关问题
- 保护用户资金和信息安全
- 提供必要的客户服务支持

## 第六条 风险提示

### 6.1 投资风险
贝壳用于短剧投资存在市场风险，投资收益不确定，用户应理性投资，量力而行。

### 6.2 技术风险
因网络故障、系统维护等技术原因可能导致充值延迟或失败，平台将尽力协助处理。

### 6.3 政策风险
因法律法规变化或监管要求，平台可能调整或暂停充值服务。

## 第七条 争议解决

### 7.1 协商解决
双方应本着友好协商的原则解决争议。

### 7.2 法律适用
本协议适用中华人民共和国法律。

### 7.3 管辖法院
因本协议产生的争议，由平台所在地人民法院管辖。

## 第八条 其他条款

### 8.1 协议修改
平台有权根据业务发展需要修改本协议，修改后的协议将在平台公布。

### 8.2 协议解释
本协议的解释权归平台所有。

### 8.3 协议生效
本协议自用户同意之日起生效。

---

**重要提示：请您在充值前仔细阅读并充分理解本协议内容，特别是涉及免除或限制责任的条款。如有疑问，请联系客服咨询。**`,
      identifier: 'recharge-agreement',
      status: 'published',
      created_at: now,
      updated_at: now
    };
  } else if (identifier === 'crowdfunding-agreement') {
    return {
      id: 0,
      title: '众筹协议',
      content: `# 剧投投平台众筹服务协议

## 第一条 协议概述

### 1.1 协议主体
本协议由大连幻象文化娱乐有限公司（以下简称"平台方"或"我们"）与参与剧投投平台众筹服务的用户（以下简称"用户"或"您"）共同签署。

### 1.2 协议性质
本协议是《剧投投平台用户服务协议》的重要组成部分，与平台其他相关协议共同构成完整的法律文件。用户在参与众筹前，应当仔细阅读并完全理解本协议的全部内容。

### 1.3 协议生效
用户点击"参与众筹"按钮即表示您已阅读、理解并同意接受本协议的全部条款约束。

## 第二条 服务定义

### 2.1 众筹服务
众筹服务是指用户通过平台向短剧项目投资"贝壳"，参与短剧制作投资并获得相应收益分配的服务。

### 2.2 服务范围
- 短剧项目众筹投资服务
- 投资记录查询服务
- 收益分配服务
- 项目进度跟踪服务
- 客户服务支持

## 第三条 众筹规则

### 3.1 投资金额限制
- 单个项目最低投资金额：根据项目设定
- 单个项目最高投资金额：根据项目设定
- 单日累计投资限额：根据用户等级和风控要求动态调整
- 平台保留根据监管要求和风险控制需要调整投资限额的权利

### 3.2 投资方式
- 使用账户内贝壳余额进行投资
- 投资金额必须为整数贝壳
- 投资成功后不可撤销

### 3.3 众筹流程
1. 用户选择短剧项目
2. 选择投资金额
3. 确认投资信息
4. 扣除账户贝壳余额
5. 获得项目投资份额

## 第四条 投资风险提示

### 4.1 市场风险
- 短剧市场存在不确定性，投资收益可能低于预期
- 市场环境变化可能影响项目收益
- 观众喜好变化可能影响短剧表现

### 4.2 制作风险
- 短剧制作过程中可能遇到技术、人员、资金等问题
- 制作周期可能延长，影响收益时间
- 制作质量可能不达预期，影响市场表现

### 4.3 政策风险
- 相关法律法规变化可能影响项目进行
- 监管政策调整可能影响收益分配
- 平台政策变化可能影响服务提供

### 4.4 流动性风险
- 投资资金在项目周期内无法提前退出
- 收益分配时间取决于项目进度和市场表现
- 投资份额不可转让

## 第五条 用户权利与义务

### 5.1 用户权利
- 查询投资记录和项目进度
- 获得投资相关的客户服务
- 按投资比例获得项目收益分配
- 对项目异常情况要求平台协助处理
- 获得项目相关信息披露

### 5.2 用户义务
- 确保投资资金来源合法
- 理性投资，量力而行
- 承担投资风险，不得要求保本保收益
- 遵守平台相关规则和法律法规
- 不得利用投资进行违法违规活动

## 第六条 平台权利与义务

### 6.1 平台权利
- 对用户投资行为进行合规性审查
- 根据风控需要暂停或限制投资服务
- 对违规用户采取相应处理措施
- 根据业务需要调整众筹规则
- 选择合作的短剧项目和制作方

### 6.2 平台义务
- 保障众筹系统的安全稳定运行
- 及时处理用户投资相关问题
- 保护用户资金和信息安全
- 提供必要的客户服务支持
- 监督项目方按约定进行制作和分配

## 第七条 收益分配

### 7.1 分配原则
- 按投资比例进行收益分配
- 扣除平台服务费后进行分配
- 分配时间根据项目实际收益情况确定

### 7.2 分配方式
- 收益以贝壳形式分配到用户账户
- 分配记录可在投资记录中查询
- 分配过程透明公开

### 7.3 税务处理
- 用户应自行承担相关税务义务
- 平台可协助提供必要的税务凭证

## 第八条 争议解决

### 8.1 协商解决
双方应本着友好协商的原则解决争议。

### 8.2 法律适用
本协议适用中华人民共和国法律。

### 8.3 管辖法院
因本协议产生的争议，由平台所在地人民法院管辖。

## 第九条 其他条款

### 9.1 协议修改
平台有权根据业务发展需要修改本协议，修改后的协议将在平台公布。

### 9.2 协议解释
本协议的解释权归平台所有。

### 9.3 协议生效
本协议自用户同意之日起生效。

---

**重要风险提示：**
1. **投资有风险，参与需谨慎**：短剧投资存在市场风险，可能面临本金损失。
2. **收益不确定**：投资收益取决于短剧市场表现，平台不承诺保本保收益。
3. **资金锁定**：投资资金在项目周期内无法提前退出，请确保资金安排合理。
4. **信息披露**：请仔细阅读项目信息，理性判断投资价值。

**请您在投资前充分了解项目情况和投资风险，确保自己具备相应的风险承受能力。如有疑问，请联系客服咨询。**`,
      identifier: 'crowdfunding-agreement',
      status: 'published',
      created_at: now,
      updated_at: now
    };
  }

  return null;
}
