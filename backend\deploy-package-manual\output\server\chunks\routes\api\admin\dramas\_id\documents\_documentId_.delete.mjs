import { c as defineEvent<PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _documentId__delete = defineEventHandler(async (event) => {
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const dramaId = getRouterParam(event, "id");
    const documentId = getRouterParam(event, "documentId");
    if (!dramaId || isNaN(Number(dramaId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u77ED\u5267ID"
      });
    }
    if (!documentId || isNaN(Number(documentId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u6587\u6863ID"
      });
    }
    const existingDocument = await query(
      "SELECT id, drama_id, name, file_url FROM drama_documents WHERE id = ? AND drama_id = ?",
      [documentId, dramaId]
    );
    if (existingDocument.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u77ED\u5267\u6587\u6863\u4E0D\u5B58\u5728"
      });
    }
    await query(
      "DELETE FROM drama_documents WHERE id = ? AND drama_id = ?",
      [documentId, dramaId]
    );
    await logAuditAction({
      action: "ADMIN_DELETE_DRAMA_DOCUMENT",
      description: `\u7BA1\u7406\u5458\u5220\u9664\u77ED\u5267\u6587\u6863: \u77ED\u5267ID=${dramaId}, \u6587\u6863ID=${documentId}`,
      userId: adminPayload.id,
      username: adminPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      metadata: {
        dramaId: Number(dramaId),
        documentId: Number(documentId),
        deletedName: existingDocument[0].name,
        deletedFileUrl: existingDocument[0].file_url
      }
    });
    logger.info("\u77ED\u5267\u6587\u6863\u5220\u9664\u6210\u529F", {
      adminId: adminPayload.id,
      adminUsername: adminPayload.username,
      dramaId: Number(dramaId),
      documentId: Number(documentId),
      deletedName: existingDocument[0].name
    });
    return {
      success: true,
      message: "\u77ED\u5267\u6587\u6863\u5220\u9664\u6210\u529F",
      data: {
        id: Number(documentId),
        dramaId: Number(dramaId)
      }
    };
  } catch (error) {
    logger.error("\u5220\u9664\u77ED\u5267\u6587\u6863\u5931\u8D25", {
      error: error.message,
      dramaId: getRouterParam(event, "id"),
      documentId: getRouterParam(event, "documentId")
    });
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "\u5220\u9664\u77ED\u5267\u6587\u6863\u5931\u8D25"
    });
  }
});

export { _documentId__delete as default };
//# sourceMappingURL=_documentId_.delete.mjs.map
