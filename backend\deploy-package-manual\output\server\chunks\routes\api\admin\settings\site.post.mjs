import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, r as readBody, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const site_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u4FEE\u6539\u7CFB\u7EDF\u8BBE\u7F6E"
      });
    }
    const body = await readBody(event);
    const { title, description, logo, favicon, icp, copyright } = body;
    if (!title) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u7F51\u7AD9\u6807\u9898\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    if (!description) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u7F51\u7AD9\u63CF\u8FF0\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const siteSettings = {
      title: title.trim(),
      description: description.trim(),
      logo: logo || "/images/logo.png",
      favicon: favicon || "/favicon.ico",
      icp: icp || "",
      copyright: copyright || ""
    };
    const settingsJson = JSON.stringify(siteSettings);
    const existingSettings = await query(
      "SELECT id FROM system_settings WHERE setting_key = ?",
      ["site_settings"]
    );
    if (existingSettings.length > 0) {
      await query(
        "UPDATE system_settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?",
        [settingsJson, "site_settings"]
      );
    } else {
      await query(
        "INSERT INTO system_settings (setting_key, setting_value, created_at, updated_at) VALUES (?, ?, NOW(), NOW())",
        ["site_settings", settingsJson]
      );
    }
    await logAuditAction({
      action: "ADMIN_UPDATE_SITE_SETTINGS",
      description: "\u7BA1\u7406\u5458\u66F4\u65B0\u7F51\u7AD9\u8BBE\u7F6E",
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        updatedSettings: siteSettings
      }
    });
    logger.info("\u7BA1\u7406\u5458\u66F4\u65B0\u7F51\u7AD9\u8BBE\u7F6E\u6210\u529F", {
      adminId: admin.id,
      adminUsername: admin.username,
      settings: siteSettings
    });
    return {
      success: true,
      message: "\u7F51\u7AD9\u8BBE\u7F6E\u66F4\u65B0\u6210\u529F",
      data: siteSettings
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u7F51\u7AD9\u8BBE\u7F6E\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { site_post as default };
//# sourceMappingURL=site.post.mjs.map
