import { c as defineEvent<PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, r as readBody, q as query, k as getPool, l as logger, e as getClientIP } from '../../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const permissions_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(adminPayload.id, PERMISSIONS.ROLE_MANAGE);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u5206\u914D\u89D2\u8272\u6743\u9650"
      });
    }
    const roleId = getRouterParam(event, "id");
    if (!roleId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u89D2\u8272ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const body = await readBody(event);
    const { permissions = [] } = body;
    const existingRole = await query(`
      SELECT id, name, code FROM admin_roles WHERE id = ?
    `, [roleId]);
    if (existingRole.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u89D2\u8272\u4E0D\u5B58\u5728"
      });
    }
    const role = existingRole[0];
    if (permissions.length > 0) {
      const validPermissions = await query(`
        SELECT code FROM admin_permissions WHERE code IN (${permissions.map(() => "?").join(",")})
      `, permissions);
      const validCodes = validPermissions.map((p) => p.code);
      const invalidCodes = permissions.filter((code) => !validCodes.includes(code));
      if (invalidCodes.length > 0) {
        throw createError({
          statusCode: 400,
          statusMessage: `\u65E0\u6548\u7684\u6743\u9650\u4EE3\u7801: ${invalidCodes.join(", ")}`
        });
      }
    }
    const connection = await getPool().getConnection();
    await connection.beginTransaction();
    try {
      await connection.execute(`
        DELETE FROM admin_role_permissions WHERE role_id = ?
      `, [roleId]);
      if (permissions.length > 0) {
        const permissionValues = permissions.map((permissionCode) => [roleId, permissionCode]);
        await connection.query(`
          INSERT INTO admin_role_permissions (role_id, permission_code, created_at)
          VALUES ?
        `, [permissionValues]);
      }
      await connection.commit();
      logger.info("\u5206\u914D\u89D2\u8272\u6743\u9650\u6210\u529F", {
        adminId: adminPayload.id,
        roleId,
        roleName: role.name,
        roleCode: role.code,
        permissionCount: permissions.length,
        permissions,
        ip: getClientIP(event)
      });
      return {
        success: true,
        data: {
          roleId: parseInt(roleId),
          permissions
        }
      };
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    logger.error("\u5206\u914D\u89D2\u8272\u6743\u9650\u5931\u8D25", {
      error: error.message,
      roleId: getRouterParam(event, "id"),
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { permissions_post as default };
//# sourceMappingURL=permissions.post.mjs.map
