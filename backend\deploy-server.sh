#!/bin/bash

echo "========================================"
echo "剧投投后端服务部署脚本"
echo "========================================"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用root用户运行此脚本"
    exit 1
fi

# 停止现有服务
echo ""
echo "[1/8] 停止现有服务..."
pm2 stop fundAdmin-backend 2>/dev/null || true
pm2 delete fundAdmin-backend 2>/dev/null || true

# 备份现有部署
echo ""
echo "[2/8] 备份现有部署..."
cd /www/wwwroot
if [ -d "api.qinghee.com.cn" ]; then
    mv api.qinghee.com.cn api.qinghee.com.cn.backup.$(date +%Y%m%d_%H%M%S)
fi

# 创建新部署目录
echo ""
echo "[3/8] 创建新部署目录..."
mkdir -p api.qinghee.com.cn
cd api.qinghee.com.cn

# 检查部署包
echo ""
echo "[4/8] 检查部署包..."
if [ ! -f "mengtu-backend-deploy.tar.gz" ]; then
    echo "错误: 找不到部署包 mengtu-backend-deploy.tar.gz"
    echo "请先上传部署包到 /www/wwwroot/api.qinghee.com.cn/ 目录"
    exit 1
fi

# 解压部署包
echo ""
echo "[5/8] 解压部署包..."
tar -xzf mengtu-backend-deploy.tar.gz

# 设置权限
echo ""
echo "[6/8] 设置权限..."
chown -R www:www /www/wwwroot/api.qinghee.com.cn
chmod -R 755 /www/wwwroot/api.qinghee.com.cn

# 安装依赖
echo ""
echo "[7/8] 安装生产依赖..."
pnpm install --prod

# 启动服务
echo ""
echo "[8/8] 启动服务..."
pm2 start ecosystem.config.js

# 等待启动
echo ""
echo "等待服务启动..."
sleep 10

# 检查服务状态
echo ""
echo "检查服务状态..."
pm2 status

# 测试API
echo ""
echo "测试API..."
curl -s https://api.qinghee.com.cn/api/health || curl -s http://localhost:3001/api/health

echo ""
echo "========================================"
echo "部署完成！"
echo "========================================"
echo ""
echo "服务状态: pm2 status"
echo "查看日志: pm2 logs fundAdmin-backend"
echo "重启服务: pm2 restart fundAdmin-backend"
echo ""
