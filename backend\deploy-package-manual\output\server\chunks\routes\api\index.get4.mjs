import { c as define<PERSON>vent<PERSON><PERSON><PERSON>, g as getQuery, q as query, l as logger, e as getClientIP, f as createError } from '../../_/nitro.mjs';
import { v as validatePagination } from '../../_/validators.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const queryParams = getQuery(event);
    const { page, pageSize, offset } = validatePagination(queryParams.page, queryParams.pageSize);
    const {
      type,
      risk,
      minInvestment,
      period,
      search
    } = queryParams;
    let whereClause = "WHERE is_published = 1";
    const params = [];
    if (type) {
      whereClause += " AND type = ?";
      params.push(type);
    }
    if (risk) {
      whereClause += " AND risk = ?";
      params.push(risk);
    }
    if (minInvestment) {
      whereClause += " AND min_investment >= ?";
      params.push(parseInt(minInvestment));
    }
    if (period) {
      whereClause += " AND period = ?";
      params.push(period);
    }
    if (search) {
      whereClause += " AND (title LIKE ? OR code LIKE ?)";
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern);
    }
    const countResult = await query(
      `SELECT COUNT(*) as total FROM funds ${whereClause}`,
      params
    );
    const total = ((_a = countResult[0]) == null ? void 0 : _a.total) || 0;
    const funds = await query(
      `SELECT code, title, description, type, risk, min_investment, period,
              target_size, raised_amount, expected_return, min_holding_period,
              risk_description, created_at
       FROM funds 
       ${whereClause}
       ORDER BY created_at DESC
       LIMIT ? OFFSET ?`,
      [...params, pageSize, offset]
    );
    const formattedFunds = funds.map((fund) => ({
      id: fund.code,
      title: fund.title,
      description: fund.description,
      type: fund.type,
      risk: fund.risk,
      minInvestment: fund.min_investment,
      period: fund.period,
      targetSize: fund.target_size,
      raisedAmount: fund.raised_amount,
      expectedReturn: fund.expected_return,
      minHoldingPeriod: fund.min_holding_period,
      riskDescription: fund.risk_description,
      createdAt: fund.created_at
    }));
    return {
      success: true,
      data: {
        list: formattedFunds,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize)
        }
      }
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u516C\u5F00\u57FA\u91D1\u5217\u8868\u5931\u8D25", {
      error: error.message,
      ip: getClientIP(event)
    });
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { index_get as default };
//# sourceMappingURL=index.get4.mjs.map
