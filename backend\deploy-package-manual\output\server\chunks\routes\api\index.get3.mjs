import { c as defineEvent<PERSON>and<PERSON>, g as getQuery, q as query, e as getClientIP, f as createError } from '../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const queryParams = getQuery(event);
    const page = parseInt(queryParams.page) || 1;
    const pageSize = Math.min(parseInt(queryParams.pageSize) || 10, 100);
    const offset = (page - 1) * pageSize;
    const {
      status,
      // 不设置默认值，允许获取所有已发布的短剧
      search
    } = queryParams;
    let whereClause = "WHERE ds.is_online = 1";
    const params = [];
    if (status === "funding") {
      whereClause += " AND dfi.current_funding < dfi.funding_goal AND DATEDIFF(dfi.funding_end_date, NOW()) > 0";
    } else if (status === "completed") {
      whereClause += " AND dfi.current_funding >= dfi.funding_goal";
    } else if (status === "expired") {
      whereClause += " AND DATEDIFF(dfi.funding_end_date, NOW()) <= 0";
    }
    if (search) {
      whereClause += " AND (ds.title LIKE ? OR dpt.director LIKE ? OR dpt.producer LIKE ?)";
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    const countResult = await query(
      `SELECT COUNT(DISTINCT ds.id) as total
       FROM drama_series ds
       LEFT JOIN drama_funding_info dfi ON ds.id = dfi.drama_id
       LEFT JOIN drama_production_team dpt ON ds.id = dpt.drama_id
       ${whereClause}`,
      params
    );
    const total = ((_a = countResult[0]) == null ? void 0 : _a.total) || 0;
    const dramas = await query(
      `SELECT
         ds.id, ds.title, ds.cover, ds.tags, ds.description, ds.episodes, ds.episode_length,
         ds.target_platform, ds.projected_views, ds.cast, ds.created_at,
         dpt.director, dpt.producer,
         dfi.funding_goal, dfi.current_funding, dfi.funding_end_date, dfi.expected_return, dfi.min_investment,
         CASE
           WHEN dfi.funding_end_date IS NULL THEN 0
           ELSE GREATEST(0, DATEDIFF(dfi.funding_end_date, NOW()))
         END as remaining_days
       FROM drama_series ds
       LEFT JOIN drama_funding_info dfi ON ds.id = dfi.drama_id
       LEFT JOIN drama_production_team dpt ON ds.id = dpt.drama_id
       ${whereClause}
       ORDER BY ds.created_at DESC
       LIMIT ? OFFSET ?`,
      [...params, pageSize, offset]
    );
    const formattedDramas = dramas.map((drama) => ({
      id: drama.id,
      title: drama.title,
      cover: drama.cover,
      tags: drama.tags ? JSON.parse(drama.tags) : [],
      fundingGoal: parseFloat(drama.funding_goal) || 0,
      currentFunding: parseFloat(drama.current_funding) || 0,
      remainingDays: parseInt(drama.remaining_days) || 0,
      description: drama.description,
      director: drama.director,
      producer: drama.producer,
      cast: drama.cast ? JSON.parse(drama.cast) : [],
      episodes: parseInt(drama.episodes) || 0,
      episodeLength: parseInt(drama.episode_length) || 0,
      targetPlatform: drama.target_platform ? JSON.parse(drama.target_platform) : [],
      expectedReleaseDate: drama.expected_release_date,
      expectedReturn: drama.expected_return,
      minInvestment: parseFloat(drama.min_investment) || 0,
      createdAt: drama.created_at,
      // 计算募资进度
      fundingProgress: drama.funding_goal > 0 ? Math.round(parseFloat(drama.current_funding) / parseFloat(drama.funding_goal) * 100) : 0
    }));
    return {
      success: true,
      data: {
        list: formattedDramas,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize)
        }
      }
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u516C\u5F00\u77ED\u5267\u5217\u8868\u5931\u8D25", {
      error: error.message,
      ip: getClientIP(event) || "unknown"
    });
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { index_get as default };
//# sourceMappingURL=index.get3.mjs.map
