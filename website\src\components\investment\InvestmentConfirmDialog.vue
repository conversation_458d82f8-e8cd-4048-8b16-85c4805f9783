<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import axios from 'axios'
import Swal from 'sweetalert2'
import { createAuthConfig, handleAuthError, requireLogin } from '../../utils/authUtils'
import { useRouter } from 'vue-router'
import { getContactSettings, type ContactSettings } from '../../services/contactService'

interface Project {
  id: number
  title: string
  expectedReturn: number
  minInvestment: number
  maxInvestment?: number
}

const props = defineProps<{
  visible: boolean
  project: Project | null
  amount: number
}>()

const emit = defineEmits<{
  close: []
  success: [data: any]
}>()

const router = useRouter()

// 用户钱包余额
const walletBalance = ref<number>(0)
// 加载状态
const loading = ref<boolean>(false)
const investing = ref<boolean>(false)
// 投资成功弹窗状态
const showSuccessDialog = ref<boolean>(false)
const investmentResult = ref<any>(null)
// 联系方式设置
const contactSettings = ref<ContactSettings>({
  onlineQrCode: '',
  paymentQrCode: '',
  contactText: '',
  contactAddress: '',
  contactEmail: '',
  contactPhone: ''
})

// 计算预期收益
const expectedReturn = computed(() => {
  if (!props.project || !props.amount) return 0
  return Math.round(props.amount * (props.project.expectedReturn / 100))
})

// 格式化金额
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('zh-CN').format(amount)
}

// 获取联系方式设置
const loadContactSettings = async () => {
  try {
    const settings = await getContactSettings()
    if (settings) {
      contactSettings.value = settings
    }
  } catch (error) {
    console.error('获取联系方式设置失败:', error)
  }
}

// 获取用户钱包余额
const fetchWalletBalance = async () => {
  try {
    loading.value = true

    // 检查登录状态
    if (!requireLogin(router)) {
      return
    }

    const response = await axios.get('/users/wallet/balance', createAuthConfig())

    if (response.data.success) {
      walletBalance.value = response.data.data.availableShells
    }
  } catch (error) {
    console.error('获取钱包余额失败:', error)
    handleAuthError(error, router)
  } finally {
    loading.value = false
  }
}

// 检查余额是否足够
const isBalanceSufficient = computed(() => {
  return walletBalance.value >= props.amount
})

// 关闭弹窗
const closeDialog = () => {
  emit('close')
}

// 关闭投资成功弹窗
const closeSuccessDialog = () => {
  showSuccessDialog.value = false
  investmentResult.value = null
  closeDialog()
}

// 复制众筹编号
const copyInvestmentNo = async (investmentNo: string) => {
  try {
    await navigator.clipboard.writeText(investmentNo)
    Swal.fire({
      title: '复制成功',
      text: '众筹编号已复制到剪贴板',
      icon: 'success',
      timer: 2000,
      showConfirmButton: false
    })
  } catch (error) {
    console.error('复制失败:', error)
    // 降级方案：使用传统方法复制
    const textArea = document.createElement('textarea')
    textArea.value = investmentNo
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      Swal.fire({
        title: '复制成功',
        text: '众筹编号已复制到剪贴板',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
      })
    } catch (fallbackError) {
      Swal.fire({
        title: '复制失败',
        text: '请手动复制众筹编号',
        icon: 'error'
      })
    }
    document.body.removeChild(textArea)
  }
}

// 确认投资
const confirmInvestment = async () => {
  if (!props.project || !props.amount) return

  if (!isBalanceSufficient.value) {
    Swal.fire({
      title: '余额不足',
      text: `您的可用余额为 ${formatCurrency(walletBalance.value)} 贝壳，无法完成此次投资`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: '去充值',
      cancelButtonText: '取消'
    }).then((result) => {
      if (result.isConfirmed) {
        // 这里可以跳转到充值页面或打开充值弹窗
        closeDialog()
        // 可以emit一个充值事件
      }
    })
    return
  }

  try {
    investing.value = true

    // 检查登录状态
    if (!requireLogin(router)) {
      return
    }

    const response = await axios.post('/users/investments/create', {
      projectId: props.project.id,
      amount: props.amount
    }, createAuthConfig())

    if (response.data.success) {
      // 保存投资结果数据
      investmentResult.value = response.data.data
      // 显示自定义投资成功弹窗
      showSuccessDialog.value = true

      emit('success', response.data.data)
    } else {
      throw new Error(response.data.message || '投资失败')
    }
  } catch (error: any) {
    console.error('投资失败:', error)

    // 处理认证错误
    if (error.response?.status === 401) {
      handleAuthError(error, router)
      return
    }

    let errorMessage = '投资失败，请稍后再试'
    if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }

    Swal.fire({
      title: '投资失败',
      text: errorMessage,
      icon: 'error'
    })
  } finally {
    investing.value = false
  }
}

// 监听弹窗显示状态，获取余额和联系方式设置
onMounted(() => {
  if (props.visible) {
    fetchWalletBalance()
  }
  loadContactSettings()
})

// 监听visible变化
const handleVisibleChange = () => {
  if (props.visible) {
    fetchWalletBalance()
  }
}

// 使用watch监听visible变化
import { watch } from 'vue'
watch(() => props.visible, handleVisibleChange)
</script>

<template>
  <!-- 投资确认弹窗 -->
  <div v-if="visible" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
      <!-- 弹窗标题 -->
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-lg font-semibold text-gray-900">确认众筹</h3>
        <button @click="closeDialog" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- 投资信息 -->
      <div v-if="project" class="mb-6">
        <div class="bg-gray-50 rounded-lg p-4 space-y-3">
          <div class="flex justify-between">
            <span class="text-gray-600">项目名称：</span>
            <span class="font-medium">{{ project.title }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">投资金额：</span>
            <span class="font-medium text-blue-600">{{ formatCurrency(amount) }} 贝壳</span>
          </div>

        </div>
      </div>

      <!-- 钱包余额信息 -->
      <div class="mb-6">
        <div class="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
          <span class="text-gray-600">可用余额：</span>
          <div class="text-right">
            <span v-if="loading" class="text-gray-500">加载中...</span>
            <span v-else class="font-medium" :class="isBalanceSufficient ? 'text-green-600' : 'text-red-600'">
              {{ formatCurrency(walletBalance) }} 贝壳
            </span>
          </div>
        </div>
        
        <!-- 余额不足提示 -->
        <div v-if="!loading && !isBalanceSufficient" class="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
          <div class="flex items-center">
            <svg class="w-5 h-5 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span class="text-red-700 text-sm">
              余额不足，还需要 {{ formatCurrency(amount - walletBalance) }} 贝壳
            </span>
          </div>
        </div>
      </div>

      <!-- 风险提示 -->
      <div class="mb-6 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div class="flex items-start">
          <svg class="w-5 h-5 text-yellow-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <div class="text-yellow-700 text-sm">
            <p class="font-medium mb-1">投资风险提示</p>
            <p>投资有风险，收益不保证。请根据自身风险承受能力谨慎投资。</p>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex space-x-3">
        <button
          @click="closeDialog"
          class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          :disabled="investing"
        >
          取消
        </button>
        <button
          @click="confirmInvestment"
          :disabled="investing || loading || !isBalanceSufficient"
          class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          <span v-if="investing" class="flex items-center justify-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            众筹中...
          </span>
          <span v-else>确认众筹</span>
        </button>
      </div>
    </div>
  </div>

  <!-- 投资成功弹窗 -->
  <div v-if="showSuccessDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
    <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
      <!-- 弹窗标题 -->
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-lg font-semibold text-gray-900">参与众筹成功</h3>
        <button @click="closeSuccessDialog" class="text-gray-400 hover:text-gray-600">
          <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- 投资信息 -->
      <div v-if="investmentResult && project" class="mb-6">
        <div class="bg-gray-50 rounded-lg p-4 space-y-3">
          <div class="flex justify-between">
            <span class="text-gray-600">项目名称：</span>
            <span class="font-medium">{{ project.title }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">投资金额：</span>
            <span class="font-medium text-blue-600">{{ formatCurrency(investmentResult.amount || amount) }} 贝壳</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-gray-600">众筹编号：</span>
            <div class="flex items-center space-x-2">
              <span class="font-medium">{{ investmentResult.investmentNo }}</span>
              <button
                @click="copyInvestmentNo(investmentResult.investmentNo)"
                class="text-blue-600 hover:text-blue-800 p-1 rounded hover:bg-blue-50"
                title="复制众筹编号"
              >
                <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 二维码显示区域 -->
      <div v-if="contactSettings.paymentQrCode" class="mb-6 text-center">
        <h4 class="text-sm font-medium text-gray-700 mb-3">客服联系方式</h4>
        <div class="flex justify-center">
          <img
            :src="contactSettings.paymentQrCode"
            alt="客服联系二维码"
            class="w-40 h-40 object-contain border border-gray-200 rounded"
            @error="$event.target.style.display='none'"
          >
        </div>
      </div>

      <!-- 重要提示 -->
      <div class="mb-6">
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="flex items-start">
            <svg class="w-5 h-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div class="text-red-700 text-sm">
              <p class="font-medium mb-1">重要提示：</p>
              <p>请尽快复制众筹编号，添加客服联系方式，完成和承制商的合同签署。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 确认按钮 -->
      <div class="flex justify-center">
        <button
          @click="closeSuccessDialog"
          class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          我知道了
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 确保弹窗在最顶层 */
.z-50 {
  z-index: 50;
}

/* 投资成功弹窗层级更高 */
.z-60 {
  z-index: 60 !important;
}

/* 复制按钮悬停效果 */
.copy-button:hover {
  background-color: #f3f4f6;
}
</style>
