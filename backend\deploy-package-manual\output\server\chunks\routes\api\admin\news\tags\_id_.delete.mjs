import { c as defineEvent<PERSON>and<PERSON>, j as getRouter<PERSON>aram, f as createError, q as query, m as transaction, o as logAdminAction, l as logger, e as getClientIP } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__delete = defineEventHandler(async (event) => {
  var _a, _b;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const tagId = getRouterParam(event, "id");
    if (!tagId || isNaN(parseInt(tagId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u6807\u7B7EID"
      });
    }
    const tagIdNum = parseInt(tagId);
    const existingTag = await query(
      "SELECT id, name FROM news_tags WHERE id = ?",
      [tagIdNum]
    );
    if (!existingTag || existingTag.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u6807\u7B7E\u4E0D\u5B58\u5728"
      });
    }
    const tag = existingTag[0];
    const usageCheck = await query(
      "SELECT COUNT(*) as count FROM news_tag_relations WHERE tag_id = ?",
      [tagIdNum]
    );
    const usageCount = ((_a = usageCheck[0]) == null ? void 0 : _a.count) || 0;
    await transaction(async (connection) => {
      if (usageCount > 0) {
        await connection.execute(
          "DELETE FROM news_tag_relations WHERE tag_id = ?",
          [tagIdNum]
        );
      }
      await connection.execute(
        "DELETE FROM news_tags WHERE id = ?",
        [tagIdNum]
      );
    });
    await logAdminAction(admin.id, "news:tags:delete", "\u5220\u9664\u65B0\u95FB\u6807\u7B7E", {
      tagId: tagIdNum,
      tagName: tag.name,
      usageCount
    });
    return {
      success: true,
      message: `\u6807\u7B7E"${tag.name}"\u5220\u9664\u6210\u529F${usageCount > 0 ? `\uFF0C\u540C\u65F6\u79FB\u9664\u4E86${usageCount}\u4E2A\u5173\u8054\u5173\u7CFB` : ""}`
    };
  } catch (error) {
    logger.error("\u5220\u9664\u6807\u7B7E\u5931\u8D25", {
      error: error.message,
      stack: error.stack,
      adminId: (_b = event.context.admin) == null ? void 0 : _b.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u5220\u9664\u6807\u7B7E\u5931\u8D25"
    });
  }
});

export { _id__delete as default };
//# sourceMappingURL=_id_.delete.mjs.map
