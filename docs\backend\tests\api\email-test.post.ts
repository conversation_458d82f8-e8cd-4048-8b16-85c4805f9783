/**
 * 测试邮件设置接口
 * POST /api/admin/settings/email/test
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权访问'
      });
    }

    // 检查权限
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: '权限不足，无法测试邮件设置'
      });
    }

    // 获取请求体
    const body = await readBody(event);
    const { to, subject, text } = body;

    // 验证必要字段
    if (!to || !subject || !text) {
      throw createError({
        statusCode: 400,
        statusMessage: '请提供收件人、主题和内容'
      });
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(to)) {
      throw createError({
        statusCode: 400,
        statusMessage: '请提供有效的邮箱地址'
      });
    }

    // 获取邮件设置
    const result = await query(
      'SELECT setting_value FROM system_settings WHERE setting_key = ?',
      ['email_settings']
    );

    if (result.length === 0 || !result[0].setting_value) {
      throw createError({
        statusCode: 400,
        statusMessage: '邮件设置未配置，请先配置邮件设置'
      });
    }

    let emailSettings;
    try {
      emailSettings = JSON.parse(result[0].setting_value);
    } catch (error) {
      throw createError({
        statusCode: 400,
        statusMessage: '邮件设置格式错误'
      });
    }

    // 验证邮件设置完整性
    if (!emailSettings.host || !emailSettings.user || !emailSettings.password) {
      throw createError({
        statusCode: 400,
        statusMessage: '邮件设置不完整，请检查SMTP服务器、用户名和密码'
      });
    }

    // 这里应该实际发送邮件，但为了演示，我们只是模拟成功
    // 在实际项目中，你需要使用nodemailer或其他邮件库
    
    // 模拟发送延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 记录审计日志
    await logAuditAction({
      action: 'ADMIN_TEST_EMAIL_SETTINGS',
      description: '管理员测试邮件设置',
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, 'user-agent') || '',
      details: { to, subject }
    });

    return {
      success: true,
      message: '测试邮件发送成功'
    };

  } catch (error: any) {
    logger.error('测试邮件设置失败', {
      error: error.message,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});
