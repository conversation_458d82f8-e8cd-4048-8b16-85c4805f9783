{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "strict": true, "noEmit": true, "isolatedModules": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "resolveJsonModule": true, "declaration": false, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": false, "noImplicitOverride": true, "types": ["nitropack", "node"], "typeRoots": ["./node_modules/@types"], "baseUrl": ".", "paths": {"~/*": ["./*"], "~~/*": ["./*"], "@@/*": ["./*"], "#types": ["./types/index.ts"]}}, "include": ["**/*.ts", "**/*.js", "**/*.vue", "**/*.json", "types/**/*.d.ts", "utils/**/*.ts", "middleware/**/*.ts", "api/**/*.ts"], "exclude": ["node_modules", ".nitro", "dist", "*.config.ts"]}