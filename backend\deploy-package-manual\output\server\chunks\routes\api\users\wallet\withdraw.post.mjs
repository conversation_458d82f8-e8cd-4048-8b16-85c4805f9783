import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, Z as verifyUserAccessToken, f as createError, r as readBody, U as findUserById, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const withdraw_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const userPayload = verifyUserAccessToken(event);
    if (!userPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u7528\u6237\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"
      });
    }
    const body = await readBody(event);
    const { amount, bankAccount, password } = body;
    if (!amount || amount <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u63D0\u73B0\u91D1\u989D\u5FC5\u987B\u5927\u4E8E0"
      });
    }
    if (!bankAccount) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u8BF7\u9009\u62E9\u94F6\u884C\u8D26\u6237"
      });
    }
    if (!password) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u8BF7\u8F93\u5165\u4EA4\u6613\u5BC6\u7801"
      });
    }
    const user = await findUserById(userPayload.id);
    if (!user) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u7528\u6237\u4E0D\u5B58\u5728"
      });
    }
    if (user.status !== 1) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u7528\u6237\u8D26\u6237\u5DF2\u88AB\u7981\u7528"
      });
    }
    if (password !== "123456") {
      throw createError({
        statusCode: 400,
        statusMessage: "\u4EA4\u6613\u5BC6\u7801\u9519\u8BEF"
      });
    }
    const mockUserBalance = 18e4;
    if (amount > mockUserBalance) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u4F59\u989D\u4E0D\u8DB3"
      });
    }
    const withdrawId = `WTH${Date.now()}${Math.random().toString(36).substr(2, 6).toUpperCase()}`;
    const now = /* @__PURE__ */ new Date();
    const estimatedArrival = new Date(now.getTime() + 3 * 24 * 60 * 60 * 1e3);
    const estimatedArrivalStr = estimatedArrival.toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit"
    });
    logger.info("\u7528\u6237\u53D1\u8D77\u63D0\u73B0\u7533\u8BF7", {
      userId: user.id,
      withdrawId,
      amount,
      bankAccount,
      ip: getClientIP(event)
    });
    return {
      success: true,
      data: {
        withdrawId,
        estimatedArrival: estimatedArrivalStr
      }
    };
  } catch (error) {
    logger.error("\u63D0\u73B0\u7533\u8BF7\u5931\u8D25", {
      error: error.message,
      userId: (_a = event.context.user) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { withdraw_post as default };
//# sourceMappingURL=withdraw.post.mjs.map
