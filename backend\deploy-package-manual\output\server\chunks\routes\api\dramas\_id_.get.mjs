import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, q as query, e as getClientIP } from '../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

function mergeActorInfo(roleData, actorsMap) {
  if (!roleData) return null;
  try {
    const parsed = JSON.parse(roleData);
    if (Array.isArray(parsed)) {
      const mergedData = parsed.map((item) => {
        if (item && typeof item.id === "number") {
          const actorInfo = actorsMap.get(item.id);
          if (actorInfo) {
            return {
              ...item,
              name: actorInfo.name,
              avatarUrl: actorInfo.avatarUrl
            };
          }
        }
        return item;
      });
      return JSON.stringify(mergedData);
    }
  } catch (e) {
  }
  return roleData;
}
function mergeCompanyInfo(companyData, companiesMap) {
  if (!companyData) return null;
  if (companiesMap.size === 0) {
    return companyData;
  }
  try {
    const parsed = JSON.parse(companyData);
    if (Array.isArray(parsed)) {
      const companyNames = parsed.map((id) => {
        if (typeof id === "number") {
          const companyName = companiesMap.get(id);
          return companyName || `\u672A\u77E5\u516C\u53F8(ID:${id})`;
        }
        return id;
      }).filter(Boolean);
      if (companyNames.length > 0) {
        return companyNames.join(",");
      }
    }
  } catch (e) {
  }
  return companyData;
}
const _id__get = defineEventHandler(async (event) => {
  var _a;
  try {
    const dramaId = getRouterParam(event, "id");
    if (!dramaId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u77ED\u5267ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const id = parseInt(dramaId);
    if (isNaN(id) || id <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u77ED\u5267ID"
      });
    }
    const dramaResult = await query(`
      SELECT
        ds.id, ds.title, ds.cover, ds.tags, ds.description, ds.episodes, ds.episode_length,
        ds.target_platform, ds.projected_views, ds.cast, ds.is_online,
        ds.created_at, ds.updated_at,
        dpt.production_company, dpt.co_production_company, dpt.executive_producer,
        dpt.co_executive_producer, dpt.chief_producer, dpt.producer, dpt.co_producer,
        dpt.director, dpt.scriptwriter, dpt.supervisor, dpt.coordinator,
        dps.schedule_pre_production, dps.schedule_filming, dps.schedule_post_production,
        dps.expected_release_date,
        dfi.funding_goal, dfi.current_funding, dfi.funding_end_date, dfi.expected_return,
        dfi.min_investment, dfi.funding_share, dfi.roi, dfi.status,
        CASE
          WHEN dfi.funding_end_date IS NULL THEN 0
          ELSE GREATEST(0, DATEDIFF(dfi.funding_end_date, NOW()))
        END as remaining_days
      FROM drama_series ds
      LEFT JOIN drama_production_team dpt ON ds.id = dpt.drama_id
      LEFT JOIN drama_production_schedule dps ON ds.id = dps.drama_id
      LEFT JOIN drama_funding_info dfi ON ds.id = dfi.drama_id
      WHERE ds.id = ? AND ds.is_online = 1
    `, [id]);
    if (dramaResult.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u77ED\u5267\u4E0D\u5B58\u5728\u6216\u672A\u4E0A\u7EBF"
      });
    }
    const drama = dramaResult[0];
    const materialsResult = await query(`
      SELECT id, drama_id, title, url, thumbnail, type, sort_order, created_at, updated_at
      FROM drama_materials
      WHERE drama_id = ?
      ORDER BY sort_order ASC, created_at ASC
    `, [id]);
    const documentsResult = await query(`
      SELECT id, drama_id, name, file_url, file_type, file_size, created_at, updated_at
      FROM drama_documents
      WHERE drama_id = ?
      ORDER BY created_at DESC
    `, [id]);
    const additionalInfoResult = await query(`
      SELECT risk_management, confirmed_resources, investment_tiers
      FROM drama_additional_info
      WHERE drama_id = ?
    `, [id]);
    const investmentTiersResult = await query(`
      SELECT id, tier_name, min_amount, max_amount, benefits, return_rate,
             limited_quantity, sold_quantity, sort_order, is_active
      FROM drama_investment_tiers
      WHERE drama_id = ? AND is_active = 1
      ORDER BY sort_order ASC, created_at ASC
    `, [id]);
    let businessLicenseInfo = null;
    try {
      if (drama && drama.production_company) {
        const productionCompanyIds = JSON.parse(drama.production_company);
        if (Array.isArray(productionCompanyIds) && productionCompanyIds.length > 0) {
          const firstCompanyId = productionCompanyIds[0];
          console.log("\u67E5\u8BE2\u8425\u4E1A\u6267\u7167\uFF0C\u516C\u53F8ID:", firstCompanyId);
          const businessLicenseResult = await query(`
            SELECT id, company_name, business_license_photo
            FROM brands
            WHERE id = ?
          `, [firstCompanyId]);
          console.log("\u8425\u4E1A\u6267\u7167\u67E5\u8BE2\u7ED3\u679C:", businessLicenseResult);
          if (businessLicenseResult.length > 0) {
            businessLicenseInfo = {
              companyId: businessLicenseResult[0].id,
              companyName: businessLicenseResult[0].company_name,
              businessLicensePhoto: businessLicenseResult[0].business_license_photo
            };
          }
        }
      }
    } catch (error) {
      console.error("\u67E5\u8BE2\u8425\u4E1A\u6267\u7167\u4FE1\u606F\u5931\u8D25:", error);
    }
    let investorsResult = [];
    let totalInvestors = 0;
    try {
      investorsResult = await query(`
        SELECT
          u.id, u.username, u.real_name,
          ui.investment_amount, ui.investment_date
        FROM user_investments ui
        LEFT JOIN users u ON ui.user_id = u.id
        WHERE ui.project_id = ? AND ui.investment_status = 'active'
        ORDER BY ui.investment_date DESC
        LIMIT 20
      `, [id]);
      const investorCountResult = await query(`
        SELECT COUNT(DISTINCT user_id) as total_investors
        FROM user_investments
        WHERE project_id = ? AND investment_status = 'active'
      `, [id]);
      totalInvestors = ((_a = investorCountResult[0]) == null ? void 0 : _a.total_investors) || 0;
    } catch (error) {
      console.error("\u6295\u8D44\u4EBA\u4FE1\u606F\u67E5\u8BE2\u5931\u8D25:", error);
      investorsResult = [];
      totalInvestors = 0;
    }
    const actorIds = /* @__PURE__ */ new Set();
    const roleFields = [
      "executive_producer",
      "co_executive_producer",
      "chief_producer",
      "producer",
      "co_producer",
      "director",
      "scriptwriter",
      "supervisor",
      "coordinator"
    ];
    roleFields.forEach((field) => {
      const fieldValue = drama[field];
      if (fieldValue) {
        try {
          const parsed = JSON.parse(fieldValue);
          if (Array.isArray(parsed)) {
            parsed.forEach((item) => {
              if (item && typeof item.id === "number") {
                actorIds.add(item.id);
              }
            });
          }
        } catch (e) {
        }
      }
    });
    let actorsMap = /* @__PURE__ */ new Map();
    if (actorIds.size > 0) {
      const actorIdsArray = Array.from(actorIds);
      const placeholders = actorIdsArray.map(() => "?").join(",");
      const actorsResult = await query(`
        SELECT id, name, avatar_url
        FROM actors
        WHERE id IN (${placeholders})
      `, actorIdsArray);
      actorsResult.forEach((actor) => {
        actorsMap.set(actor.id, {
          id: actor.id,
          name: actor.name,
          avatarUrl: actor.avatar_url
        });
      });
    }
    const companyIds = /* @__PURE__ */ new Set();
    const companyFields = ["production_company", "co_production_company"];
    companyFields.forEach((field) => {
      const fieldValue = drama[field];
      if (fieldValue) {
        try {
          const parsed = JSON.parse(fieldValue);
          if (Array.isArray(parsed)) {
            parsed.forEach((id2) => {
              if (typeof id2 === "number") {
                companyIds.add(id2);
              }
            });
          }
        } catch (e) {
        }
      }
    });
    let companiesMap = /* @__PURE__ */ new Map();
    if (companyIds.size > 0) {
      const companyIdsArray = Array.from(companyIds);
      const placeholders = companyIdsArray.map(() => "?").join(",");
      const companiesResult = await query(`
        SELECT id, company_name
        FROM brands
        WHERE id IN (${placeholders})
      `, companyIdsArray);
      companiesResult.forEach((company) => {
        companiesMap.set(company.id, company.company_name);
      });
    }
    const formattedDrama = {
      id: drama.id,
      title: drama.title,
      cover: drama.cover,
      tags: drama.tags ? JSON.parse(drama.tags) : [],
      fundingGoal: parseFloat(drama.funding_goal) || 0,
      currentFunding: parseFloat(drama.current_funding) || 0,
      remainingDays: parseInt(drama.remaining_days) || 0,
      description: drama.description,
      isOnline: drama.is_online,
      director: drama.director,
      scriptwriter: drama.scriptwriter,
      producer: drama.producer,
      cast: drama.cast ? JSON.parse(drama.cast) : [],
      episodes: parseInt(drama.episodes) || 0,
      episodeLength: parseInt(drama.episode_length) || 0,
      targetPlatform: drama.target_platform ? JSON.parse(drama.target_platform) : [],
      expectedReleaseDate: drama.expected_release_date,
      expectedReturn: parseFloat(drama.expected_return) || 0,
      minInvestment: parseFloat(drama.min_investment) || 0,
      fundingShare: parseFloat(drama.funding_share) || 100,
      projectedViews: drama.projected_views,
      roi: parseFloat(drama.roi) || 0,
      schedulePreProduction: drama.schedule_pre_production,
      scheduleFilming: drama.schedule_filming,
      schedulePostProduction: drama.schedule_post_production,
      scheduleRelease: drama.expected_release_date,
      // 制作团队信息（合并演员和公司详细信息）
      productionTeam: {
        productionCompany: mergeCompanyInfo(drama.production_company, companiesMap),
        coProductionCompany: mergeCompanyInfo(drama.co_production_company, companiesMap),
        executiveProducer: mergeActorInfo(drama.executive_producer, actorsMap),
        coExecutiveProducer: mergeActorInfo(drama.co_executive_producer, actorsMap),
        chiefProducer: mergeActorInfo(drama.chief_producer, actorsMap),
        producer: mergeActorInfo(drama.producer, actorsMap),
        coProducer: mergeActorInfo(drama.co_producer, actorsMap),
        director: mergeActorInfo(drama.director, actorsMap),
        scriptwriter: mergeActorInfo(drama.scriptwriter, actorsMap),
        supervisor: mergeActorInfo(drama.supervisor, actorsMap),
        coordinator: mergeActorInfo(drama.coordinator, actorsMap)
      },
      // 同时提供嵌套的schedule对象供前端使用
      schedule: {
        preProduction: drama.schedule_pre_production,
        filming: drama.schedule_filming,
        postProduction: drama.schedule_post_production,
        release: drama.expected_release_date
      },
      // 从additional_info表获取的数据
      confirmedResources: additionalInfoResult.length > 0 && additionalInfoResult[0].confirmed_resources ? JSON.parse(additionalInfoResult[0].confirmed_resources) : [],
      riskManagement: additionalInfoResult.length > 0 && additionalInfoResult[0].risk_management ? JSON.parse(additionalInfoResult[0].risk_management) : [],
      // 投资权益档位信息
      investmentTiers: investmentTiersResult.map((tier) => ({
        id: tier.id,
        tierName: tier.tier_name,
        minAmount: parseFloat(tier.min_amount),
        maxAmount: tier.max_amount ? parseFloat(tier.max_amount) : null,
        benefits: tier.benefits,
        returnRate: tier.return_rate ? parseFloat(tier.return_rate) : null,
        limitedQuantity: tier.limited_quantity,
        soldQuantity: tier.sold_quantity || 0,
        sortOrder: tier.sort_order || 0,
        isActive: tier.is_active === 1
      })),
      // 营业执照信息
      businessLicense: businessLicenseInfo,
      createdAt: drama.created_at,
      updatedAt: drama.updated_at,
      // 计算募资进度
      fundingProgress: drama.funding_goal > 0 ? Math.round(parseFloat(drama.current_funding) / parseFloat(drama.funding_goal) * 100) : 0,
      // 投资人信息
      investors: {
        totalCount: totalInvestors,
        list: investorsResult.map((investor) => ({
          id: investor.id,
          username: investor.username,
          realName: investor.real_name,
          investmentAmount: parseFloat(investor.investment_amount),
          investmentDate: investor.investment_date
        }))
      },
      // 格式化素材列表
      materials: materialsResult.map((material) => ({
        id: material.id,
        dramaId: material.drama_id,
        title: material.title,
        url: material.url,
        thumbnail: material.thumbnail,
        type: material.type,
        sortOrder: material.sort_order,
        createdAt: material.created_at,
        updatedAt: material.updated_at
      })),
      // 格式化文档列表
      documents: documentsResult.map((document) => ({
        id: document.id,
        dramaId: document.drama_id,
        name: document.name,
        fileUrl: document.file_url,
        fileType: document.file_type,
        fileSize: document.file_size,
        createdAt: document.created_at,
        updatedAt: document.updated_at
      }))
    };
    return {
      success: true,
      data: formattedDrama
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u516C\u5F00\u77ED\u5267\u8BE6\u60C5\u5931\u8D25", {
      error: error.message,
      dramaId: getRouterParam(event, "id"),
      ip: getClientIP(event) || "unknown"
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { _id__get as default };
//# sourceMappingURL=_id_.get.mjs.map
