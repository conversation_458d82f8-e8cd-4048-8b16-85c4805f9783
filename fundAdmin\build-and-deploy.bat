@echo off
echo ========================================
echo 剧投投后台管理系统构建和部署脚本
echo ========================================

echo.
echo [1/6] 清理之前的构建...
if exist apps\web-antd\dist rmdir /s /q apps\web-antd\dist
if exist node_modules\.vite rmdir /s /q node_modules\.vite

echo.
echo [2/6] 安装依赖...
call pnpm install

echo.
echo [3/6] 构建生产版本...
call pnpm build:antd

echo.
echo [4/6] 检查构建结果...
if not exist apps\web-antd\dist\index.html (
    echo 错误: 构建失败，找不到 apps\web-antd\dist\index.html
    pause
    exit /b 1
)

echo.
echo [5/6] 创建部署包...
if exist deploy-package rmdir /s /q deploy-package
mkdir deploy-package

echo 复制构建文件...
xcopy apps\web-antd\dist deploy-package\ /E /I /Q

echo 复制Nginx配置...
copy nginx.conf deploy-package\

echo.
echo [6/6] 压缩部署包...
cd deploy-package
tar -czf ..\fundAdmin-deploy.tar.gz *
cd ..

echo.
echo 构建完成！
echo.
echo 部署包已创建: fundAdmin-deploy.tar.gz
echo.
echo 接下来请：
echo 1. 将 fundAdmin-deploy.tar.gz 上传到服务器
echo 2. 在服务器上执行部署命令
echo.
echo 服务器部署命令：
echo cd /www/wwwroot
echo mkdir admin.qinghee.com.cn
echo cd admin.qinghee.com.cn
echo # 上传 fundAdmin-deploy.tar.gz 到此目录
echo tar -xzf fundAdmin-deploy.tar.gz
echo # 配置Nginx虚拟主机指向此目录
echo.
pause
