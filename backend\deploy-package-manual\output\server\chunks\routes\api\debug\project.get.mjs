import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, g as getQuery, q as query } from '../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const project_get = defineEventHandler(async (event) => {
  try {
    const queryParams = getQuery(event);
    const projectId = queryParams.id || 1;
    const projectQuery = `
      SELECT
        ds.id, ds.title,
        dfi.min_investment, dfi.funding_goal,
        dfi.current_funding, dfi.status, dfi.expected_return
      FROM drama_series ds
      LEFT JOIN drama_funding_info dfi ON ds.id = dfi.drama_id
      WHERE ds.id = ?
    `;
    const projectRows = await query(projectQuery, [projectId]);
    if (!projectRows || projectRows.length === 0) {
      return {
        success: false,
        message: "\u9879\u76EE\u4E0D\u5B58\u5728"
      };
    }
    const project = projectRows[0];
    return {
      success: true,
      data: {
        raw: project,
        processed: {
          id: project.id,
          title: project.title,
          minInvestment: parseFloat(project.min_investment) || 5e4,
          fundingGoal: parseFloat(project.funding_goal) || 0,
          currentFunding: parseFloat(project.current_funding) || 0,
          expectedReturn: parseFloat(project.expected_return) || 15,
          status: project.status
        }
      }
    };
  } catch (error) {
    console.error("\u8C03\u8BD5\u9879\u76EE\u6570\u636E\u5931\u8D25:", error);
    return {
      success: false,
      message: "\u8C03\u8BD5\u5931\u8D25",
      error: error.message
    };
  }
});

export { project_get as default };
//# sourceMappingURL=project.get.mjs.map
