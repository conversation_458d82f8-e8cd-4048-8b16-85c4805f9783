import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, f as createError, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__delete = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const brandId = getRouterParam(event, "id");
    if (!brandId || isNaN(Number(brandId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u5382\u724CID"
      });
    }
    const existingBrand = await query(
      "SELECT id, brand_name, company_name FROM brands WHERE id = ?",
      [brandId]
    );
    if (existingBrand.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u5382\u724C\u4E0D\u5B58\u5728"
      });
    }
    const brand = existingBrand[0];
    await query(
      "DELETE FROM brands WHERE id = ?",
      [brandId]
    );
    await logAuditAction({
      action: "ADMIN_DELETE_BRAND",
      description: `\u7BA1\u7406\u5458\u5220\u9664\u5382\u724C: ${brand.brand_name}`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        brandId,
        brandName: brand.brand_name,
        companyName: brand.company_name
      }
    });
    logger.info("\u7BA1\u7406\u5458\u5220\u9664\u5382\u724C\u6210\u529F", {
      adminId: admin.id,
      adminUsername: admin.username,
      brandId,
      brandName: brand.brand_name,
      companyName: brand.company_name
    });
    return {
      success: true,
      message: "\u5382\u724C\u5220\u9664\u6210\u529F",
      data: {
        id: brandId,
        brandName: brand.brand_name,
        companyName: brand.company_name
      }
    };
  } catch (error) {
    logger.error("\u7BA1\u7406\u5458\u5220\u9664\u5382\u724C\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      brandId: getRouterParam(event, "id"),
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u5220\u9664\u5382\u724C\u5931\u8D25"
    });
  }
});

export { _id__delete as default };
//# sourceMappingURL=_id_.delete.mjs.map
