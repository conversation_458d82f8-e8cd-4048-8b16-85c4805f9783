import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, r as readBody, q as query, y as hashPassword, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const body = await readBody(event);
    const {
      username,
      email,
      phone,
      password,
      userType = "investor",
      realName,
      companyName,
      idCard,
      businessLicense,
      status = "active"
    } = body;
    if (!username || !email || !password) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u7528\u6237\u540D\u3001\u90AE\u7BB1\u548C\u5BC6\u7801\u4E3A\u5FC5\u586B\u5B57\u6BB5"
      });
    }
    if (!["investor", "producer", "fund_manager"].includes(userType)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u7528\u6237\u7C7B\u578B"
      });
    }
    const existingUsers = await query(
      "SELECT id FROM users WHERE username = ? OR email = ? OR (phone IS NOT NULL AND phone = ?)",
      [username, email, phone || null]
    );
    if (existingUsers.length > 0) {
      throw createError({
        statusCode: 409,
        statusMessage: "\u7528\u6237\u540D\u3001\u90AE\u7BB1\u6216\u624B\u673A\u53F7\u5DF2\u5B58\u5728"
      });
    }
    const hashedPassword = await hashPassword(password);
    const statusValue = status === "active" ? 1 : 0;
    const result = await query(`
      INSERT INTO users (
        username, email, phone, password_hash, user_type, real_name,
        company_name, id_card, business_license, status, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      username,
      email,
      phone || null,
      hashedPassword,
      userType,
      realName || null,
      companyName || null,
      idCard || null,
      businessLicense || null,
      statusValue
    ]);
    await logAuditAction({
      action: "ADMIN_CREATE_USER",
      description: `\u7BA1\u7406\u5458\u521B\u5EFA\u7528\u6237: ${username} (${userType})`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        newUserId: result.insertId,
        newUsername: username,
        userType,
        realName,
        companyName
      }
    });
    logger.info("\u7BA1\u7406\u5458\u521B\u5EFA\u7528\u6237\u6210\u529F", {
      adminId: admin.id,
      newUserId: result.insertId,
      username,
      userType,
      ip: getClientIP(event)
    });
    return {
      success: true,
      message: "\u7528\u6237\u521B\u5EFA\u6210\u529F",
      data: {
        id: result.insertId,
        username,
        userType
      }
    };
  } catch (error) {
    logger.error("\u521B\u5EFA\u7528\u6237\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    throw error;
  }
});

export { index_post as default };
//# sourceMappingURL=index.post9.mjs.map
