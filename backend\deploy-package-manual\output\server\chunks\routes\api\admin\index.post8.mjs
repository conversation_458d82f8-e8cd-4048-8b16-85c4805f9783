import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, r as readBody, f as createError, q as query, l as logger, e as getClientIP } from '../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const body = await readBody(event);
    const {
      name,
      avatarUrl,
      bio,
      tags,
      roleType = "\u6F14\u5458",
      sortOrder = 0,
      status = "active"
    } = body;
    if (!name) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u827A\u4EBA\u59D3\u540D\u4E3A\u5FC5\u586B\u5B57\u6BB5"
      });
    }
    const validRoleTypes = ["\u51FA\u54C1\u4EBA", "\u8054\u5408\u51FA\u54C1\u4EBA", "\u603B\u5236\u7247\u4EBA", "\u5236\u7247\u4EBA", "\u8054\u5408\u5236\u7247\u4EBA", "\u5BFC\u6F14", "\u76D1\u5236", "\u7F16\u5267", "\u7EDF\u7B79", "\u6F14\u5458"];
    if (!validRoleTypes.includes(roleType)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u8EAB\u4EFD\u7C7B\u578B"
      });
    }
    const existingArtists = await query(
      "SELECT id FROM actors WHERE name = ?",
      [name]
    );
    if (existingArtists.length > 0) {
      throw createError({
        statusCode: 409,
        statusMessage: "\u827A\u4EBA\u59D3\u540D\u5DF2\u5B58\u5728"
      });
    }
    const statusValue = status === "active" ? 1 : 0;
    const result = await query(`
      INSERT INTO actors (
        name, avatar_url, bio, tags, role_type, sort_order, is_active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      name,
      avatarUrl || null,
      bio || null,
      tags || null,
      roleType,
      sortOrder || 0,
      statusValue
    ]);
    logger.info("\u7BA1\u7406\u5458\u521B\u5EFA\u827A\u4EBA\u6210\u529F", {
      adminId: admin.id,
      newArtistId: result.insertId,
      name,
      roleType,
      ip: getClientIP(event)
    });
    return {
      success: true,
      message: "\u827A\u4EBA\u521B\u5EFA\u6210\u529F",
      data: {
        id: result.insertId,
        name,
        roleType
      }
    };
  } catch (error) {
    logger.error("\u521B\u5EFA\u827A\u4EBA\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    throw error;
  }
});

export { index_post as default };
//# sourceMappingURL=index.post8.mjs.map
