import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, q as query, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const site_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u8BBF\u95EE\u7CFB\u7EDF\u8BBE\u7F6E"
      });
    }
    const result = await query(
      "SELECT setting_value FROM system_settings WHERE setting_key = ?",
      ["site_settings"]
    );
    let siteSettings = {};
    if (result.length > 0 && result[0].setting_value) {
      try {
        siteSettings = JSON.parse(result[0].setting_value);
      } catch (error) {
        logger.error("\u89E3\u6790\u7F51\u7AD9\u8BBE\u7F6EJSON\u5931\u8D25", { error: error.message });
      }
    }
    if (Object.keys(siteSettings).length === 0) {
      siteSettings = {
        title: "\u5267\u6295\u6295",
        description: "\u4E2D\u56FD\u6700\u4E13\u4E1A\u7684\u77ED\u5267\u6295\u8D44\u5E73\u53F0",
        logo: "/images/logo.png",
        favicon: "/favicon.ico",
        icp: "\u7CA4ICP\u5907XXXXXXXX\u53F7",
        copyright: "\xA9 2023 \u5267\u6295\u6295. \u4FDD\u7559\u6240\u6709\u6743\u5229"
      };
    }
    return {
      success: true,
      data: siteSettings
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u7F51\u7AD9\u8BBE\u7F6E\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { site_get as default };
//# sourceMappingURL=site.get.mjs.map
