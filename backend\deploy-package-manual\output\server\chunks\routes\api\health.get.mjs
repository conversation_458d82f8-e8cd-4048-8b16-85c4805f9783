import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, q as query, l as logger, X as setResponseStatus, e as getClientIP } from '../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const health_get = defineEventHandler(async (event) => {
  try {
    let dbStatus = "healthy";
    let dbLatency = 0;
    try {
      const startTime = Date.now();
      await query("SELECT 1 as test");
      dbLatency = Date.now() - startTime;
    } catch (dbError) {
      dbStatus = "unhealthy";
      logger.error("\u6570\u636E\u5E93\u5065\u5EB7\u68C0\u67E5\u5931\u8D25", { error: dbError.message });
    }
    const health = {
      status: dbStatus === "healthy" ? "healthy" : "degraded",
      timestamp: (/* @__PURE__ */ new Date()).toISOString(),
      version: "1.0.0",
      environment: "production",
      services: {
        database: {
          status: dbStatus,
          latency: `${dbLatency}ms`
        },
        api: {
          status: "healthy",
          uptime: process.uptime()
        }
      }
    };
    const statusCode = health.status === "healthy" ? 200 : 503;
    setResponseStatus(event, statusCode);
    return {
      success: health.status === "healthy",
      data: health
    };
  } catch (error) {
    logger.error("\u5065\u5EB7\u68C0\u67E5\u5931\u8D25", {
      error: error.message,
      ip: getClientIP(event)
    });
    setResponseStatus(event, 503);
    return {
      success: false,
      data: {
        status: "unhealthy",
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        error: "Health check failed"
      }
    };
  }
});

export { health_get as default };
//# sourceMappingURL=health.get.mjs.map
