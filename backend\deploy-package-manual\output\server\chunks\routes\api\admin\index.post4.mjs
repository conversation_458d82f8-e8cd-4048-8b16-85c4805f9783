import { c as defineEvent<PERSON><PERSON><PERSON>, r as readBody, f as createError, m as transaction, l as logger, e as getClientIP } from '../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_post = defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const {
      // 基础信息
      title,
      cover,
      tags = [],
      description,
      episodes = 12,
      episodeLength = 5,
      targetPlatform = [],
      projectedViews,
      cast = [],
      isOnline = 1,
      // 制作团队信息
      productionTeam = {},
      // 制作进度信息
      productionSchedule = {},
      // 募资信息
      fundingInfo = {},
      // 其他信息
      additionalInfo = {}
    } = body;
    if (!title || !cover || !description) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6807\u9898\u3001\u5C01\u9762\u548C\u63CF\u8FF0\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    if (typeof episodes !== "number" || episodes <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u96C6\u6570\u5FC5\u987B\u4E3A\u6B63\u6574\u6570"
      });
    }
    if (typeof episodeLength !== "number" || episodeLength <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5355\u96C6\u65F6\u957F\u5FC5\u987B\u4E3A\u6B63\u6570"
      });
    }
    if (fundingInfo.fundingGoal !== void 0) {
      if (typeof fundingInfo.fundingGoal !== "number" || fundingInfo.fundingGoal <= 0) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u52DF\u8D44\u76EE\u6807\u5FC5\u987B\u4E3A\u6B63\u6570"
        });
      }
    }
    if (fundingInfo.minInvestment !== void 0) {
      if (typeof fundingInfo.minInvestment !== "number" || fundingInfo.minInvestment <= 0) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u6700\u5C0F\u6295\u8D44\u91D1\u989D\u5FC5\u987B\u4E3A\u6B63\u6570"
        });
      }
    }
    if (fundingInfo.status) {
      const validStatuses = ["draft", "published", "ended", "archived"];
      if (!validStatuses.includes(fundingInfo.status)) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u65E0\u6548\u7684\u72B6\u6001\u503C"
        });
      }
    }
    const result = await transaction(async (connection) => {
      const dramaResult = await connection.execute(`
        INSERT INTO drama_series (
          title, cover, tags, description, episodes, episode_length,
          target_platform, projected_views, cast, is_online, creator_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        title,
        cover,
        JSON.stringify(tags || []),
        description,
        episodes,
        episodeLength,
        JSON.stringify(targetPlatform || []),
        projectedViews || null,
        JSON.stringify(cast || []),
        isOnline,
        null
        // creator_id 暂时为空，后续可以从认证信息中获取
      ]);
      const dramaId = dramaResult[0].insertId;
      if (productionTeam && Object.keys(productionTeam).length > 0) {
        await connection.execute(`
          INSERT INTO drama_production_team (
            drama_id, production_company, co_production_company, executive_producer,
            co_executive_producer, chief_producer, producer, co_producer,
            director, scriptwriter, supervisor, coordinator
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          dramaId,
          productionTeam.productionCompany || null,
          productionTeam.coProductionCompany || null,
          productionTeam.executiveProducer || null,
          productionTeam.coExecutiveProducer || null,
          productionTeam.chiefProducer || null,
          productionTeam.producer || null,
          productionTeam.coProducer || null,
          productionTeam.director || null,
          productionTeam.scriptwriter || null,
          productionTeam.supervisor || null,
          productionTeam.coordinator || null
        ]);
      }
      if (productionSchedule && Object.keys(productionSchedule).length > 0) {
        await connection.execute(`
          INSERT INTO drama_production_schedule (
            drama_id, schedule_pre_production, schedule_filming,
            schedule_post_production, expected_release_date
          ) VALUES (?, ?, ?, ?, ?)
        `, [
          dramaId,
          productionSchedule.preProduction || null,
          productionSchedule.filming || null,
          productionSchedule.postProduction || null,
          productionSchedule.expectedReleaseDate || null
        ]);
      }
      if (fundingInfo && Object.keys(fundingInfo).length > 0) {
        await connection.execute(`
          INSERT INTO drama_funding_info (
            drama_id, funding_goal, current_funding, funding_end_date,
            funding_share, min_investment, expected_return, roi, status
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          dramaId,
          fundingInfo.fundingGoal || null,
          fundingInfo.currentFunding || 0,
          fundingInfo.fundingEndDate || null,
          fundingInfo.fundingShare || null,
          fundingInfo.minInvestment || null,
          fundingInfo.expectedReturn || null,
          fundingInfo.roi || null,
          fundingInfo.status || "draft"
        ]);
      }
      if (additionalInfo && Object.keys(additionalInfo).length > 0) {
        await connection.execute(`
          INSERT INTO drama_additional_info (
            drama_id, risk_management, confirmed_resources, investment_tiers
          ) VALUES (?, ?, ?, ?)
        `, [
          dramaId,
          additionalInfo.riskManagement ? JSON.stringify(additionalInfo.riskManagement) : null,
          additionalInfo.confirmedResources ? JSON.stringify(additionalInfo.confirmedResources) : null,
          additionalInfo.investmentTiers ? JSON.stringify(additionalInfo.investmentTiers) : null
        ]);
      }
      return dramaId;
    });
    logger.info("\u521B\u5EFA\u77ED\u5267\u6210\u529F", {
      // adminId: admin.id,
      dramaId: result,
      title,
      ip: getClientIP(event)
    });
    return {
      success: true,
      data: {
        id: result,
        message: "\u77ED\u5267\u521B\u5EFA\u6210\u529F"
      }
    };
  } catch (error) {
    logger.error("\u521B\u5EFA\u77ED\u5267\u5931\u8D25", {
      error: error.message,
      // adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { index_post as default };
//# sourceMappingURL=index.post4.mjs.map
