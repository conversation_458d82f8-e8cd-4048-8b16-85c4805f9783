-- 修复短剧相关表的字符编码问题
-- 确保所有drama表都使用utf8mb4字符集和utf8mb4_unicode_ci排序规则
-- 执行前请备份数据库！

-- 设置连接字符集
SET NAMES utf8mb4;
SET CHARACTER_SET_CLIENT = utf8mb4;
SET CHARACTER_SET_CONNECTION = utf8mb4;
SET CHARACTER_SET_RESULTS = utf8mb4;

-- 禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- ============================================================================
-- 1. 修复表级别的字符集和排序规则
-- ============================================================================

-- 修复 drama_series 表
ALTER TABLE drama_series CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修复 drama_production_team 表
ALTER TABLE drama_production_team CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修复 drama_production_schedule 表
ALTER TABLE drama_production_schedule CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修复 drama_funding_info 表
ALTER TABLE drama_funding_info CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修复 drama_additional_info 表
ALTER TABLE drama_additional_info CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修复 drama_investment_tiers 表
ALTER TABLE drama_investment_tiers CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修复其他drama相关表
ALTER TABLE drama_tags CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE drama_platforms CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE drama_materials CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- ============================================================================
-- 2. 修复具体字段的字符集（如果表级别修复不够）
-- ============================================================================

-- 修复 drama_series 表的字段
ALTER TABLE drama_series 
MODIFY COLUMN title VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '短剧标题/名称',
MODIFY COLUMN cover TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '短剧封面图片URL',
MODIFY COLUMN tags TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '短剧标签，多个标签用逗号分隔，需要从标签配置中选择',
MODIFY COLUMN description TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '短剧简介描述',
MODIFY COLUMN target_platform VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '目标播放平台，需要从平台配置中选择',
MODIFY COLUMN projected_views VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '预计播放量',
MODIFY COLUMN cast TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '演员阵容，多个演员用逗号分隔';

-- 修复 drama_production_team 表的字段
ALTER TABLE drama_production_team 
MODIFY COLUMN production_company VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '出品公司',
MODIFY COLUMN co_production_company VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '联合出品公司',
MODIFY COLUMN executive_producer VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '出品人',
MODIFY COLUMN co_executive_producer VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '联合出品人',
MODIFY COLUMN chief_producer VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '总制片人',
MODIFY COLUMN producer VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '制片人',
MODIFY COLUMN co_producer VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '联合制片人',
MODIFY COLUMN director VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '导演姓名',
MODIFY COLUMN scriptwriter VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '编剧姓名',
MODIFY COLUMN supervisor VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '监制',
MODIFY COLUMN coordinator VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '统筹';

-- 修复 drama_production_schedule 表的字段
ALTER TABLE drama_production_schedule 
MODIFY COLUMN schedule_pre_production VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '前期制作筹备时间安排，包含起止时间',
MODIFY COLUMN schedule_filming VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '拍摄时间安排，包含起止时间',
MODIFY COLUMN schedule_post_production VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '后期制作时间安排，包含起止时间',
MODIFY COLUMN expected_release_date VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '预期上线日期，包含起止时间';

-- 修复 drama_additional_info 表的字段
ALTER TABLE drama_additional_info 
MODIFY COLUMN risk_management TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '风险管理措施（绑定到风险提示，查看企业信息）',
MODIFY COLUMN confirmed_resources TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '已确认资源',
MODIFY COLUMN investment_tiers TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '投资档位信息';

-- 修复 drama_investment_tiers 表的字段
ALTER TABLE drama_investment_tiers 
MODIFY COLUMN tier_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '档位名称',
MODIFY COLUMN benefits TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '投资权益描述';

-- ============================================================================
-- 3. 检查并修复可能损坏的数据（如果需要）
-- ============================================================================

-- 如果发现数据损坏，可以从备份表重新迁移数据
-- 注意：只有在确认数据损坏时才执行以下操作

-- 检查是否有乱码数据的示例查询
-- SELECT id, title, director, producer FROM drama_production_team WHERE director LIKE '%?%' OR producer LIKE '%?%';

-- 如果需要重新迁移数据，可以执行以下操作：
/*
-- 清空可能损坏的表
TRUNCATE TABLE drama_production_team;
TRUNCATE TABLE drama_production_schedule;
TRUNCATE TABLE drama_funding_info;
TRUNCATE TABLE drama_additional_info;

-- 重新从备份表迁移数据
INSERT INTO drama_production_team (
    drama_id, production_company, co_production_company, executive_producer, 
    co_executive_producer, chief_producer, producer, co_producer, 
    director, scriptwriter, supervisor, coordinator
)
SELECT 
    id, production_company, co_production_company, executive_producer,
    co_executive_producer, chief_producer, producer, co_producer,
    director, scriptwriter, supervisor, coordinator
FROM drama_series_full_backup
WHERE id IS NOT NULL;

INSERT INTO drama_production_schedule (
    drama_id, schedule_pre_production, schedule_filming, 
    schedule_post_production, expected_release_date
)
SELECT 
    id, schedule_pre_production, schedule_filming,
    schedule_post_production, expected_release_date
FROM drama_series_full_backup
WHERE id IS NOT NULL;

INSERT INTO drama_funding_info (
    drama_id, funding_goal, current_funding, funding_end_date, 
    funding_share, min_investment, expected_return, roi, status
)
SELECT 
    id, 
    funding_goal, 
    current_funding, 
    funding_end_date,
    funding_share, 
    min_investment, 
    CASE 
        WHEN expected_return = '' OR expected_return IS NULL THEN NULL 
        WHEN expected_return REGEXP '^[0-9]+\.?[0-9]*$' THEN CAST(expected_return AS DECIMAL(5,2))
        ELSE NULL 
    END,
    CASE 
        WHEN roi = '' OR roi IS NULL THEN NULL 
        WHEN roi REGEXP '^[0-9]+\.?[0-9]*$' THEN CAST(roi AS DECIMAL(5,2))
        ELSE NULL 
    END,
    status
FROM drama_series_full_backup
WHERE id IS NOT NULL;

INSERT INTO drama_additional_info (
    drama_id, risk_management, confirmed_resources, investment_tiers
)
SELECT 
    id, risk_management, confirmed_resources, investment_tiers
FROM drama_series_full_backup
WHERE id IS NOT NULL;
*/

-- ============================================================================
-- 4. 验证修复结果
-- ============================================================================

-- 检查表的字符集设置
SELECT TABLE_NAME, TABLE_COLLATION 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME LIKE 'drama_%' 
ORDER BY TABLE_NAME;

-- 检查字段的字符集设置
SELECT TABLE_NAME, COLUMN_NAME, CHARACTER_SET_NAME, COLLATION_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'mengtu' 
  AND TABLE_NAME LIKE 'drama_%' 
  AND DATA_TYPE IN ('varchar', 'text', 'longtext', 'mediumtext', 'tinytext') 
ORDER BY TABLE_NAME, COLUMN_NAME;

-- 测试中文数据显示
SELECT '测试中文显示' as test_chinese;
SELECT drama_id, production_company, director, producer FROM drama_production_team LIMIT 3;
SELECT id, title FROM drama_series LIMIT 3;

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

COMMIT;
