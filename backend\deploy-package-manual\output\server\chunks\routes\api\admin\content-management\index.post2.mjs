import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, r as readBody, f as createError, q as query } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_post = defineEventHandler(async (event) => {
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const body = await readBody(event);
    const { name, fontColor, backgroundColor } = body;
    if (!name || !fontColor || !backgroundColor) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6807\u7B7E\u540D\u79F0\u3001\u5B57\u4F53\u989C\u8272\u548C\u80CC\u666F\u989C\u8272\u4E3A\u5FC5\u586B\u9879"
      });
    }
    if (name.length > 50) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6807\u7B7E\u540D\u79F0\u4E0D\u80FD\u8D85\u8FC750\u4E2A\u5B57\u7B26"
      });
    }
    const colorRegex = /^#[0-9A-Fa-f]{6}$/;
    if (!colorRegex.test(fontColor)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5B57\u4F53\u989C\u8272\u683C\u5F0F\u4E0D\u6B63\u786E\uFF0C\u8BF7\u4F7F\u7528\u5341\u516D\u8FDB\u5236\u683C\u5F0F\uFF08\u5982\uFF1A#000000\uFF09"
      });
    }
    if (!colorRegex.test(backgroundColor)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u80CC\u666F\u989C\u8272\u683C\u5F0F\u4E0D\u6B63\u786E\uFF0C\u8BF7\u4F7F\u7528\u5341\u516D\u8FDB\u5236\u683C\u5F0F\uFF08\u5982\uFF1A#ffffff\uFF09"
      });
    }
    const existingTag = await query(
      "SELECT id FROM drama_tags WHERE name = ?",
      [name]
    );
    if (existingTag.length > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6807\u7B7E\u540D\u79F0\u5DF2\u5B58\u5728"
      });
    }
    const result = await query(
      `INSERT INTO drama_tags (name, font_color, background_color, operator)
       VALUES (?, ?, ?, ?)`,
      [name, fontColor, backgroundColor, admin.username]
    );
    const tagId = result.insertId;
    console.log("\u7BA1\u7406\u5458\u521B\u5EFA\u6807\u7B7E\u6210\u529F", {
      adminId: admin.id,
      adminUsername: admin.username,
      tagId,
      name,
      fontColor,
      backgroundColor
    });
    return {
      success: true,
      message: "\u6807\u7B7E\u521B\u5EFA\u6210\u529F",
      data: {
        id: tagId,
        name,
        fontColor,
        backgroundColor
      }
    };
  } catch (error) {
    console.error("\u7BA1\u7406\u5458\u521B\u5EFA\u6807\u7B7E\u5931\u8D25", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u521B\u5EFA\u6807\u7B7E\u5931\u8D25"
    });
  }
});

export { index_post as default };
//# sourceMappingURL=index.post2.mjs.map
