import { c as defineEvent<PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, r as readBody, q as query, l as logger, e as getClientIP } from '../../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__put = defineEventHandler(async (event) => {
  var _a;
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(adminPayload.id, PERMISSIONS.SYSTEM_MENU_EDIT);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u66F4\u65B0\u83DC\u5355"
      });
    }
    const menuId = getRouterParam(event, "id");
    if (!menuId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u83DC\u5355ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const body = await readBody(event);
    const {
      name,
      path,
      component,
      type,
      status,
      authCode,
      icon,
      meta,
      sortOrder,
      pid
    } = body;
    if (!name || !type) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u83DC\u5355\u540D\u79F0\u548C\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const validTypes = ["catalog", "menu", "button", "embedded", "link"];
    if (!validTypes.includes(type)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u83DC\u5355\u7C7B\u578B"
      });
    }
    const existingMenu = await query(`
      SELECT id, name FROM admin_menus WHERE id = ?
    `, [menuId]);
    if (existingMenu.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u83DC\u5355\u4E0D\u5B58\u5728"
      });
    }
    const nameCheck = await query(`
      SELECT id FROM admin_menus WHERE name = ? AND id != ?
    `, [name, menuId]);
    if (nameCheck.length > 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u83DC\u5355\u540D\u79F0\u5DF2\u88AB\u5176\u4ED6\u83DC\u5355\u4F7F\u7528"
      });
    }
    if (path) {
      const pathCheck = await query(`
        SELECT id FROM admin_menus WHERE path = ? AND id != ?
      `, [path, menuId]);
      if (pathCheck.length > 0) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u83DC\u5355\u8DEF\u5F84\u5DF2\u88AB\u5176\u4ED6\u83DC\u5355\u4F7F\u7528"
        });
      }
    }
    await query(`
      UPDATE admin_menus 
      SET pid = ?, name = ?, path = ?, component = ?, type = ?, 
          status = ?, auth_code = ?, icon = ?, meta = ?, 
          sort_order = ?, updated_at = NOW()
      WHERE id = ?
    `, [
      pid || null,
      name,
      path || null,
      component || null,
      type,
      status,
      authCode || null,
      icon || null,
      meta ? JSON.stringify(meta) : null,
      sortOrder || 0,
      menuId
    ]);
    logger.info("\u66F4\u65B0\u83DC\u5355\u6210\u529F", {
      adminId: adminPayload.id,
      menuId,
      menuName: name,
      menuType: type,
      ip: getClientIP(event)
    });
    return {
      success: true,
      data: {
        message: "\u83DC\u5355\u66F4\u65B0\u6210\u529F"
      }
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u83DC\u5355\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { _id__put as default };
//# sourceMappingURL=_id_.put.mjs.map
