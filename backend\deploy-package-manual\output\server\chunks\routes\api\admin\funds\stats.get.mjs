import { c as defineEvent<PERSON><PERSON><PERSON>, f as createError, q as query, l as logger, e as getClientIP } from '../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const stats_get = defineEventHandler(async (event) => {
  var _a, _b, _c, _d, _e, _f, _g;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u67E5\u770B\u57FA\u91D1\u7EDF\u8BA1"
      });
    }
    const [
      totalStats,
      publishedStats,
      draftStats,
      financialStats
    ] = await Promise.all([
      // 总基金数
      query("SELECT COUNT(*) as total FROM funds"),
      // 已发布基金数
      query("SELECT COUNT(*) as published FROM funds WHERE is_published = 1"),
      // 草稿基金数
      query("SELECT COUNT(*) as draft FROM funds WHERE is_published = 0"),
      // 财务统计
      query(`
        SELECT 
          SUM(target_size) as totalTargetSize,
          SUM(raised_amount) as totalRaisedAmount,
          AVG(CASE WHEN target_size > 0 THEN (raised_amount / target_size) * 100 ELSE 0 END) as averageProgress
        FROM funds
      `)
    ]);
    const typeStats = await query(`
      SELECT 
        type,
        COUNT(*) as count,
        SUM(target_size) as targetSize,
        SUM(raised_amount) as raisedAmount
      FROM funds 
      GROUP BY type
    `);
    const riskStats = await query(`
      SELECT 
        risk,
        COUNT(*) as count,
        SUM(target_size) as targetSize,
        SUM(raised_amount) as raisedAmount
      FROM funds 
      GROUP BY risk
      ORDER BY risk
    `);
    const periodStats = await query(`
      SELECT 
        period,
        COUNT(*) as count,
        SUM(target_size) as targetSize,
        SUM(raised_amount) as raisedAmount
      FROM funds 
      GROUP BY period
    `);
    const trendStats = await query(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count
      FROM funds 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
      GROUP BY DATE(created_at)
      ORDER BY date
    `);
    const stats = {
      totalFunds: ((_a = totalStats[0]) == null ? void 0 : _a.total) || 0,
      publishedFunds: ((_b = publishedStats[0]) == null ? void 0 : _b.published) || 0,
      draftFunds: ((_c = draftStats[0]) == null ? void 0 : _c.draft) || 0,
      totalTargetSize: ((_d = financialStats[0]) == null ? void 0 : _d.totalTargetSize) || 0,
      totalRaisedAmount: ((_e = financialStats[0]) == null ? void 0 : _e.totalRaisedAmount) || 0,
      averageProgress: Math.round(((_f = financialStats[0]) == null ? void 0 : _f.averageProgress) || 0),
      // 分组统计
      typeDistribution: typeStats.map((item) => ({
        type: item.type,
        count: item.count,
        targetSize: item.targetSize || 0,
        raisedAmount: item.raisedAmount || 0,
        progress: item.targetSize > 0 ? Math.round(item.raisedAmount / item.targetSize * 100) : 0
      })),
      riskDistribution: riskStats.map((item) => ({
        risk: item.risk,
        count: item.count,
        targetSize: item.targetSize || 0,
        raisedAmount: item.raisedAmount || 0,
        progress: item.targetSize > 0 ? Math.round(item.raisedAmount / item.targetSize * 100) : 0
      })),
      periodDistribution: periodStats.map((item) => ({
        period: item.period,
        count: item.count,
        targetSize: item.targetSize || 0,
        raisedAmount: item.raisedAmount || 0,
        progress: item.targetSize > 0 ? Math.round(item.raisedAmount / item.targetSize * 100) : 0
      })),
      // 趋势数据
      creationTrend: trendStats.map((item) => ({
        date: item.date,
        count: item.count
      }))
    };
    return {
      success: true,
      data: stats
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u57FA\u91D1\u7EDF\u8BA1\u5931\u8D25", {
      error: error.message,
      adminId: (_g = event.context.admin) == null ? void 0 : _g.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { stats_get as default };
//# sourceMappingURL=stats.get.mjs.map
