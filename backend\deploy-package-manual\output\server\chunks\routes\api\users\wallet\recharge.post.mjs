import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, Z as verifyUserAccessToken, f as createError, r as readBody, q as query } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const recharge_post = defineEventHandler(async (event) => {
  try {
    const userPayload = verifyUserAccessToken(event);
    if (!userPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u7528\u6237\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"
      });
    }
    const userId = userPayload.id;
    const body = await readBody(event);
    const { amount } = body;
    if (!amount || amount <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5145\u503C\u91D1\u989D\u5FC5\u987B\u5927\u4E8E0"
      });
    }
    let assetRows = await query(`
      SELECT
        shells_balance,
        total_invested_shells
      FROM user_assets
      WHERE user_id = ?
    `, [userId]);
    let currentBalance = 0;
    let currentTotalInvested = 0;
    if (!assetRows || assetRows.length === 0) {
      console.log(`\u7528\u6237 ${userId} \u8D44\u4EA7\u8BB0\u5F55\u4E0D\u5B58\u5728\uFF0C\u521B\u5EFA\u521D\u59CB\u8BB0\u5F55`);
      await query(`
        INSERT INTO user_assets (
          user_id, shells_balance, diamonds_balance,
          total_invested_shells, total_earned_diamonds,
          frozen_shells, frozen_diamonds, created_at, updated_at
        ) VALUES (?, 0, 0, 0, 0, 0, 0, NOW(), NOW())
      `, [userId]);
      currentBalance = 0;
      currentTotalInvested = 0;
    } else {
      const currentAsset = assetRows[0];
      currentBalance = parseFloat(currentAsset.shells_balance);
      currentTotalInvested = parseFloat(currentAsset.total_invested_shells);
    }
    const newBalance = currentBalance + parseFloat(amount);
    const newTotalInvested = currentTotalInvested + parseFloat(amount);
    const transactionNo = `TXN${Date.now()}${Math.random().toString(36).substr(2, 6).toUpperCase()}`;
    try {
      await query(`
        INSERT INTO user_asset_transactions (
          user_id, transaction_type, amount, balance_before, balance_after,
          related_type, description, transaction_no, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `, [
        userId,
        "shells_in",
        amount,
        currentBalance,
        currentBalance,
        // 余额暂时不变，等待管理员确认
        "recharge",
        `\u7EBF\u4E0B\u5145\u503C ${amount} \u8D1D\u58F3`,
        transactionNo,
        "pending"
      ]);
      console.log(`\u521B\u5EFApending\u5145\u503C\u8BA2\u5355: ${transactionNo}, \u91D1\u989D: ${amount}, \u7528\u6237: ${userId}`);
      return {
        success: true,
        data: {
          orderId: transactionNo,
          amount: parseFloat(amount),
          status: "pending",
          message: "\u5145\u503C\u8BA2\u5355\u521B\u5EFA\u6210\u529F\uFF0C\u8BF7\u8054\u7CFB\u5BA2\u670D\u5B8C\u6210\u652F\u4ED8",
          // 客服微信信息
          customerService: {
            wechatQrCode: "https://example.com/customer-service-qr.png",
            // 这里应该是实际的客服微信二维码
            workingHours: "24\u5C0F\u65F6",
            instructions: "\u8BF7\u626B\u63CF\u4E8C\u7EF4\u7801\u6DFB\u52A0\u5BA2\u670D\u5FAE\u4FE1\uFF0C\u5E76\u53D1\u9001\u8BA2\u5355\u53F7\u5B8C\u6210\u652F\u4ED8"
          }
        }
      };
    } catch (error) {
      throw error;
    }
  } catch (error) {
    console.error("\u5145\u503C\u5931\u8D25:", error);
    return {
      success: false,
      message: "\u5145\u503C\u5931\u8D25",
      error: error.message
    };
  }
});

export { recharge_post as default };
//# sourceMappingURL=recharge.post.mjs.map
