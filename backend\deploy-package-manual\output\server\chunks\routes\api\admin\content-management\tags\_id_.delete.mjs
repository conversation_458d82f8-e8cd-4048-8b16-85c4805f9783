import { c as defineEvent<PERSON>and<PERSON>, j as getRouter<PERSON>aram, f as createError, q as query } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__delete = defineEventHandler(async (event) => {
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const tagId = getRouterParam(event, "id");
    if (!tagId || isNaN(Number(tagId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u6807\u7B7EID"
      });
    }
    const existingTag = await query(
      "SELECT id, name FROM drama_tags WHERE id = ?",
      [tagId]
    );
    if (existingTag.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u6807\u7B7E\u4E0D\u5B58\u5728"
      });
    }
    const tag = existingTag[0];
    await query("DELETE FROM drama_tags WHERE id = ?", [tagId]);
    console.log("\u7BA1\u7406\u5458\u5220\u9664\u6807\u7B7E\u6210\u529F", {
      adminId: admin.id,
      adminUsername: admin.username,
      tagId,
      tagName: tag.name
    });
    return {
      success: true,
      message: "\u6807\u7B7E\u5220\u9664\u6210\u529F",
      data: {
        id: tagId,
        name: tag.name
      }
    };
  } catch (error) {
    console.error("\u7BA1\u7406\u5458\u5220\u9664\u6807\u7B7E\u5931\u8D25", error);
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u5220\u9664\u6807\u7B7E\u5931\u8D25"
    });
  }
});

export { _id__delete as default };
//# sourceMappingURL=_id_.delete.mjs.map
