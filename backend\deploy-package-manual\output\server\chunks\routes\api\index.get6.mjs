import { c as defineE<PERSON><PERSON><PERSON><PERSON>, q as query, f as createError } from '../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_get = defineEventHandler(async (event) => {
  try {
    const tags = await query(`
      SELECT
        id,
        name,
        font_color as fontColor,
        background_color as backgroundColor
      FROM drama_tags
      ORDER BY click_count DESC, id ASC
    `);
    return {
      success: true,
      message: "\u83B7\u53D6\u6807\u7B7E\u5217\u8868\u6210\u529F",
      data: tags
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u516C\u5F00\u6807\u7B7E\u5217\u8868\u5931\u8D25:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "\u83B7\u53D6\u6807\u7B7E\u5217\u8868\u5931\u8D25"
    });
  }
});

export { index_get as default };
//# sourceMappingURL=index.get6.mjs.map
