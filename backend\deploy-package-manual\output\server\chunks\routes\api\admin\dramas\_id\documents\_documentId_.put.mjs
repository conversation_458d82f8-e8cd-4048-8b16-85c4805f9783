import { c as defineEvent<PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, r as readBody, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _documentId__put = defineEventHandler(async (event) => {
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const dramaId = getRouterParam(event, "id");
    const documentId = getRouterParam(event, "documentId");
    if (!dramaId || isNaN(Number(dramaId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u77ED\u5267ID"
      });
    }
    if (!documentId || isNaN(Number(documentId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u6587\u6863ID"
      });
    }
    const body = await readBody(event);
    const { name, fileUrl, fileType, fileSize } = body;
    if (!name || name.trim() === "") {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6587\u6863\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    if (!fileUrl || fileUrl.trim() === "") {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6587\u4EF6\u94FE\u63A5\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const existingDocument = await query(
      "SELECT id, drama_id, name, file_url FROM drama_documents WHERE id = ? AND drama_id = ?",
      [documentId, dramaId]
    );
    if (existingDocument.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u77ED\u5267\u6587\u6863\u4E0D\u5B58\u5728"
      });
    }
    await query(
      `UPDATE drama_documents
       SET name = ?, file_url = ?, file_type = ?, file_size = ?
       WHERE id = ? AND drama_id = ?`,
      [name.trim(), fileUrl.trim(), fileType || null, fileSize || null, documentId, dramaId]
    );
    await logAuditAction({
      action: "ADMIN_UPDATE_DRAMA_DOCUMENT",
      description: `\u7BA1\u7406\u5458\u66F4\u65B0\u77ED\u5267\u6587\u6863: \u77ED\u5267ID=${dramaId}, \u6587\u6863ID=${documentId}`,
      userId: adminPayload.id,
      username: adminPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      metadata: {
        dramaId: Number(dramaId),
        documentId: Number(documentId),
        oldName: existingDocument[0].name,
        newName: name.trim(),
        oldFileUrl: existingDocument[0].file_url,
        newFileUrl: fileUrl.trim(),
        fileType: fileType || null,
        fileSize: fileSize || null
      }
    });
    logger.info("\u77ED\u5267\u6587\u6863\u66F4\u65B0\u6210\u529F", {
      adminId: adminPayload.id,
      adminUsername: adminPayload.username,
      dramaId: Number(dramaId),
      documentId: Number(documentId),
      name: name.trim()
    });
    return {
      success: true,
      message: "\u77ED\u5267\u6587\u6863\u66F4\u65B0\u6210\u529F",
      data: {
        id: Number(documentId),
        dramaId: Number(dramaId),
        name: name.trim(),
        fileUrl: fileUrl.trim(),
        fileType: fileType || null,
        fileSize: fileSize || null,
        updatedAt: (/* @__PURE__ */ new Date()).toISOString()
      }
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u77ED\u5267\u6587\u6863\u5931\u8D25", {
      error: error.message,
      dramaId: getRouterParam(event, "id"),
      documentId: getRouterParam(event, "documentId")
    });
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "\u66F4\u65B0\u77ED\u5267\u6587\u6863\u5931\u8D25"
    });
  }
});

export { _documentId__put as default };
//# sourceMappingURL=_documentId_.put.mjs.map
