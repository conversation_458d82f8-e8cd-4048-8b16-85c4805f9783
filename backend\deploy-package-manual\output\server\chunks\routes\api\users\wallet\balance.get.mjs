import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, Z as verifyUserAccessToken, f as createError, q as query } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const balance_get = defineEventHandler(async (event) => {
  try {
    const userPayload = verifyUserAccessToken(event);
    if (!userPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u7528\u6237\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"
      });
    }
    const userId = userPayload.id;
    let assetRows = await query(`
      SELECT
        shells_balance,
        diamonds_balance,
        total_invested_shells,
        total_earned_diamonds,
        frozen_shells,
        frozen_diamonds
      FROM user_assets
      WHERE user_id = ?
    `, [userId]);
    if (!assetRows || assetRows.length === 0) {
      console.log(`\u7528\u6237 ${userId} \u8D44\u4EA7\u8BB0\u5F55\u4E0D\u5B58\u5728\uFF0C\u521B\u5EFA\u521D\u59CB\u8BB0\u5F55`);
      await query(`
        INSERT INTO user_assets (
          user_id, shells_balance, diamonds_balance,
          total_invested_shells, total_earned_diamonds,
          frozen_shells, frozen_diamonds, created_at, updated_at
        ) VALUES (?, 0, 0, 0, 0, 0, 0, NOW(), NOW())
      `, [userId]);
      assetRows = await query(`
        SELECT
          shells_balance,
          diamonds_balance,
          total_invested_shells,
          total_earned_diamonds,
          frozen_shells,
          frozen_diamonds
        FROM user_assets
        WHERE user_id = ?
      `, [userId]);
    }
    const asset = assetRows[0];
    return {
      success: true,
      data: {
        shellsBalance: parseFloat(asset.shells_balance),
        diamondsBalance: parseFloat(asset.diamonds_balance),
        totalInvestedShells: parseFloat(asset.total_invested_shells),
        totalEarnedDiamonds: parseFloat(asset.total_earned_diamonds),
        frozenShells: parseFloat(asset.frozen_shells),
        frozenDiamonds: parseFloat(asset.frozen_diamonds),
        availableShells: parseFloat(asset.shells_balance) - parseFloat(asset.frozen_shells)
      }
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u94B1\u5305\u4F59\u989D\u5931\u8D25:", error);
    return {
      success: false,
      message: "\u83B7\u53D6\u94B1\u5305\u4F59\u989D\u5931\u8D25",
      error: error.message
    };
  }
});

export { balance_get as default };
//# sourceMappingURL=balance.get.mjs.map
