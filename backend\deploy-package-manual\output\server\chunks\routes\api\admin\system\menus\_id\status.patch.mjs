import { c as defineEvent<PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, r as readBody, q as query, l as logger, e as getClientIP } from '../../../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const status_patch = defineEventHandler(async (event) => {
  var _a;
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(adminPayload.id, PERMISSIONS.SYSTEM_MENU_EDIT);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u4FEE\u6539\u83DC\u5355\u72B6\u6001"
      });
    }
    const menuId = getRouterParam(event, "id");
    if (!menuId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u83DC\u5355ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const body = await readBody(event);
    const { status } = body;
    if (status !== 0 && status !== 1) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u72B6\u6001\u503C"
      });
    }
    const existingMenu = await query(`
      SELECT id, name, status FROM admin_menus WHERE id = ?
    `, [menuId]);
    if (existingMenu.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u83DC\u5355\u4E0D\u5B58\u5728"
      });
    }
    const menu = existingMenu[0];
    if (status === 0) {
      const activeChildren = await query(`
        SELECT COUNT(*) as count FROM admin_menus 
        WHERE pid = ? AND status = 1
      `, [menuId]);
      if (activeChildren[0].count > 0) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u8BE5\u83DC\u5355\u4E0B\u8FD8\u6709\u542F\u7528\u7684\u5B50\u83DC\u5355\uFF0C\u8BF7\u5148\u7981\u7528\u5B50\u83DC\u5355"
        });
      }
    }
    await query(`
      UPDATE admin_menus 
      SET status = ?, updated_at = NOW()
      WHERE id = ?
    `, [status, menuId]);
    logger.info("\u83DC\u5355\u72B6\u6001\u5207\u6362\u6210\u529F", {
      adminId: adminPayload.id,
      menuId,
      menuName: menu.name,
      oldStatus: menu.status,
      newStatus: status,
      ip: getClientIP(event)
    });
    return {
      success: true,
      data: {
        id: menuId,
        status,
        message: `\u83DC\u5355\u5DF2${status === 1 ? "\u542F\u7528" : "\u7981\u7528"}`
      }
    };
  } catch (error) {
    logger.error("\u83DC\u5355\u72B6\u6001\u5207\u6362\u5931\u8D25", {
      error: error.message,
      menuId: getRouterParam(event, "id"),
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { status_patch as default };
//# sourceMappingURL=status.patch.mjs.map
