import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, f as createError, q as query, r as readBody, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__put = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const bannerId = getRouterParam(event, "id");
    if (!bannerId || isNaN(Number(bannerId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u6A2A\u5E45ID"
      });
    }
    const existingBanner = await query(
      "SELECT * FROM banners WHERE id = ?",
      [bannerId]
    );
    if (existingBanner.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u6A2A\u5E45\u4E0D\u5B58\u5728"
      });
    }
    const body = await readBody(event);
    const {
      title,
      subtitle,
      image_url,
      link_url,
      background_color,
      text_color,
      is_active,
      open_in_new_tab,
      sort_order
    } = body;
    if (!title) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6A2A\u5E45\u6807\u9898\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    if (!image_url) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6A2A\u5E45\u56FE\u7247\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const isActiveValue = is_active === true || is_active === 1 || is_active === "1" ? 1 : 0;
    const openInNewTabValue = open_in_new_tab === true || open_in_new_tab === 1 || open_in_new_tab === "1" ? 1 : 0;
    let finalSortOrder = sort_order;
    if (finalSortOrder === void 0 || finalSortOrder === null || isNaN(Number(finalSortOrder))) {
      finalSortOrder = 0;
    }
    await query(
      `UPDATE banners 
       SET title = ?, subtitle = ?, image_url = ?, link_url = ?, background_color = ?, 
           text_color = ?, is_active = ?, open_in_new_tab = ?, sort_order = ?, updated_at = NOW()
       WHERE id = ?`,
      [
        title.trim(),
        (subtitle == null ? void 0 : subtitle.trim()) || null,
        image_url.trim(),
        (link_url == null ? void 0 : link_url.trim()) || null,
        background_color || "#ffffff",
        text_color || "#000000",
        isActiveValue,
        openInNewTabValue,
        finalSortOrder,
        bannerId
      ]
    );
    await logAuditAction({
      action: "ADMIN_UPDATE_BANNER",
      description: `\u7BA1\u7406\u5458\u66F4\u65B0\u6A2A\u5E45: ${title}`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        bannerId: Number(bannerId),
        title,
        subtitle,
        imageUrl: image_url,
        linkUrl: link_url,
        isActive: isActiveValue === 1,
        sortOrder: finalSortOrder,
        oldData: existingBanner[0]
      }
    });
    logger.info("\u7BA1\u7406\u5458\u66F4\u65B0\u6A2A\u5E45\u6210\u529F", {
      adminId: admin.id,
      adminUsername: admin.username,
      bannerId: Number(bannerId),
      title
    });
    return {
      success: true,
      message: "\u6A2A\u5E45\u66F4\u65B0\u6210\u529F",
      data: {
        id: Number(bannerId)
      }
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u6A2A\u5E45\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { _id__put as default };
//# sourceMappingURL=_id_.put.mjs.map
