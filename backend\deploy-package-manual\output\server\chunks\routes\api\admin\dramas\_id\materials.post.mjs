import { c as define<PERSON>vent<PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, f as createError, q as query, r as readBody, l as logger, e as getClientIP } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const materials_post = defineEventHandler(async (event) => {
  var _a, _b, _c;
  try {
    const dramaId = getRouterParam(event, "id");
    if (!dramaId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u77ED\u5267ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const id = parseInt(dramaId);
    if (isNaN(id) || id <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u77ED\u5267ID"
      });
    }
    const dramaCheck = await query(`
      SELECT id FROM drama_series WHERE id = ?
    `, [id]);
    if (dramaCheck.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u77ED\u5267\u4E0D\u5B58\u5728"
      });
    }
    const body = await readBody(event);
    const {
      title,
      url,
      thumbnail,
      type = "image",
      sortOrder = 0
    } = body;
    if (!url) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u7D20\u6750URL\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    if (!["image", "video"].includes(type)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u7D20\u6750\u7C7B\u578B"
      });
    }
    if (typeof sortOrder !== "number") {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6392\u5E8F\u503C\u5FC5\u987B\u4E3A\u6570\u5B57"
      });
    }
    let finalSortOrder = sortOrder;
    if (sortOrder === 0) {
      const maxSortResult = await query(`
        SELECT COALESCE(MAX(sort_order), 0) as maxSort 
        FROM drama_materials 
        WHERE drama_id = ?
      `, [id]);
      finalSortOrder = (((_a = maxSortResult[0]) == null ? void 0 : _a.maxSort) || 0) + 1;
    }
    const result = await query(`
      INSERT INTO drama_materials (
        drama_id, title, url, thumbnail, type, sort_order, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      id,
      title || null,
      url,
      thumbnail || null,
      type,
      finalSortOrder
    ]);
    logger.info("\u521B\u5EFA\u77ED\u5267\u7D20\u6750\u6210\u529F", {
      adminId: ((_b = event.context.admin) == null ? void 0 : _b.id) || "unknown",
      dramaId: id,
      materialId: result.insertId,
      type,
      ip: getClientIP(event)
    });
    return {
      success: true,
      data: {
        id: result.insertId,
        message: "\u7D20\u6750\u521B\u5EFA\u6210\u529F"
      }
    };
  } catch (error) {
    logger.error("\u521B\u5EFA\u77ED\u5267\u7D20\u6750\u5931\u8D25", {
      error: error.message,
      dramaId: getRouterParam(event, "id"),
      adminId: (_c = event.context.admin) == null ? void 0 : _c.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    if (error.code === "ER_NO_REFERENCED_ROW_2") {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5173\u8054\u7684\u77ED\u5267\u4E0D\u5B58\u5728"
      });
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { materials_post as default };
//# sourceMappingURL=materials.post.mjs.map
