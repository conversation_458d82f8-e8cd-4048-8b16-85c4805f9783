{"name": "@mengtu/backend-real", "version": "1.0.0", "description": "剧投投真实后端服务 - 基于Nitro框架", "private": true, "license": "MIT", "author": "剧投投团队", "scripts": {"build": "nitro build", "start": "nitro dev", "dev": "nitro dev", "preview": "nitro preview", "typecheck": "nitro typecheck"}, "dependencies": {"ali-oss": "^6.23.0", "bcryptjs": "^2.4.3", "cos-nodejs-sdk-v5": "^2.14.7", "crossws": "^0.4.1", "dotenv": "^16.3.1", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "mysql2": "^3.6.1", "nitropack": "^2.11.13", "uuid": "^10.0.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^1.4.12", "@types/node": "^24.2.0", "@types/uuid": "^10.0.0", "h3": "^1.15.3"}}