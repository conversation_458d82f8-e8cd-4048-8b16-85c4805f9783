<template>
  <div class="bg-white rounded-lg shadow p-6">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 左侧：封面图片 -->
      <div class="lg:col-span-1">
        <div class="aspect-w-3 aspect-h-4">
          <img
            :src="drama.cover"
            :alt="drama.title"
            class="w-full h-64 object-cover rounded-lg shadow-md"
            @error="handleImageError"
          />
        </div>
      </div>

      <!-- 右侧：基本信息 -->
      <div class="lg:col-span-2 space-y-6">
        <!-- 标题和状态 -->
        <div class="flex items-start justify-between">
          <div>
            <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ drama.title }}</h2>
            <div class="flex items-center space-x-2">
              <Tag :color="drama.isOnline ? 'success' : 'error'">
                {{ drama.isOnline ? '已上线' : '未上线' }}
              </Tag>
              <span class="text-sm text-gray-500">ID: {{ drama.id }}</span>
            </div>
          </div>
        </div>

        <!-- 标签 -->
        <div v-if="parsedTags && parsedTags.length > 0">
          <h3 class="text-sm font-medium text-gray-700 mb-2">标签</h3>
          <div class="flex flex-wrap gap-2">
            <Tag v-for="tag in parsedTags" :key="tag" color="blue">
              {{ tag }}
            </Tag>
          </div>
        </div>

        <!-- 基本信息表格 -->
        <div class="border border-gray-200 rounded-lg overflow-hidden">
          <table class="min-w-full divide-y divide-gray-200">
            <tbody class="bg-white divide-y divide-gray-200">
              <tr>
                <td class="px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50 w-1/3">集数</td>
                <td class="px-4 py-3 text-sm text-gray-900">{{ drama.episodes }}集</td>
              </tr>
              <tr>
                <td class="px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50">单集时长</td>
                <td class="px-4 py-3 text-sm text-gray-900">{{ drama.episodeLength }}分钟</td>
              </tr>
              <tr>
                <td class="px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50">预计播放量</td>
                <td class="px-4 py-3 text-sm text-gray-900">{{ drama.projectedViews || '-' }}</td>
              </tr>
              <tr>
                <td class="px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50">是否上线</td>
                <td class="px-4 py-3 text-sm text-gray-900">
                  <Tag :color="drama.isOnline ? 'success' : 'error'">
                    {{ drama.isOnline ? '已上线' : '未上线' }}
                  </Tag>
                </td>
              </tr>
              <tr>
                <td class="px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50">创建时间</td>
                <td class="px-4 py-3 text-sm text-gray-900">
                  {{ new Date(drama.createdAt).toLocaleString('zh-CN') }}
                </td>
              </tr>
              <tr>
                <td class="px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50">更新时间</td>
                <td class="px-4 py-3 text-sm text-gray-900">
                  {{ new Date(drama.updatedAt).toLocaleString('zh-CN') }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 演员阵容 -->
        <div v-if="parsedCast && parsedCast.length > 0">
          <h3 class="text-sm font-medium text-gray-700 mb-2">演员阵容</h3>
          <div class="flex flex-wrap gap-2">
            <Tag v-for="actor in parsedCast" :key="actor" color="purple">
              {{ actor }}
            </Tag>
          </div>
        </div>

        <!-- 目标平台 -->
        <div v-if="parsedTargetPlatform && parsedTargetPlatform.length > 0">
          <h3 class="text-sm font-medium text-gray-700 mb-2">目标平台</h3>
          <div class="flex flex-wrap gap-2">
            <Tag v-for="platform in parsedTargetPlatform" :key="platform" color="green">
              {{ platform }}
            </Tag>
          </div>
        </div>

        <!-- 短剧描述 -->
        <div v-if="drama.description">
          <h3 class="text-sm font-medium text-gray-700 mb-2">短剧描述</h3>
          <p class="text-gray-900 leading-relaxed">{{ drama.description }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { Tag } from 'ant-design-vue';
import type { DramaManagementApi } from '#/api/drama-management';

interface Props {
  drama: DramaManagementApi.Drama;
}

const props = defineProps<Props>();

// 解析JSON字段
const parsedTags = computed(() => {
  if (!props.drama.tags) return [];
  if (Array.isArray(props.drama.tags)) return props.drama.tags;
  try {
    return JSON.parse(props.drama.tags);
  } catch {
    return [];
  }
});

const parsedCast = computed(() => {
  if (!props.drama.cast) return [];
  if (Array.isArray(props.drama.cast)) return props.drama.cast;
  try {
    return JSON.parse(props.drama.cast);
  } catch {
    return [];
  }
});

const parsedTargetPlatform = computed(() => {
  if (!props.drama.targetPlatform) return [];
  if (Array.isArray(props.drama.targetPlatform)) return props.drama.targetPlatform;
  try {
    return JSON.parse(props.drama.targetPlatform);
  } catch {
    return [];
  }
});

// 图片错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjI2NyIgdmlld0JveD0iMCAwIDIwMCAyNjciIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjY3IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04NS4zMzMzIDEwNi42NjdIMTE0LjY2N1YxMzZIMTQ0VjE2NS4zMzNIODUuMzMzM1YxMDYuNjY3WiIgZmlsbD0iI0Q5REREREQiLz4KPC9zdmc+'; // 使用 base64 编码的占位图片
};
</script>
