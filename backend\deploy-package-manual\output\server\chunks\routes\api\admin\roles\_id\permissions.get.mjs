import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, q as query, l as logger, e as getClientIP } from '../../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const permissions_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(adminPayload.id, PERMISSIONS.ROLE_MANAGE);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u67E5\u770B\u89D2\u8272\u6743\u9650"
      });
    }
    const roleId = getRouterParam(event, "id");
    if (!roleId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u89D2\u8272ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const existingRole = await query(`
      SELECT id, name FROM admin_roles WHERE id = ?
    `, [roleId]);
    if (existingRole.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u89D2\u8272\u4E0D\u5B58\u5728"
      });
    }
    const permissions = await query(`
      SELECT permission_code 
      FROM admin_role_permissions 
      WHERE role_id = ?
    `, [roleId]);
    const permissionCodes = permissions.map((p) => p.permission_code);
    logger.info("\u83B7\u53D6\u89D2\u8272\u6743\u9650\u6210\u529F", {
      adminId: adminPayload.id,
      roleId,
      permissionCount: permissionCodes.length,
      ip: getClientIP(event)
    });
    return {
      success: true,
      data: permissionCodes
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u89D2\u8272\u6743\u9650\u5931\u8D25", {
      error: error.message,
      roleId: getRouterParam(event, "id"),
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { permissions_get as default };
//# sourceMappingURL=permissions.get.mjs.map
