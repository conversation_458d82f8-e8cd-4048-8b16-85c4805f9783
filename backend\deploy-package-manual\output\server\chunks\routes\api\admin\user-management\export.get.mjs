import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, g as getQuery, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger, z as setHeader } from '../../../../_/nitro.mjs';
import { a as validateStatus } from '../../../../_/validators.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const export_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.USER_LIST);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u5BFC\u51FA\u7528\u6237\u6570\u636E"
      });
    }
    const query$1 = getQuery(event);
    const search = query$1.search || "";
    const status = validateStatus(query$1.status);
    const userType = query$1.userType || "";
    let whereClause = "WHERE 1=1";
    const params = [];
    if (search) {
      whereClause += " AND (username LIKE ? OR email LIKE ? OR phone LIKE ? OR real_name LIKE ? OR company_name LIKE ?)";
      params.push(`%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`, `%${search}%`);
    }
    if (status !== null) {
      whereClause += " AND status = ?";
      params.push(status);
    }
    if (userType && ["investor", "producer", "fund_manager"].includes(userType)) {
      whereClause += " AND user_type = ?";
      params.push(userType);
    }
    const users = await query(
      `SELECT id, username, email, phone, user_type, real_name, company_name,
              id_card, business_license, status, created_at, updated_at, last_login_at
       FROM users
       ${whereClause}
       ORDER BY created_at DESC`,
      params
    );
    const exportData = users.map((user) => ({
      "ID": user.id,
      "\u7528\u6237\u540D": user.username,
      "\u90AE\u7BB1": user.email,
      "\u624B\u673A\u53F7": user.phone || "",
      "\u7528\u6237\u7C7B\u578B": getUserTypeLabel(user.user_type),
      "\u771F\u5B9E\u59D3\u540D": user.real_name || "",
      "\u516C\u53F8\u540D\u79F0": user.company_name || "",
      "\u8EAB\u4EFD\u8BC1\u53F7": user.id_card || "",
      "\u8425\u4E1A\u6267\u7167\u53F7": user.business_license || "",
      "\u72B6\u6001": user.status === 1 ? "\u6B63\u5E38" : "\u7981\u7528",
      "\u6CE8\u518C\u65F6\u95F4": user.created_at ? new Date(user.created_at).toLocaleString("zh-CN") : "",
      "\u6700\u540E\u767B\u5F55": user.last_login_at ? new Date(user.last_login_at).toLocaleString("zh-CN") : "\u4ECE\u672A\u767B\u5F55",
      "\u66F4\u65B0\u65F6\u95F4": user.updated_at ? new Date(user.updated_at).toLocaleString("zh-CN") : ""
    }));
    const headers = Object.keys(exportData[0] || {});
    const csvContent = [
      headers.join(","),
      ...exportData.map(
        (row) => headers.map((header) => {
          const value = row[header] || "";
          return typeof value === "string" && value.includes(",") ? `"${value.replace(/"/g, '""')}"` : value;
        }).join(",")
      )
    ].join("\n");
    const bom = "\uFEFF";
    const csvWithBom = bom + csvContent;
    await logAuditAction({
      action: "ADMIN_EXPORT_USERS",
      description: `\u7BA1\u7406\u5458\u5BFC\u51FA\u7528\u6237\u6570\u636E\uFF0C\u5171 ${users.length} \u6761\u8BB0\u5F55`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        exportCount: users.length,
        filters: { search, status, userType }
      }
    });
    logger.info("\u7BA1\u7406\u5458\u5BFC\u51FA\u7528\u6237\u6570\u636E", {
      adminId: admin.id,
      exportCount: users.length,
      filters: { search, status, userType },
      ip: getClientIP(event)
    });
    const timestamp = (/* @__PURE__ */ new Date()).toISOString().slice(0, 19).replace(/:/g, "-");
    const filename = `users_export_${timestamp}.csv`;
    setHeader(event, "Content-Type", "text/csv; charset=utf-8");
    setHeader(event, "Content-Disposition", `attachment; filename="${encodeURIComponent(filename)}"`);
    setHeader(event, "Cache-Control", "no-cache");
    return csvWithBom;
  } catch (error) {
    logger.error("\u5BFC\u51FA\u7528\u6237\u6570\u636E\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});
function getUserTypeLabel(userType) {
  const typeMap = {
    "investor": "\u6295\u8D44\u8005",
    "producer": "\u627F\u5236\u5382\u724C",
    "fund_manager": "\u57FA\u91D1\u7BA1\u7406\u4EBA"
  };
  return typeMap[userType] || userType;
}

export { export_get as default };
//# sourceMappingURL=export.get.mjs.map
