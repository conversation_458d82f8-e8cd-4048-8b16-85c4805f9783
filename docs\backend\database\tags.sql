-- 标签管理表
CREATE TABLE IF NOT EXISTS tags (
  id INT AUTO_INCREMENT PRIMARY KEY COMMENT '标签ID',
  name VARCHAR(50) NOT NULL COMMENT '标签名称',
  font_color VARCHAR(7) NOT NULL DEFAULT '#000000' COMMENT '字体颜色',
  background_color VARCHAR(7) NOT NULL DEFAULT '#ffffff' COMMENT '背景颜色',
  click_count INT NOT NULL DEFAULT 0 COMMENT '点击数',
  operator VARCHAR(50) NOT NULL COMMENT '操作人',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  operated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '操作时间',
  UNIQUE KEY uk_name (name) COMMENT '标签名称唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签管理表';

-- 插入示例数据
INSERT INTO tags (name, font_color, background_color, click_count, operator) VALUES
('热门', '#ffffff', '#ff4d4f', 1250, 'admin'),
('推荐', '#ffffff', '#52c41a', 980, 'admin'),
('新剧', '#ffffff', '#1890ff', 756, 'admin'),
('经典', '#000000', '#faad14', 432, 'admin'),
('独家', '#ffffff', '#722ed1', 298, 'admin');
