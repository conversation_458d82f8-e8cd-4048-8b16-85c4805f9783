# 生产环境数据库部署指南

## 📁 文件说明

### **mengtu_production_final.sql**
- **用途**: 生产环境完整数据库文件
- **数据库名**: mengtu
- **表数量**: 46张表
- **特点**: 包含完整表结构和数据，支持覆盖更新

## 🚀 部署步骤

### **方法一：宝塔面板部署（推荐）**

1. **登录宝塔面板**
   ```
   访问: http://你的服务器IP:8888
   ```

2. **进入数据库管理**
   - 点击左侧菜单 "数据库"
   - 找到 `mengtu` 数据库
   - 点击 "管理" 按钮

3. **导入数据库**
   - 进入 phpMyAdmin 界面
   - 确保选中了 `mengtu` 数据库
   - 点击顶部 "导入" 选项卡
   - 点击 "选择文件" 按钮
   - 上传 `mengtu_production_final.sql` 文件
   - 点击 "执行" 按钮

4. **等待完成**
   - 导入过程可能需要几分钟
   - 成功后会显示 "导入已成功完成"

### **方法二：命令行部署**

```bash
# 1. 上传文件到服务器
scp docs/mengtu_production_final.sql root@你的服务器IP:/root/

# 2. 登录服务器
ssh root@你的服务器IP

# 3. 导入数据库
mysql -u root -p mengtu < /root/mengtu_production_final.sql
```

## ✅ 验证部署

### **检查表数量**
```sql
-- 应该返回 46
SELECT COUNT(*) as table_count 
FROM information_schema.tables 
WHERE table_schema = 'mengtu';
```

### **检查核心表数据**
```sql
-- 检查用户表
SELECT COUNT(*) as user_count FROM users;

-- 检查短剧表
SELECT COUNT(*) as drama_count FROM drama_series;

-- 检查基金表
SELECT COUNT(*) as fund_count FROM funds;

-- 检查演员表
SELECT COUNT(*) as actor_count FROM actors;
```

### **检查表结构**
```sql
-- 查看所有表
SHOW TABLES;

-- 检查用户表结构
DESCRIBE users;
```

## 🔧 重要特性

### **自动覆盖更新**
- ✅ 使用 `DROP TABLE IF EXISTS` 语句
- ✅ 完全替换现有表结构
- ✅ 不会因为表已存在而报错
- ✅ 自动更新所有字段调整

### **包含的数据**
- ✅ 完整的表结构定义
- ✅ 所有索引和约束
- ✅ 现有的业务数据
- ✅ 测试数据（可后续清理）

### **字符集设置**
- ✅ 统一使用 utf8mb4_unicode_ci
- ✅ 支持中文和emoji
- ✅ 避免乱码问题

## ⚠️ 注意事项

### **数据备份**
- 🔴 **重要**: 执行前请备份现有数据库
- 🔴 此操作会完全替换现有表
- 🔴 无法撤销，请谨慎操作

### **执行环境**
- ✅ 确保数据库 `mengtu` 已存在
- ✅ 确保有足够的磁盘空间
- ✅ 确保数据库用户有足够权限

### **执行时间**
- ⏱️ 预计执行时间: 2-5分钟
- ⏱️ 取决于服务器性能和数据量
- ⏱️ 执行期间请勿中断

## 🎯 执行结果

成功执行后，您将获得：

- ✅ **46张完整的数据表**
- ✅ **最新的表结构**（包含您的字段调整）
- ✅ **完整的业务数据**
- ✅ **正确的字符集设置**
- ✅ **所有必要的索引和约束**

## 📞 问题排查

### **常见错误及解决方案**

**错误1: 数据库不存在**
```sql
CREATE DATABASE mengtu CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

**错误2: 权限不足**
```sql
GRANT ALL PRIVILEGES ON mengtu.* TO 'your_user'@'localhost';
FLUSH PRIVILEGES;
```

**错误3: 导入超时**
- 增加 phpMyAdmin 超时设置
- 或使用命令行方式导入

需要帮助请随时联系！
