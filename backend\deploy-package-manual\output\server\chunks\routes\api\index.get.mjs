import { c as defineEvent<PERSON>and<PERSON>, g as getQuery, q as query, l as logger, e as getClientIP, f as createError } from '../../_/nitro.mjs';
import { v as validatePagination } from '../../_/validators.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_get = defineEventHandler(async (event) => {
  var _a;
  try {
    const queryParams = getQuery(event);
    const { page, pageSize, offset } = validatePagination(queryParams.page, queryParams.pageSize);
    const whereClause = "WHERE is_active = 1";
    const countResult = await query(
      `SELECT COUNT(*) as total FROM actors ${whereClause}`
    );
    const total = ((_a = countResult[0]) == null ? void 0 : _a.total) || 0;
    const actors = await query(
      `SELECT id, name, avatar_url, bio, tags, sort_order, created_at
       FROM actors 
       ${whereClause}
       ORDER BY sort_order ASC, id DESC
       LIMIT ? OFFSET ?`,
      [pageSize, offset]
    );
    const formattedActors = actors.map((actor) => ({
      id: actor.id,
      name: actor.name,
      avatarUrl: actor.avatar_url,
      bio: actor.bio,
      tags: actor.tags,
      sortOrder: actor.sort_order,
      createdAt: actor.created_at
    }));
    return {
      success: true,
      data: {
        list: formattedActors,
        pagination: {
          page,
          pageSize,
          total,
          totalPages: Math.ceil(total / pageSize)
        }
      }
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u516C\u5F00\u6F14\u5458\u5217\u8868\u5931\u8D25", {
      error: error.message,
      ip: getClientIP(event)
    });
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { index_get as default };
//# sourceMappingURL=index.get.mjs.map
