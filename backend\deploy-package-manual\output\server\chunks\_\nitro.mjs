import process from 'node:process';globalThis._importMeta_=globalThis._importMeta_||{url:"file:///_entry.js",env:process.env};import http from 'node:http';
import https from 'node:https';
import { EventEmitter } from 'node:events';
import { Buffer as Buffer$1 } from 'node:buffer';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import mysql from 'mysql2/promise';
import { promises, existsSync } from 'node:fs';
import { fileURLToPath } from 'node:url';
import { resolve as resolve$1, dirname as dirname$1, join } from 'node:path';
import { createHash } from 'node:crypto';

const suspectProtoRx = /"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/;
const suspectConstructorRx = /"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/;
const JsonSigRx = /^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;
function jsonParseTransform(key, value) {
  if (key === "__proto__" || key === "constructor" && value && typeof value === "object" && "prototype" in value) {
    warnKeyDropped(key);
    return;
  }
  return value;
}
function warnKeyDropped(key) {
  console.warn(`[destr] Dropping "${key}" key to prevent prototype pollution.`);
}
function destr(value, options = {}) {
  if (typeof value !== "string") {
    return value;
  }
  if (value[0] === '"' && value[value.length - 1] === '"' && value.indexOf("\\") === -1) {
    return value.slice(1, -1);
  }
  const _value = value.trim();
  if (_value.length <= 9) {
    switch (_value.toLowerCase()) {
      case "true": {
        return true;
      }
      case "false": {
        return false;
      }
      case "undefined": {
        return void 0;
      }
      case "null": {
        return null;
      }
      case "nan": {
        return Number.NaN;
      }
      case "infinity": {
        return Number.POSITIVE_INFINITY;
      }
      case "-infinity": {
        return Number.NEGATIVE_INFINITY;
      }
    }
  }
  if (!JsonSigRx.test(value)) {
    if (options.strict) {
      throw new SyntaxError("[destr] Invalid JSON");
    }
    return value;
  }
  try {
    if (suspectProtoRx.test(value) || suspectConstructorRx.test(value)) {
      if (options.strict) {
        throw new Error("[destr] Possible prototype pollution");
      }
      return JSON.parse(value, jsonParseTransform);
    }
    return JSON.parse(value);
  } catch (error) {
    if (options.strict) {
      throw error;
    }
    return value;
  }
}

const HASH_RE = /#/g;
const AMPERSAND_RE = /&/g;
const SLASH_RE = /\//g;
const EQUAL_RE = /=/g;
const PLUS_RE = /\+/g;
const ENC_CARET_RE = /%5e/gi;
const ENC_BACKTICK_RE = /%60/gi;
const ENC_PIPE_RE = /%7c/gi;
const ENC_SPACE_RE = /%20/gi;
const ENC_SLASH_RE = /%2f/gi;
function encode(text) {
  return encodeURI("" + text).replace(ENC_PIPE_RE, "|");
}
function encodeQueryValue(input) {
  return encode(typeof input === "string" ? input : JSON.stringify(input)).replace(PLUS_RE, "%2B").replace(ENC_SPACE_RE, "+").replace(HASH_RE, "%23").replace(AMPERSAND_RE, "%26").replace(ENC_BACKTICK_RE, "`").replace(ENC_CARET_RE, "^").replace(SLASH_RE, "%2F");
}
function encodeQueryKey(text) {
  return encodeQueryValue(text).replace(EQUAL_RE, "%3D");
}
function decode$1(text = "") {
  try {
    return decodeURIComponent("" + text);
  } catch {
    return "" + text;
  }
}
function decodePath(text) {
  return decode$1(text.replace(ENC_SLASH_RE, "%252F"));
}
function decodeQueryKey(text) {
  return decode$1(text.replace(PLUS_RE, " "));
}
function decodeQueryValue(text) {
  return decode$1(text.replace(PLUS_RE, " "));
}

function parseQuery(parametersString = "") {
  const object = /* @__PURE__ */ Object.create(null);
  if (parametersString[0] === "?") {
    parametersString = parametersString.slice(1);
  }
  for (const parameter of parametersString.split("&")) {
    const s = parameter.match(/([^=]+)=?(.*)/) || [];
    if (s.length < 2) {
      continue;
    }
    const key = decodeQueryKey(s[1]);
    if (key === "__proto__" || key === "constructor") {
      continue;
    }
    const value = decodeQueryValue(s[2] || "");
    if (object[key] === void 0) {
      object[key] = value;
    } else if (Array.isArray(object[key])) {
      object[key].push(value);
    } else {
      object[key] = [object[key], value];
    }
  }
  return object;
}
function encodeQueryItem(key, value) {
  if (typeof value === "number" || typeof value === "boolean") {
    value = String(value);
  }
  if (!value) {
    return encodeQueryKey(key);
  }
  if (Array.isArray(value)) {
    return value.map(
      (_value) => `${encodeQueryKey(key)}=${encodeQueryValue(_value)}`
    ).join("&");
  }
  return `${encodeQueryKey(key)}=${encodeQueryValue(value)}`;
}
function stringifyQuery(query) {
  return Object.keys(query).filter((k) => query[k] !== void 0).map((k) => encodeQueryItem(k, query[k])).filter(Boolean).join("&");
}

const PROTOCOL_STRICT_REGEX = /^[\s\w\0+.-]{2,}:([/\\]{1,2})/;
const PROTOCOL_REGEX = /^[\s\w\0+.-]{2,}:([/\\]{2})?/;
const PROTOCOL_RELATIVE_REGEX = /^([/\\]\s*){2,}[^/\\]/;
const JOIN_LEADING_SLASH_RE = /^\.?\//;
function hasProtocol(inputString, opts = {}) {
  if (typeof opts === "boolean") {
    opts = { acceptRelative: opts };
  }
  if (opts.strict) {
    return PROTOCOL_STRICT_REGEX.test(inputString);
  }
  return PROTOCOL_REGEX.test(inputString) || (opts.acceptRelative ? PROTOCOL_RELATIVE_REGEX.test(inputString) : false);
}
function hasTrailingSlash(input = "", respectQueryAndFragment) {
  {
    return input.endsWith("/");
  }
}
function withoutTrailingSlash(input = "", respectQueryAndFragment) {
  {
    return (hasTrailingSlash(input) ? input.slice(0, -1) : input) || "/";
  }
}
function withTrailingSlash(input = "", respectQueryAndFragment) {
  {
    return input.endsWith("/") ? input : input + "/";
  }
}
function hasLeadingSlash(input = "") {
  return input.startsWith("/");
}
function withLeadingSlash(input = "") {
  return hasLeadingSlash(input) ? input : "/" + input;
}
function withBase(input, base) {
  if (isEmptyURL(base) || hasProtocol(input)) {
    return input;
  }
  const _base = withoutTrailingSlash(base);
  if (input.startsWith(_base)) {
    return input;
  }
  return joinURL(_base, input);
}
function withoutBase(input, base) {
  if (isEmptyURL(base)) {
    return input;
  }
  const _base = withoutTrailingSlash(base);
  if (!input.startsWith(_base)) {
    return input;
  }
  const trimmed = input.slice(_base.length);
  return trimmed[0] === "/" ? trimmed : "/" + trimmed;
}
function withQuery(input, query) {
  const parsed = parseURL(input);
  const mergedQuery = { ...parseQuery(parsed.search), ...query };
  parsed.search = stringifyQuery(mergedQuery);
  return stringifyParsedURL(parsed);
}
function getQuery$1(input) {
  return parseQuery(parseURL(input).search);
}
function isEmptyURL(url) {
  return !url || url === "/";
}
function isNonEmptyURL(url) {
  return url && url !== "/";
}
function joinURL(base, ...input) {
  let url = base || "";
  for (const segment of input.filter((url2) => isNonEmptyURL(url2))) {
    if (url) {
      const _segment = segment.replace(JOIN_LEADING_SLASH_RE, "");
      url = withTrailingSlash(url) + _segment;
    } else {
      url = segment;
    }
  }
  return url;
}

const protocolRelative = Symbol.for("ufo:protocolRelative");
function parseURL(input = "", defaultProto) {
  const _specialProtoMatch = input.match(
    /^[\s\0]*(blob:|data:|javascript:|vbscript:)(.*)/i
  );
  if (_specialProtoMatch) {
    const [, _proto, _pathname = ""] = _specialProtoMatch;
    return {
      protocol: _proto.toLowerCase(),
      pathname: _pathname,
      href: _proto + _pathname,
      auth: "",
      host: "",
      search: "",
      hash: ""
    };
  }
  if (!hasProtocol(input, { acceptRelative: true })) {
    return defaultProto ? parseURL(defaultProto + input) : parsePath(input);
  }
  const [, protocol = "", auth, hostAndPath = ""] = input.replace(/\\/g, "/").match(/^[\s\0]*([\w+.-]{2,}:)?\/\/([^/@]+@)?(.*)/) || [];
  let [, host = "", path = ""] = hostAndPath.match(/([^#/?]*)(.*)?/) || [];
  if (protocol === "file:") {
    path = path.replace(/\/(?=[A-Za-z]:)/, "");
  }
  const { pathname, search, hash } = parsePath(path);
  return {
    protocol: protocol.toLowerCase(),
    auth: auth ? auth.slice(0, Math.max(0, auth.length - 1)) : "",
    host,
    pathname,
    search,
    hash,
    [protocolRelative]: !protocol
  };
}
function parsePath(input = "") {
  const [pathname = "", search = "", hash = ""] = (input.match(/([^#?]*)(\?[^#]*)?(#.*)?/) || []).splice(1);
  return {
    pathname,
    search,
    hash
  };
}
function stringifyParsedURL(parsed) {
  const pathname = parsed.pathname || "";
  const search = parsed.search ? (parsed.search.startsWith("?") ? "" : "?") + parsed.search : "";
  const hash = parsed.hash || "";
  const auth = parsed.auth ? parsed.auth + "@" : "";
  const host = parsed.host || "";
  const proto = parsed.protocol || parsed[protocolRelative] ? (parsed.protocol || "") + "//" : "";
  return proto + auth + host + pathname + search + hash;
}

function parse$1(str, options) {
  if (typeof str !== "string") {
    throw new TypeError("argument str must be a string");
  }
  const obj = {};
  const opt = {};
  const dec = opt.decode || decode;
  let index = 0;
  while (index < str.length) {
    const eqIdx = str.indexOf("=", index);
    if (eqIdx === -1) {
      break;
    }
    let endIdx = str.indexOf(";", index);
    if (endIdx === -1) {
      endIdx = str.length;
    } else if (endIdx < eqIdx) {
      index = str.lastIndexOf(";", eqIdx - 1) + 1;
      continue;
    }
    const key = str.slice(index, eqIdx).trim();
    if (opt?.filter && !opt?.filter(key)) {
      index = endIdx + 1;
      continue;
    }
    if (void 0 === obj[key]) {
      let val = str.slice(eqIdx + 1, endIdx).trim();
      if (val.codePointAt(0) === 34) {
        val = val.slice(1, -1);
      }
      obj[key] = tryDecode(val, dec);
    }
    index = endIdx + 1;
  }
  return obj;
}
function decode(str) {
  return str.includes("%") ? decodeURIComponent(str) : str;
}
function tryDecode(str, decode2) {
  try {
    return decode2(str);
  } catch {
    return str;
  }
}

const fieldContentRegExp = /^[\u0009\u0020-\u007E\u0080-\u00FF]+$/;
function serialize$1(name, value, options) {
  const opt = options || {};
  const enc = opt.encode || encodeURIComponent;
  if (typeof enc !== "function") {
    throw new TypeError("option encode is invalid");
  }
  if (!fieldContentRegExp.test(name)) {
    throw new TypeError("argument name is invalid");
  }
  const encodedValue = enc(value);
  if (encodedValue && !fieldContentRegExp.test(encodedValue)) {
    throw new TypeError("argument val is invalid");
  }
  let str = name + "=" + encodedValue;
  if (void 0 !== opt.maxAge && opt.maxAge !== null) {
    const maxAge = opt.maxAge - 0;
    if (Number.isNaN(maxAge) || !Number.isFinite(maxAge)) {
      throw new TypeError("option maxAge is invalid");
    }
    str += "; Max-Age=" + Math.floor(maxAge);
  }
  if (opt.domain) {
    if (!fieldContentRegExp.test(opt.domain)) {
      throw new TypeError("option domain is invalid");
    }
    str += "; Domain=" + opt.domain;
  }
  if (opt.path) {
    if (!fieldContentRegExp.test(opt.path)) {
      throw new TypeError("option path is invalid");
    }
    str += "; Path=" + opt.path;
  }
  if (opt.expires) {
    if (!isDate(opt.expires) || Number.isNaN(opt.expires.valueOf())) {
      throw new TypeError("option expires is invalid");
    }
    str += "; Expires=" + opt.expires.toUTCString();
  }
  if (opt.httpOnly) {
    str += "; HttpOnly";
  }
  if (opt.secure) {
    str += "; Secure";
  }
  if (opt.priority) {
    const priority = typeof opt.priority === "string" ? opt.priority.toLowerCase() : opt.priority;
    switch (priority) {
      case "low": {
        str += "; Priority=Low";
        break;
      }
      case "medium": {
        str += "; Priority=Medium";
        break;
      }
      case "high": {
        str += "; Priority=High";
        break;
      }
      default: {
        throw new TypeError("option priority is invalid");
      }
    }
  }
  if (opt.sameSite) {
    const sameSite = typeof opt.sameSite === "string" ? opt.sameSite.toLowerCase() : opt.sameSite;
    switch (sameSite) {
      case true: {
        str += "; SameSite=Strict";
        break;
      }
      case "lax": {
        str += "; SameSite=Lax";
        break;
      }
      case "strict": {
        str += "; SameSite=Strict";
        break;
      }
      case "none": {
        str += "; SameSite=None";
        break;
      }
      default: {
        throw new TypeError("option sameSite is invalid");
      }
    }
  }
  if (opt.partitioned) {
    str += "; Partitioned";
  }
  return str;
}
function isDate(val) {
  return Object.prototype.toString.call(val) === "[object Date]" || val instanceof Date;
}

function parseSetCookie(setCookieValue, options) {
  const parts = (setCookieValue || "").split(";").filter((str) => typeof str === "string" && !!str.trim());
  const nameValuePairStr = parts.shift() || "";
  const parsed = _parseNameValuePair(nameValuePairStr);
  const name = parsed.name;
  let value = parsed.value;
  try {
    value = options?.decode === false ? value : (options?.decode || decodeURIComponent)(value);
  } catch {
  }
  const cookie = {
    name,
    value
  };
  for (const part of parts) {
    const sides = part.split("=");
    const partKey = (sides.shift() || "").trimStart().toLowerCase();
    const partValue = sides.join("=");
    switch (partKey) {
      case "expires": {
        cookie.expires = new Date(partValue);
        break;
      }
      case "max-age": {
        cookie.maxAge = Number.parseInt(partValue, 10);
        break;
      }
      case "secure": {
        cookie.secure = true;
        break;
      }
      case "httponly": {
        cookie.httpOnly = true;
        break;
      }
      case "samesite": {
        cookie.sameSite = partValue;
        break;
      }
      default: {
        cookie[partKey] = partValue;
      }
    }
  }
  return cookie;
}
function _parseNameValuePair(nameValuePairStr) {
  let name = "";
  let value = "";
  const nameValueArr = nameValuePairStr.split("=");
  if (nameValueArr.length > 1) {
    name = nameValueArr.shift();
    value = nameValueArr.join("=");
  } else {
    value = nameValuePairStr;
  }
  return { name, value };
}

const NODE_TYPES = {
  NORMAL: 0,
  WILDCARD: 1,
  PLACEHOLDER: 2
};

function createRouter$1(options = {}) {
  const ctx = {
    options,
    rootNode: createRadixNode(),
    staticRoutesMap: {}
  };
  const normalizeTrailingSlash = (p) => options.strictTrailingSlash ? p : p.replace(/\/$/, "") || "/";
  if (options.routes) {
    for (const path in options.routes) {
      insert(ctx, normalizeTrailingSlash(path), options.routes[path]);
    }
  }
  return {
    ctx,
    lookup: (path) => lookup(ctx, normalizeTrailingSlash(path)),
    insert: (path, data) => insert(ctx, normalizeTrailingSlash(path), data),
    remove: (path) => remove(ctx, normalizeTrailingSlash(path))
  };
}
function lookup(ctx, path) {
  const staticPathNode = ctx.staticRoutesMap[path];
  if (staticPathNode) {
    return staticPathNode.data;
  }
  const sections = path.split("/");
  const params = {};
  let paramsFound = false;
  let wildcardNode = null;
  let node = ctx.rootNode;
  let wildCardParam = null;
  for (let i = 0; i < sections.length; i++) {
    const section = sections[i];
    if (node.wildcardChildNode !== null) {
      wildcardNode = node.wildcardChildNode;
      wildCardParam = sections.slice(i).join("/");
    }
    const nextNode = node.children.get(section);
    if (nextNode === void 0) {
      if (node && node.placeholderChildren.length > 1) {
        const remaining = sections.length - i;
        node = node.placeholderChildren.find((c) => c.maxDepth === remaining) || null;
      } else {
        node = node.placeholderChildren[0] || null;
      }
      if (!node) {
        break;
      }
      if (node.paramName) {
        params[node.paramName] = section;
      }
      paramsFound = true;
    } else {
      node = nextNode;
    }
  }
  if ((node === null || node.data === null) && wildcardNode !== null) {
    node = wildcardNode;
    params[node.paramName || "_"] = wildCardParam;
    paramsFound = true;
  }
  if (!node) {
    return null;
  }
  if (paramsFound) {
    return {
      ...node.data,
      params: paramsFound ? params : void 0
    };
  }
  return node.data;
}
function insert(ctx, path, data) {
  let isStaticRoute = true;
  const sections = path.split("/");
  let node = ctx.rootNode;
  let _unnamedPlaceholderCtr = 0;
  const matchedNodes = [node];
  for (const section of sections) {
    let childNode;
    if (childNode = node.children.get(section)) {
      node = childNode;
    } else {
      const type = getNodeType(section);
      childNode = createRadixNode({ type, parent: node });
      node.children.set(section, childNode);
      if (type === NODE_TYPES.PLACEHOLDER) {
        childNode.paramName = section === "*" ? `_${_unnamedPlaceholderCtr++}` : section.slice(1);
        node.placeholderChildren.push(childNode);
        isStaticRoute = false;
      } else if (type === NODE_TYPES.WILDCARD) {
        node.wildcardChildNode = childNode;
        childNode.paramName = section.slice(
          3
          /* "**:" */
        ) || "_";
        isStaticRoute = false;
      }
      matchedNodes.push(childNode);
      node = childNode;
    }
  }
  for (const [depth, node2] of matchedNodes.entries()) {
    node2.maxDepth = Math.max(matchedNodes.length - depth, node2.maxDepth || 0);
  }
  node.data = data;
  if (isStaticRoute === true) {
    ctx.staticRoutesMap[path] = node;
  }
  return node;
}
function remove(ctx, path) {
  let success = false;
  const sections = path.split("/");
  let node = ctx.rootNode;
  for (const section of sections) {
    node = node.children.get(section);
    if (!node) {
      return success;
    }
  }
  if (node.data) {
    const lastSection = sections.at(-1) || "";
    node.data = null;
    if (Object.keys(node.children).length === 0 && node.parent) {
      node.parent.children.delete(lastSection);
      node.parent.wildcardChildNode = null;
      node.parent.placeholderChildren = [];
    }
    success = true;
  }
  return success;
}
function createRadixNode(options = {}) {
  return {
    type: options.type || NODE_TYPES.NORMAL,
    maxDepth: 0,
    parent: options.parent || null,
    children: /* @__PURE__ */ new Map(),
    data: options.data || null,
    paramName: options.paramName || null,
    wildcardChildNode: null,
    placeholderChildren: []
  };
}
function getNodeType(str) {
  if (str.startsWith("**")) {
    return NODE_TYPES.WILDCARD;
  }
  if (str[0] === ":" || str === "*") {
    return NODE_TYPES.PLACEHOLDER;
  }
  return NODE_TYPES.NORMAL;
}

function toRouteMatcher(router) {
  const table = _routerNodeToTable("", router.ctx.rootNode);
  return _createMatcher(table, router.ctx.options.strictTrailingSlash);
}
function _createMatcher(table, strictTrailingSlash) {
  return {
    ctx: { table },
    matchAll: (path) => _matchRoutes(path, table, strictTrailingSlash)
  };
}
function _createRouteTable() {
  return {
    static: /* @__PURE__ */ new Map(),
    wildcard: /* @__PURE__ */ new Map(),
    dynamic: /* @__PURE__ */ new Map()
  };
}
function _matchRoutes(path, table, strictTrailingSlash) {
  if (strictTrailingSlash !== true && path.endsWith("/")) {
    path = path.slice(0, -1) || "/";
  }
  const matches = [];
  for (const [key, value] of _sortRoutesMap(table.wildcard)) {
    if (path === key || path.startsWith(key + "/")) {
      matches.push(value);
    }
  }
  for (const [key, value] of _sortRoutesMap(table.dynamic)) {
    if (path.startsWith(key + "/")) {
      const subPath = "/" + path.slice(key.length).split("/").splice(2).join("/");
      matches.push(..._matchRoutes(subPath, value));
    }
  }
  const staticMatch = table.static.get(path);
  if (staticMatch) {
    matches.push(staticMatch);
  }
  return matches.filter(Boolean);
}
function _sortRoutesMap(m) {
  return [...m.entries()].sort((a, b) => a[0].length - b[0].length);
}
function _routerNodeToTable(initialPath, initialNode) {
  const table = _createRouteTable();
  function _addNode(path, node) {
    if (path) {
      if (node.type === NODE_TYPES.NORMAL && !(path.includes("*") || path.includes(":"))) {
        if (node.data) {
          table.static.set(path, node.data);
        }
      } else if (node.type === NODE_TYPES.WILDCARD) {
        table.wildcard.set(path.replace("/**", ""), node.data);
      } else if (node.type === NODE_TYPES.PLACEHOLDER) {
        const subTable = _routerNodeToTable("", node);
        if (node.data) {
          subTable.static.set("/", node.data);
        }
        table.dynamic.set(path.replace(/\/\*|\/:\w+/, ""), subTable);
        return;
      }
    }
    for (const [childPath, child] of node.children.entries()) {
      _addNode(`${path}/${childPath}`.replace("//", "/"), child);
    }
  }
  _addNode(initialPath, initialNode);
  return table;
}

function isPlainObject(value) {
  if (value === null || typeof value !== "object") {
    return false;
  }
  const prototype = Object.getPrototypeOf(value);
  if (prototype !== null && prototype !== Object.prototype && Object.getPrototypeOf(prototype) !== null) {
    return false;
  }
  if (Symbol.iterator in value) {
    return false;
  }
  if (Symbol.toStringTag in value) {
    return Object.prototype.toString.call(value) === "[object Module]";
  }
  return true;
}

function _defu(baseObject, defaults, namespace = ".", merger) {
  if (!isPlainObject(defaults)) {
    return _defu(baseObject, {}, namespace, merger);
  }
  const object = Object.assign({}, defaults);
  for (const key in baseObject) {
    if (key === "__proto__" || key === "constructor") {
      continue;
    }
    const value = baseObject[key];
    if (value === null || value === void 0) {
      continue;
    }
    if (merger && merger(object, key, value, namespace)) {
      continue;
    }
    if (Array.isArray(value) && Array.isArray(object[key])) {
      object[key] = [...value, ...object[key]];
    } else if (isPlainObject(value) && isPlainObject(object[key])) {
      object[key] = _defu(
        value,
        object[key],
        (namespace ? `${namespace}.` : "") + key.toString(),
        merger
      );
    } else {
      object[key] = value;
    }
  }
  return object;
}
function createDefu(merger) {
  return (...arguments_) => (
    // eslint-disable-next-line unicorn/no-array-reduce
    arguments_.reduce((p, c) => _defu(p, c, "", merger), {})
  );
}
const defu = createDefu();
const defuFn = createDefu((object, key, currentValue) => {
  if (object[key] !== void 0 && typeof currentValue === "function") {
    object[key] = currentValue(object[key]);
    return true;
  }
});

function o(n){throw new Error(`${n} is not implemented yet!`)}let i$1 = class i extends EventEmitter{__unenv__={};readableEncoding=null;readableEnded=true;readableFlowing=false;readableHighWaterMark=0;readableLength=0;readableObjectMode=false;readableAborted=false;readableDidRead=false;closed=false;errored=null;readable=false;destroyed=false;static from(e,t){return new i(t)}constructor(e){super();}_read(e){}read(e){}setEncoding(e){return this}pause(){return this}resume(){return this}isPaused(){return  true}unpipe(e){return this}unshift(e,t){}wrap(e){return this}push(e,t){return  false}_destroy(e,t){this.removeAllListeners();}destroy(e){return this.destroyed=true,this._destroy(e),this}pipe(e,t){return {}}compose(e,t){throw new Error("Method not implemented.")}[Symbol.asyncDispose](){return this.destroy(),Promise.resolve()}async*[Symbol.asyncIterator](){throw o("Readable.asyncIterator")}iterator(e){throw o("Readable.iterator")}map(e,t){throw o("Readable.map")}filter(e,t){throw o("Readable.filter")}forEach(e,t){throw o("Readable.forEach")}reduce(e,t,r){throw o("Readable.reduce")}find(e,t){throw o("Readable.find")}findIndex(e,t){throw o("Readable.findIndex")}some(e,t){throw o("Readable.some")}toArray(e){throw o("Readable.toArray")}every(e,t){throw o("Readable.every")}flatMap(e,t){throw o("Readable.flatMap")}drop(e,t){throw o("Readable.drop")}take(e,t){throw o("Readable.take")}asIndexedPairs(e){throw o("Readable.asIndexedPairs")}};let l$1 = class l extends EventEmitter{__unenv__={};writable=true;writableEnded=false;writableFinished=false;writableHighWaterMark=0;writableLength=0;writableObjectMode=false;writableCorked=0;closed=false;errored=null;writableNeedDrain=false;writableAborted=false;destroyed=false;_data;_encoding="utf8";constructor(e){super();}pipe(e,t){return {}}_write(e,t,r){if(this.writableEnded){r&&r();return}if(this._data===void 0)this._data=e;else {const s=typeof this._data=="string"?Buffer$1.from(this._data,this._encoding||t||"utf8"):this._data,a=typeof e=="string"?Buffer$1.from(e,t||this._encoding||"utf8"):e;this._data=Buffer$1.concat([s,a]);}this._encoding=t,r&&r();}_writev(e,t){}_destroy(e,t){}_final(e){}write(e,t,r){const s=typeof t=="string"?this._encoding:"utf8",a=typeof t=="function"?t:typeof r=="function"?r:void 0;return this._write(e,s,a),true}setDefaultEncoding(e){return this}end(e,t,r){const s=typeof e=="function"?e:typeof t=="function"?t:typeof r=="function"?r:void 0;if(this.writableEnded)return s&&s(),this;const a=e===s?void 0:e;if(a){const u=t===s?void 0:t;this.write(a,u,s);}return this.writableEnded=true,this.writableFinished=true,this.emit("close"),this.emit("finish"),this}cork(){}uncork(){}destroy(e){return this.destroyed=true,delete this._data,this.removeAllListeners(),this}compose(e,t){throw new Error("Method not implemented.")}};const c=class{allowHalfOpen=true;_destroy;constructor(e=new i$1,t=new l$1){Object.assign(this,e),Object.assign(this,t),this._destroy=g(e._destroy,t._destroy);}};function _(){return Object.assign(c.prototype,i$1.prototype),Object.assign(c.prototype,l$1.prototype),c}function g(...n){return function(...e){for(const t of n)t(...e);}}const m=_();class A extends m{__unenv__={};bufferSize=0;bytesRead=0;bytesWritten=0;connecting=false;destroyed=false;pending=false;localAddress="";localPort=0;remoteAddress="";remoteFamily="";remotePort=0;autoSelectFamilyAttemptedAddresses=[];readyState="readOnly";constructor(e){super();}write(e,t,r){return  false}connect(e,t,r){return this}end(e,t,r){return this}setEncoding(e){return this}pause(){return this}resume(){return this}setTimeout(e,t){return this}setNoDelay(e){return this}setKeepAlive(e,t){return this}address(){return {}}unref(){return this}ref(){return this}destroySoon(){this.destroy();}resetAndDestroy(){const e=new Error("ERR_SOCKET_CLOSED");return e.code="ERR_SOCKET_CLOSED",this.destroy(e),this}}class y extends i$1{aborted=false;httpVersion="1.1";httpVersionMajor=1;httpVersionMinor=1;complete=true;connection;socket;headers={};trailers={};method="GET";url="/";statusCode=200;statusMessage="";closed=false;errored=null;readable=false;constructor(e){super(),this.socket=this.connection=e||new A;}get rawHeaders(){const e=this.headers,t=[];for(const r in e)if(Array.isArray(e[r]))for(const s of e[r])t.push(r,s);else t.push(r,e[r]);return t}get rawTrailers(){return []}setTimeout(e,t){return this}get headersDistinct(){return p(this.headers)}get trailersDistinct(){return p(this.trailers)}}function p(n){const e={};for(const[t,r]of Object.entries(n))t&&(e[t]=(Array.isArray(r)?r:[r]).filter(Boolean));return e}class w extends l$1{statusCode=200;statusMessage="";upgrading=false;chunkedEncoding=false;shouldKeepAlive=false;useChunkedEncodingByDefault=false;sendDate=false;finished=false;headersSent=false;strictContentLength=false;connection=null;socket=null;req;_headers={};constructor(e){super(),this.req=e;}assignSocket(e){e._httpMessage=this,this.socket=e,this.connection=e,this.emit("socket",e),this._flush();}_flush(){this.flushHeaders();}detachSocket(e){}writeContinue(e){}writeHead(e,t,r){e&&(this.statusCode=e),typeof t=="string"&&(this.statusMessage=t,t=void 0);const s=r||t;if(s&&!Array.isArray(s))for(const a in s)this.setHeader(a,s[a]);return this.headersSent=true,this}writeProcessing(){}setTimeout(e,t){return this}appendHeader(e,t){e=e.toLowerCase();const r=this._headers[e],s=[...Array.isArray(r)?r:[r],...Array.isArray(t)?t:[t]].filter(Boolean);return this._headers[e]=s.length>1?s:s[0],this}setHeader(e,t){return this._headers[e.toLowerCase()]=t,this}setHeaders(e){for(const[t,r]of Object.entries(e))this.setHeader(t,r);return this}getHeader(e){return this._headers[e.toLowerCase()]}getHeaders(){return this._headers}getHeaderNames(){return Object.keys(this._headers)}hasHeader(e){return e.toLowerCase()in this._headers}removeHeader(e){delete this._headers[e.toLowerCase()];}addTrailers(e){}flushHeaders(){}writeEarlyHints(e,t){typeof t=="function"&&t();}}const E=(()=>{const n=function(){};return n.prototype=Object.create(null),n})();function R(n={}){const e=new E,t=Array.isArray(n)||H(n)?n:Object.entries(n);for(const[r,s]of t)if(s){if(e[r]===void 0){e[r]=s;continue}e[r]=[...Array.isArray(e[r])?e[r]:[e[r]],...Array.isArray(s)?s:[s]];}return e}function H(n){return typeof n?.entries=="function"}function v(n={}){if(n instanceof Headers)return n;const e=new Headers;for(const[t,r]of Object.entries(n))if(r!==void 0){if(Array.isArray(r)){for(const s of r)e.append(t,String(s));continue}e.set(t,String(r));}return e}const S=new Set([101,204,205,304]);async function b(n,e){const t=new y,r=new w(t);t.url=e.url?.toString()||"/";let s;if(!t.url.startsWith("/")){const d=new URL(t.url);s=d.host,t.url=d.pathname+d.search+d.hash;}t.method=e.method||"GET",t.headers=R(e.headers||{}),t.headers.host||(t.headers.host=e.host||s||"localhost"),t.connection.encrypted=t.connection.encrypted||e.protocol==="https",t.body=e.body||null,t.__unenv__=e.context,await n(t,r);let a=r._data;(S.has(r.statusCode)||t.method.toUpperCase()==="HEAD")&&(a=null,delete r._headers["content-length"]);const u={status:r.statusCode,statusText:r.statusMessage,headers:r._headers,body:a};return t.destroy(),r.destroy(),u}async function C(n,e,t={}){try{const r=await b(n,{url:e,...t});return new Response(r.body,{status:r.status,statusText:r.statusText,headers:v(r.headers)})}catch(r){return new Response(r.toString(),{status:Number.parseInt(r.statusCode||r.code)||500,statusText:r.statusText})}}

function hasProp(obj, prop) {
  try {
    return prop in obj;
  } catch {
    return false;
  }
}

class H3Error extends Error {
  static __h3_error__ = true;
  statusCode = 500;
  fatal = false;
  unhandled = false;
  statusMessage;
  data;
  cause;
  constructor(message, opts = {}) {
    super(message, opts);
    if (opts.cause && !this.cause) {
      this.cause = opts.cause;
    }
  }
  toJSON() {
    const obj = {
      message: this.message,
      statusCode: sanitizeStatusCode(this.statusCode, 500)
    };
    if (this.statusMessage) {
      obj.statusMessage = sanitizeStatusMessage(this.statusMessage);
    }
    if (this.data !== void 0) {
      obj.data = this.data;
    }
    return obj;
  }
}
function createError$1(input) {
  if (typeof input === "string") {
    return new H3Error(input);
  }
  if (isError(input)) {
    return input;
  }
  const err = new H3Error(input.message ?? input.statusMessage ?? "", {
    cause: input.cause || input
  });
  if (hasProp(input, "stack")) {
    try {
      Object.defineProperty(err, "stack", {
        get() {
          return input.stack;
        }
      });
    } catch {
      try {
        err.stack = input.stack;
      } catch {
      }
    }
  }
  if (input.data) {
    err.data = input.data;
  }
  if (input.statusCode) {
    err.statusCode = sanitizeStatusCode(input.statusCode, err.statusCode);
  } else if (input.status) {
    err.statusCode = sanitizeStatusCode(input.status, err.statusCode);
  }
  if (input.statusMessage) {
    err.statusMessage = input.statusMessage;
  } else if (input.statusText) {
    err.statusMessage = input.statusText;
  }
  if (err.statusMessage) {
    const originalMessage = err.statusMessage;
    const sanitizedMessage = sanitizeStatusMessage(err.statusMessage);
    if (sanitizedMessage !== originalMessage) {
      console.warn(
        "[h3] Please prefer using `message` for longer error messages instead of `statusMessage`. In the future, `statusMessage` will be sanitized by default."
      );
    }
  }
  if (input.fatal !== void 0) {
    err.fatal = input.fatal;
  }
  if (input.unhandled !== void 0) {
    err.unhandled = input.unhandled;
  }
  return err;
}
function sendError(event, error, debug) {
  if (event.handled) {
    return;
  }
  const h3Error = isError(error) ? error : createError$1(error);
  const responseBody = {
    statusCode: h3Error.statusCode,
    statusMessage: h3Error.statusMessage,
    stack: [],
    data: h3Error.data
  };
  if (debug) {
    responseBody.stack = (h3Error.stack || "").split("\n").map((l) => l.trim());
  }
  if (event.handled) {
    return;
  }
  const _code = Number.parseInt(h3Error.statusCode);
  setResponseStatus(event, _code, h3Error.statusMessage);
  event.node.res.setHeader("content-type", MIMES.json);
  event.node.res.end(JSON.stringify(responseBody, void 0, 2));
}
function isError(input) {
  return input?.constructor?.__h3_error__ === true;
}

function parse(multipartBodyBuffer, boundary) {
  let lastline = "";
  let state = 0 /* INIT */;
  let buffer = [];
  const allParts = [];
  let currentPartHeaders = [];
  for (let i = 0; i < multipartBodyBuffer.length; i++) {
    const prevByte = i > 0 ? multipartBodyBuffer[i - 1] : null;
    const currByte = multipartBodyBuffer[i];
    const newLineChar = currByte === 10 || currByte === 13;
    if (!newLineChar) {
      lastline += String.fromCodePoint(currByte);
    }
    const newLineDetected = currByte === 10 && prevByte === 13;
    if (0 /* INIT */ === state && newLineDetected) {
      if ("--" + boundary === lastline) {
        state = 1 /* READING_HEADERS */;
      }
      lastline = "";
    } else if (1 /* READING_HEADERS */ === state && newLineDetected) {
      if (lastline.length > 0) {
        const i2 = lastline.indexOf(":");
        if (i2 > 0) {
          const name = lastline.slice(0, i2).toLowerCase();
          const value = lastline.slice(i2 + 1).trim();
          currentPartHeaders.push([name, value]);
        }
      } else {
        state = 2 /* READING_DATA */;
        buffer = [];
      }
      lastline = "";
    } else if (2 /* READING_DATA */ === state) {
      if (lastline.length > boundary.length + 4) {
        lastline = "";
      }
      if ("--" + boundary === lastline) {
        const j = buffer.length - lastline.length;
        const part = buffer.slice(0, j - 1);
        allParts.push(process$1(part, currentPartHeaders));
        buffer = [];
        currentPartHeaders = [];
        lastline = "";
        state = 3 /* READING_PART_SEPARATOR */;
      } else {
        buffer.push(currByte);
      }
      if (newLineDetected) {
        lastline = "";
      }
    } else if (3 /* READING_PART_SEPARATOR */ === state && newLineDetected) {
      state = 1 /* READING_HEADERS */;
    }
  }
  return allParts;
}
function process$1(data, headers) {
  const dataObj = {};
  const contentDispositionHeader = headers.find((h) => h[0] === "content-disposition")?.[1] || "";
  for (const i of contentDispositionHeader.split(";")) {
    const s = i.split("=");
    if (s.length !== 2) {
      continue;
    }
    const key = (s[0] || "").trim();
    if (key === "name" || key === "filename") {
      const _value = (s[1] || "").trim().replace(/"/g, "");
      dataObj[key] = Buffer.from(_value, "latin1").toString("utf8");
    }
  }
  const contentType = headers.find((h) => h[0] === "content-type")?.[1] || "";
  if (contentType) {
    dataObj.type = contentType;
  }
  dataObj.data = Buffer.from(data);
  return dataObj;
}

function getQuery(event) {
  return getQuery$1(event.path || "");
}
function getRouterParams(event, opts = {}) {
  let params = event.context.params || {};
  if (opts.decode) {
    params = { ...params };
    for (const key in params) {
      params[key] = decode$1(params[key]);
    }
  }
  return params;
}
function getRouterParam(event, name, opts = {}) {
  const params = getRouterParams(event, opts);
  return params[name];
}
function getMethod(event, defaultMethod = "GET") {
  return (event.node.req.method || defaultMethod).toUpperCase();
}
function isMethod(event, expected, allowHead) {
  if (typeof expected === "string") {
    if (event.method === expected) {
      return true;
    }
  } else if (expected.includes(event.method)) {
    return true;
  }
  return false;
}
function assertMethod(event, expected, allowHead) {
  if (!isMethod(event, expected)) {
    throw createError$1({
      statusCode: 405,
      statusMessage: "HTTP method is not allowed."
    });
  }
}
function getRequestHeaders(event) {
  const _headers = {};
  for (const key in event.node.req.headers) {
    const val = event.node.req.headers[key];
    _headers[key] = Array.isArray(val) ? val.filter(Boolean).join(", ") : val;
  }
  return _headers;
}
function getRequestHeader(event, name) {
  const headers = getRequestHeaders(event);
  const value = headers[name.toLowerCase()];
  return value;
}
const getHeader = getRequestHeader;
function getRequestHost(event, opts = {}) {
  if (opts.xForwardedHost) {
    const xForwardedHost = event.node.req.headers["x-forwarded-host"];
    if (xForwardedHost) {
      return xForwardedHost;
    }
  }
  return event.node.req.headers.host || "localhost";
}
function getRequestProtocol(event, opts = {}) {
  if (opts.xForwardedProto !== false && event.node.req.headers["x-forwarded-proto"] === "https") {
    return "https";
  }
  return event.node.req.connection?.encrypted ? "https" : "http";
}
function getRequestURL(event, opts = {}) {
  const host = getRequestHost(event, opts);
  const protocol = getRequestProtocol(event, opts);
  const path = (event.node.req.originalUrl || event.path).replace(
    /^[/\\]+/g,
    "/"
  );
  return new URL(path, `${protocol}://${host}`);
}
function getRequestIP(event, opts = {}) {
  if (event.context.clientAddress) {
    return event.context.clientAddress;
  }
  if (opts.xForwardedFor) {
    const xForwardedFor = getRequestHeader(event, "x-forwarded-for")?.split(",").shift()?.trim();
    if (xForwardedFor) {
      return xForwardedFor;
    }
  }
  if (event.node.req.socket.remoteAddress) {
    return event.node.req.socket.remoteAddress;
  }
}

const RawBodySymbol = Symbol.for("h3RawBody");
const ParsedBodySymbol = Symbol.for("h3ParsedBody");
const PayloadMethods$1 = ["PATCH", "POST", "PUT", "DELETE"];
function readRawBody(event, encoding = "utf8") {
  assertMethod(event, PayloadMethods$1);
  const _rawBody = event._requestBody || event.web?.request?.body || event.node.req[RawBodySymbol] || event.node.req.rawBody || event.node.req.body;
  if (_rawBody) {
    const promise2 = Promise.resolve(_rawBody).then((_resolved) => {
      if (Buffer.isBuffer(_resolved)) {
        return _resolved;
      }
      if (typeof _resolved.pipeTo === "function") {
        return new Promise((resolve, reject) => {
          const chunks = [];
          _resolved.pipeTo(
            new WritableStream({
              write(chunk) {
                chunks.push(chunk);
              },
              close() {
                resolve(Buffer.concat(chunks));
              },
              abort(reason) {
                reject(reason);
              }
            })
          ).catch(reject);
        });
      } else if (typeof _resolved.pipe === "function") {
        return new Promise((resolve, reject) => {
          const chunks = [];
          _resolved.on("data", (chunk) => {
            chunks.push(chunk);
          }).on("end", () => {
            resolve(Buffer.concat(chunks));
          }).on("error", reject);
        });
      }
      if (_resolved.constructor === Object) {
        return Buffer.from(JSON.stringify(_resolved));
      }
      if (_resolved instanceof URLSearchParams) {
        return Buffer.from(_resolved.toString());
      }
      if (_resolved instanceof FormData) {
        return new Response(_resolved).bytes().then((uint8arr) => Buffer.from(uint8arr));
      }
      return Buffer.from(_resolved);
    });
    return encoding ? promise2.then((buff) => buff.toString(encoding)) : promise2;
  }
  if (!Number.parseInt(event.node.req.headers["content-length"] || "") && !String(event.node.req.headers["transfer-encoding"] ?? "").split(",").map((e) => e.trim()).filter(Boolean).includes("chunked")) {
    return Promise.resolve(void 0);
  }
  const promise = event.node.req[RawBodySymbol] = new Promise(
    (resolve, reject) => {
      const bodyData = [];
      event.node.req.on("error", (err) => {
        reject(err);
      }).on("data", (chunk) => {
        bodyData.push(chunk);
      }).on("end", () => {
        resolve(Buffer.concat(bodyData));
      });
    }
  );
  const result = encoding ? promise.then((buff) => buff.toString(encoding)) : promise;
  return result;
}
async function readBody(event, options = {}) {
  const request = event.node.req;
  if (hasProp(request, ParsedBodySymbol)) {
    return request[ParsedBodySymbol];
  }
  const contentType = request.headers["content-type"] || "";
  const body = await readRawBody(event);
  let parsed;
  if (contentType === "application/json") {
    parsed = _parseJSON(body, options.strict ?? true);
  } else if (contentType.startsWith("application/x-www-form-urlencoded")) {
    parsed = _parseURLEncodedBody(body);
  } else if (contentType.startsWith("text/")) {
    parsed = body;
  } else {
    parsed = _parseJSON(body, options.strict ?? false);
  }
  request[ParsedBodySymbol] = parsed;
  return parsed;
}
async function readMultipartFormData(event) {
  const contentType = getRequestHeader(event, "content-type");
  if (!contentType || !contentType.startsWith("multipart/form-data")) {
    return;
  }
  const boundary = contentType.match(/boundary=([^;]*)(;|$)/i)?.[1];
  if (!boundary) {
    return;
  }
  const body = await readRawBody(event, false);
  if (!body) {
    return;
  }
  return parse(body, boundary);
}
function getRequestWebStream(event) {
  if (!PayloadMethods$1.includes(event.method)) {
    return;
  }
  const bodyStream = event.web?.request?.body || event._requestBody;
  if (bodyStream) {
    return bodyStream;
  }
  const _hasRawBody = RawBodySymbol in event.node.req || "rawBody" in event.node.req || "body" in event.node.req || "__unenv__" in event.node.req;
  if (_hasRawBody) {
    return new ReadableStream({
      async start(controller) {
        const _rawBody = await readRawBody(event, false);
        if (_rawBody) {
          controller.enqueue(_rawBody);
        }
        controller.close();
      }
    });
  }
  return new ReadableStream({
    start: (controller) => {
      event.node.req.on("data", (chunk) => {
        controller.enqueue(chunk);
      });
      event.node.req.on("end", () => {
        controller.close();
      });
      event.node.req.on("error", (err) => {
        controller.error(err);
      });
    }
  });
}
function _parseJSON(body = "", strict) {
  if (!body) {
    return void 0;
  }
  try {
    return destr(body, { strict });
  } catch {
    throw createError$1({
      statusCode: 400,
      statusMessage: "Bad Request",
      message: "Invalid JSON body"
    });
  }
}
function _parseURLEncodedBody(body) {
  const form = new URLSearchParams(body);
  const parsedForm = /* @__PURE__ */ Object.create(null);
  for (const [key, value] of form.entries()) {
    if (hasProp(parsedForm, key)) {
      if (!Array.isArray(parsedForm[key])) {
        parsedForm[key] = [parsedForm[key]];
      }
      parsedForm[key].push(value);
    } else {
      parsedForm[key] = value;
    }
  }
  return parsedForm;
}

function handleCacheHeaders(event, opts) {
  const cacheControls = ["public", ...opts.cacheControls || []];
  let cacheMatched = false;
  if (opts.maxAge !== void 0) {
    cacheControls.push(`max-age=${+opts.maxAge}`, `s-maxage=${+opts.maxAge}`);
  }
  if (opts.modifiedTime) {
    const modifiedTime = new Date(opts.modifiedTime);
    const ifModifiedSince = event.node.req.headers["if-modified-since"];
    event.node.res.setHeader("last-modified", modifiedTime.toUTCString());
    if (ifModifiedSince && new Date(ifModifiedSince) >= modifiedTime) {
      cacheMatched = true;
    }
  }
  if (opts.etag) {
    event.node.res.setHeader("etag", opts.etag);
    const ifNonMatch = event.node.req.headers["if-none-match"];
    if (ifNonMatch === opts.etag) {
      cacheMatched = true;
    }
  }
  event.node.res.setHeader("cache-control", cacheControls.join(", "));
  if (cacheMatched) {
    event.node.res.statusCode = 304;
    if (!event.handled) {
      event.node.res.end();
    }
    return true;
  }
  return false;
}

const MIMES = {
  html: "text/html",
  json: "application/json"
};

const DISALLOWED_STATUS_CHARS = /[^\u0009\u0020-\u007E]/g;
function sanitizeStatusMessage(statusMessage = "") {
  return statusMessage.replace(DISALLOWED_STATUS_CHARS, "");
}
function sanitizeStatusCode(statusCode, defaultStatusCode = 200) {
  if (!statusCode) {
    return defaultStatusCode;
  }
  if (typeof statusCode === "string") {
    statusCode = Number.parseInt(statusCode, 10);
  }
  if (statusCode < 100 || statusCode > 999) {
    return defaultStatusCode;
  }
  return statusCode;
}

function getDistinctCookieKey(name, opts) {
  return [name, opts.domain || "", opts.path || "/"].join(";");
}

function parseCookies(event) {
  return parse$1(event.node.req.headers.cookie || "");
}
function getCookie(event, name) {
  return parseCookies(event)[name];
}
function setCookie(event, name, value, serializeOptions = {}) {
  if (!serializeOptions.path) {
    serializeOptions = { path: "/", ...serializeOptions };
  }
  const newCookie = serialize$1(name, value, serializeOptions);
  const currentCookies = splitCookiesString(
    event.node.res.getHeader("set-cookie")
  );
  if (currentCookies.length === 0) {
    event.node.res.setHeader("set-cookie", newCookie);
    return;
  }
  const newCookieKey = getDistinctCookieKey(name, serializeOptions);
  event.node.res.removeHeader("set-cookie");
  for (const cookie of currentCookies) {
    const parsed = parseSetCookie(cookie);
    const key = getDistinctCookieKey(parsed.name, parsed);
    if (key === newCookieKey) {
      continue;
    }
    event.node.res.appendHeader("set-cookie", cookie);
  }
  event.node.res.appendHeader("set-cookie", newCookie);
}
function deleteCookie(event, name, serializeOptions) {
  setCookie(event, name, "", {
    ...serializeOptions,
    maxAge: 0
  });
}
function splitCookiesString(cookiesString) {
  if (Array.isArray(cookiesString)) {
    return cookiesString.flatMap((c) => splitCookiesString(c));
  }
  if (typeof cookiesString !== "string") {
    return [];
  }
  const cookiesStrings = [];
  let pos = 0;
  let start;
  let ch;
  let lastComma;
  let nextStart;
  let cookiesSeparatorFound;
  const skipWhitespace = () => {
    while (pos < cookiesString.length && /\s/.test(cookiesString.charAt(pos))) {
      pos += 1;
    }
    return pos < cookiesString.length;
  };
  const notSpecialChar = () => {
    ch = cookiesString.charAt(pos);
    return ch !== "=" && ch !== ";" && ch !== ",";
  };
  while (pos < cookiesString.length) {
    start = pos;
    cookiesSeparatorFound = false;
    while (skipWhitespace()) {
      ch = cookiesString.charAt(pos);
      if (ch === ",") {
        lastComma = pos;
        pos += 1;
        skipWhitespace();
        nextStart = pos;
        while (pos < cookiesString.length && notSpecialChar()) {
          pos += 1;
        }
        if (pos < cookiesString.length && cookiesString.charAt(pos) === "=") {
          cookiesSeparatorFound = true;
          pos = nextStart;
          cookiesStrings.push(cookiesString.slice(start, lastComma));
          start = pos;
        } else {
          pos = lastComma + 1;
        }
      } else {
        pos += 1;
      }
    }
    if (!cookiesSeparatorFound || pos >= cookiesString.length) {
      cookiesStrings.push(cookiesString.slice(start));
    }
  }
  return cookiesStrings;
}

const defer = typeof setImmediate === "undefined" ? (fn) => fn() : setImmediate;
function send(event, data, type) {
  if (type) {
    defaultContentType(event, type);
  }
  return new Promise((resolve) => {
    defer(() => {
      if (!event.handled) {
        event.node.res.end(data);
      }
      resolve();
    });
  });
}
function sendNoContent(event, code) {
  if (event.handled) {
    return;
  }
  if (!code && event.node.res.statusCode !== 200) {
    code = event.node.res.statusCode;
  }
  const _code = sanitizeStatusCode(code, 204);
  if (_code === 204) {
    event.node.res.removeHeader("content-length");
  }
  event.node.res.writeHead(_code);
  event.node.res.end();
}
function setResponseStatus(event, code, text) {
  if (code) {
    event.node.res.statusCode = sanitizeStatusCode(
      code,
      event.node.res.statusCode
    );
  }
  if (text) {
    event.node.res.statusMessage = sanitizeStatusMessage(text);
  }
}
function defaultContentType(event, type) {
  if (type && event.node.res.statusCode !== 304 && !event.node.res.getHeader("content-type")) {
    event.node.res.setHeader("content-type", type);
  }
}
function sendRedirect(event, location, code = 302) {
  event.node.res.statusCode = sanitizeStatusCode(
    code,
    event.node.res.statusCode
  );
  event.node.res.setHeader("location", location);
  const encodedLoc = location.replace(/"/g, "%22");
  const html = `<!DOCTYPE html><html><head><meta http-equiv="refresh" content="0; url=${encodedLoc}"></head></html>`;
  return send(event, html, MIMES.html);
}
function getResponseHeader(event, name) {
  return event.node.res.getHeader(name);
}
function setResponseHeaders(event, headers) {
  for (const [name, value] of Object.entries(headers)) {
    event.node.res.setHeader(
      name,
      value
    );
  }
}
const setHeaders = setResponseHeaders;
function setResponseHeader(event, name, value) {
  event.node.res.setHeader(name, value);
}
const setHeader = setResponseHeader;
function appendResponseHeader(event, name, value) {
  let current = event.node.res.getHeader(name);
  if (!current) {
    event.node.res.setHeader(name, value);
    return;
  }
  if (!Array.isArray(current)) {
    current = [current.toString()];
  }
  event.node.res.setHeader(name, [...current, value]);
}
function removeResponseHeader(event, name) {
  return event.node.res.removeHeader(name);
}
function isStream(data) {
  if (!data || typeof data !== "object") {
    return false;
  }
  if (typeof data.pipe === "function") {
    if (typeof data._read === "function") {
      return true;
    }
    if (typeof data.abort === "function") {
      return true;
    }
  }
  if (typeof data.pipeTo === "function") {
    return true;
  }
  return false;
}
function isWebResponse(data) {
  return typeof Response !== "undefined" && data instanceof Response;
}
function sendStream(event, stream) {
  if (!stream || typeof stream !== "object") {
    throw new Error("[h3] Invalid stream provided.");
  }
  event.node.res._data = stream;
  if (!event.node.res.socket) {
    event._handled = true;
    return Promise.resolve();
  }
  if (hasProp(stream, "pipeTo") && typeof stream.pipeTo === "function") {
    return stream.pipeTo(
      new WritableStream({
        write(chunk) {
          event.node.res.write(chunk);
        }
      })
    ).then(() => {
      event.node.res.end();
    });
  }
  if (hasProp(stream, "pipe") && typeof stream.pipe === "function") {
    return new Promise((resolve, reject) => {
      stream.pipe(event.node.res);
      if (stream.on) {
        stream.on("end", () => {
          event.node.res.end();
          resolve();
        });
        stream.on("error", (error) => {
          reject(error);
        });
      }
      event.node.res.on("close", () => {
        if (stream.abort) {
          stream.abort();
        }
      });
    });
  }
  throw new Error("[h3] Invalid or incompatible stream provided.");
}
function sendWebResponse(event, response) {
  for (const [key, value] of response.headers) {
    if (key === "set-cookie") {
      event.node.res.appendHeader(key, splitCookiesString(value));
    } else {
      event.node.res.setHeader(key, value);
    }
  }
  if (response.status) {
    event.node.res.statusCode = sanitizeStatusCode(
      response.status,
      event.node.res.statusCode
    );
  }
  if (response.statusText) {
    event.node.res.statusMessage = sanitizeStatusMessage(response.statusText);
  }
  if (response.redirected) {
    event.node.res.setHeader("location", response.url);
  }
  if (!response.body) {
    event.node.res.end();
    return;
  }
  return sendStream(event, response.body);
}

const PayloadMethods = /* @__PURE__ */ new Set(["PATCH", "POST", "PUT", "DELETE"]);
const ignoredHeaders = /* @__PURE__ */ new Set([
  "transfer-encoding",
  "accept-encoding",
  "connection",
  "keep-alive",
  "upgrade",
  "expect",
  "host",
  "accept"
]);
async function proxyRequest(event, target, opts = {}) {
  let body;
  let duplex;
  if (PayloadMethods.has(event.method)) {
    if (opts.streamRequest) {
      body = getRequestWebStream(event);
      duplex = "half";
    } else {
      body = await readRawBody(event, false).catch(() => void 0);
    }
  }
  const method = opts.fetchOptions?.method || event.method;
  const fetchHeaders = mergeHeaders$1(
    getProxyRequestHeaders(event, { host: target.startsWith("/") }),
    opts.fetchOptions?.headers,
    opts.headers
  );
  return sendProxy(event, target, {
    ...opts,
    fetchOptions: {
      method,
      body,
      duplex,
      ...opts.fetchOptions,
      headers: fetchHeaders
    }
  });
}
async function sendProxy(event, target, opts = {}) {
  let response;
  try {
    response = await _getFetch(opts.fetch)(target, {
      headers: opts.headers,
      ignoreResponseError: true,
      // make $ofetch.raw transparent
      ...opts.fetchOptions
    });
  } catch (error) {
    throw createError$1({
      status: 502,
      statusMessage: "Bad Gateway",
      cause: error
    });
  }
  event.node.res.statusCode = sanitizeStatusCode(
    response.status,
    event.node.res.statusCode
  );
  event.node.res.statusMessage = sanitizeStatusMessage(response.statusText);
  const cookies = [];
  for (const [key, value] of response.headers.entries()) {
    if (key === "content-encoding") {
      continue;
    }
    if (key === "content-length") {
      continue;
    }
    if (key === "set-cookie") {
      cookies.push(...splitCookiesString(value));
      continue;
    }
    event.node.res.setHeader(key, value);
  }
  if (cookies.length > 0) {
    event.node.res.setHeader(
      "set-cookie",
      cookies.map((cookie) => {
        if (opts.cookieDomainRewrite) {
          cookie = rewriteCookieProperty(
            cookie,
            opts.cookieDomainRewrite,
            "domain"
          );
        }
        if (opts.cookiePathRewrite) {
          cookie = rewriteCookieProperty(
            cookie,
            opts.cookiePathRewrite,
            "path"
          );
        }
        return cookie;
      })
    );
  }
  if (opts.onResponse) {
    await opts.onResponse(event, response);
  }
  if (response._data !== void 0) {
    return response._data;
  }
  if (event.handled) {
    return;
  }
  if (opts.sendStream === false) {
    const data = new Uint8Array(await response.arrayBuffer());
    return event.node.res.end(data);
  }
  if (response.body) {
    for await (const chunk of response.body) {
      event.node.res.write(chunk);
    }
  }
  return event.node.res.end();
}
function getProxyRequestHeaders(event, opts) {
  const headers = /* @__PURE__ */ Object.create(null);
  const reqHeaders = getRequestHeaders(event);
  for (const name in reqHeaders) {
    if (!ignoredHeaders.has(name) || name === "host" && opts?.host) {
      headers[name] = reqHeaders[name];
    }
  }
  return headers;
}
function fetchWithEvent(event, req, init, options) {
  return _getFetch(options?.fetch)(req, {
    ...init,
    context: init?.context || event.context,
    headers: {
      ...getProxyRequestHeaders(event, {
        host: typeof req === "string" && req.startsWith("/")
      }),
      ...init?.headers
    }
  });
}
function _getFetch(_fetch) {
  if (_fetch) {
    return _fetch;
  }
  if (globalThis.fetch) {
    return globalThis.fetch;
  }
  throw new Error(
    "fetch is not available. Try importing `node-fetch-native/polyfill` for Node.js."
  );
}
function rewriteCookieProperty(header, map, property) {
  const _map = typeof map === "string" ? { "*": map } : map;
  return header.replace(
    new RegExp(`(;\\s*${property}=)([^;]+)`, "gi"),
    (match, prefix, previousValue) => {
      let newValue;
      if (previousValue in _map) {
        newValue = _map[previousValue];
      } else if ("*" in _map) {
        newValue = _map["*"];
      } else {
        return match;
      }
      return newValue ? prefix + newValue : "";
    }
  );
}
function mergeHeaders$1(defaults, ...inputs) {
  const _inputs = inputs.filter(Boolean);
  if (_inputs.length === 0) {
    return defaults;
  }
  const merged = new Headers(defaults);
  for (const input of _inputs) {
    const entries = Array.isArray(input) ? input : typeof input.entries === "function" ? input.entries() : Object.entries(input);
    for (const [key, value] of entries) {
      if (value !== void 0) {
        merged.set(key, value);
      }
    }
  }
  return merged;
}

class H3Event {
  "__is_event__" = true;
  // Context
  node;
  // Node
  web;
  // Web
  context = {};
  // Shared
  // Request
  _method;
  _path;
  _headers;
  _requestBody;
  // Response
  _handled = false;
  // Hooks
  _onBeforeResponseCalled;
  _onAfterResponseCalled;
  constructor(req, res) {
    this.node = { req, res };
  }
  // --- Request ---
  get method() {
    if (!this._method) {
      this._method = (this.node.req.method || "GET").toUpperCase();
    }
    return this._method;
  }
  get path() {
    return this._path || this.node.req.url || "/";
  }
  get headers() {
    if (!this._headers) {
      this._headers = _normalizeNodeHeaders(this.node.req.headers);
    }
    return this._headers;
  }
  // --- Respoonse ---
  get handled() {
    return this._handled || this.node.res.writableEnded || this.node.res.headersSent;
  }
  respondWith(response) {
    return Promise.resolve(response).then(
      (_response) => sendWebResponse(this, _response)
    );
  }
  // --- Utils ---
  toString() {
    return `[${this.method}] ${this.path}`;
  }
  toJSON() {
    return this.toString();
  }
  // --- Deprecated ---
  /** @deprecated Please use `event.node.req` instead. */
  get req() {
    return this.node.req;
  }
  /** @deprecated Please use `event.node.res` instead. */
  get res() {
    return this.node.res;
  }
}
function isEvent(input) {
  return hasProp(input, "__is_event__");
}
function createEvent(req, res) {
  return new H3Event(req, res);
}
function _normalizeNodeHeaders(nodeHeaders) {
  const headers = new Headers();
  for (const [name, value] of Object.entries(nodeHeaders)) {
    if (Array.isArray(value)) {
      for (const item of value) {
        headers.append(name, item);
      }
    } else if (value) {
      headers.set(name, value);
    }
  }
  return headers;
}

function defineEventHandler(handler) {
  if (typeof handler === "function") {
    handler.__is_handler__ = true;
    return handler;
  }
  const _hooks = {
    onRequest: _normalizeArray(handler.onRequest),
    onBeforeResponse: _normalizeArray(handler.onBeforeResponse)
  };
  const _handler = (event) => {
    return _callHandler(event, handler.handler, _hooks);
  };
  _handler.__is_handler__ = true;
  _handler.__resolve__ = handler.handler.__resolve__;
  _handler.__websocket__ = handler.websocket;
  return _handler;
}
function _normalizeArray(input) {
  return input ? Array.isArray(input) ? input : [input] : void 0;
}
async function _callHandler(event, handler, hooks) {
  if (hooks.onRequest) {
    for (const hook of hooks.onRequest) {
      await hook(event);
      if (event.handled) {
        return;
      }
    }
  }
  const body = await handler(event);
  const response = { body };
  if (hooks.onBeforeResponse) {
    for (const hook of hooks.onBeforeResponse) {
      await hook(event, response);
    }
  }
  return response.body;
}
const eventHandler = defineEventHandler;
function isEventHandler(input) {
  return hasProp(input, "__is_handler__");
}
function toEventHandler(input, _, _route) {
  if (!isEventHandler(input)) {
    console.warn(
      "[h3] Implicit event handler conversion is deprecated. Use `eventHandler()` or `fromNodeMiddleware()` to define event handlers.",
      _route && _route !== "/" ? `
     Route: ${_route}` : "",
      `
     Handler: ${input}`
    );
  }
  return input;
}
function defineLazyEventHandler(factory) {
  let _promise;
  let _resolved;
  const resolveHandler = () => {
    if (_resolved) {
      return Promise.resolve(_resolved);
    }
    if (!_promise) {
      _promise = Promise.resolve(factory()).then((r) => {
        const handler2 = r.default || r;
        if (typeof handler2 !== "function") {
          throw new TypeError(
            "Invalid lazy handler result. It should be a function:",
            handler2
          );
        }
        _resolved = { handler: toEventHandler(r.default || r) };
        return _resolved;
      });
    }
    return _promise;
  };
  const handler = eventHandler((event) => {
    if (_resolved) {
      return _resolved.handler(event);
    }
    return resolveHandler().then((r) => r.handler(event));
  });
  handler.__resolve__ = resolveHandler;
  return handler;
}
const lazyEventHandler = defineLazyEventHandler;

function createApp(options = {}) {
  const stack = [];
  const handler = createAppEventHandler(stack, options);
  const resolve = createResolver(stack);
  handler.__resolve__ = resolve;
  const getWebsocket = cachedFn(() => websocketOptions(resolve, options));
  const app = {
    // @ts-expect-error
    use: (arg1, arg2, arg3) => use(app, arg1, arg2, arg3),
    resolve,
    handler,
    stack,
    options,
    get websocket() {
      return getWebsocket();
    }
  };
  return app;
}
function use(app, arg1, arg2, arg3) {
  if (Array.isArray(arg1)) {
    for (const i of arg1) {
      use(app, i, arg2, arg3);
    }
  } else if (Array.isArray(arg2)) {
    for (const i of arg2) {
      use(app, arg1, i, arg3);
    }
  } else if (typeof arg1 === "string") {
    app.stack.push(
      normalizeLayer({ ...arg3, route: arg1, handler: arg2 })
    );
  } else if (typeof arg1 === "function") {
    app.stack.push(normalizeLayer({ ...arg2, handler: arg1 }));
  } else {
    app.stack.push(normalizeLayer({ ...arg1 }));
  }
  return app;
}
function createAppEventHandler(stack, options) {
  const spacing = options.debug ? 2 : void 0;
  return eventHandler(async (event) => {
    event.node.req.originalUrl = event.node.req.originalUrl || event.node.req.url || "/";
    const _reqPath = event._path || event.node.req.url || "/";
    let _layerPath;
    if (options.onRequest) {
      await options.onRequest(event);
    }
    for (const layer of stack) {
      if (layer.route.length > 1) {
        if (!_reqPath.startsWith(layer.route)) {
          continue;
        }
        _layerPath = _reqPath.slice(layer.route.length) || "/";
      } else {
        _layerPath = _reqPath;
      }
      if (layer.match && !layer.match(_layerPath, event)) {
        continue;
      }
      event._path = _layerPath;
      event.node.req.url = _layerPath;
      const val = await layer.handler(event);
      const _body = val === void 0 ? void 0 : await val;
      if (_body !== void 0) {
        const _response = { body: _body };
        if (options.onBeforeResponse) {
          event._onBeforeResponseCalled = true;
          await options.onBeforeResponse(event, _response);
        }
        await handleHandlerResponse(event, _response.body, spacing);
        if (options.onAfterResponse) {
          event._onAfterResponseCalled = true;
          await options.onAfterResponse(event, _response);
        }
        return;
      }
      if (event.handled) {
        if (options.onAfterResponse) {
          event._onAfterResponseCalled = true;
          await options.onAfterResponse(event, void 0);
        }
        return;
      }
    }
    if (!event.handled) {
      throw createError$1({
        statusCode: 404,
        statusMessage: `Cannot find any path matching ${event.path || "/"}.`
      });
    }
    if (options.onAfterResponse) {
      event._onAfterResponseCalled = true;
      await options.onAfterResponse(event, void 0);
    }
  });
}
function createResolver(stack) {
  return async (path) => {
    let _layerPath;
    for (const layer of stack) {
      if (layer.route === "/" && !layer.handler.__resolve__) {
        continue;
      }
      if (!path.startsWith(layer.route)) {
        continue;
      }
      _layerPath = path.slice(layer.route.length) || "/";
      if (layer.match && !layer.match(_layerPath, void 0)) {
        continue;
      }
      let res = { route: layer.route, handler: layer.handler };
      if (res.handler.__resolve__) {
        const _res = await res.handler.__resolve__(_layerPath);
        if (!_res) {
          continue;
        }
        res = {
          ...res,
          ..._res,
          route: joinURL(res.route || "/", _res.route || "/")
        };
      }
      return res;
    }
  };
}
function normalizeLayer(input) {
  let handler = input.handler;
  if (handler.handler) {
    handler = handler.handler;
  }
  if (input.lazy) {
    handler = lazyEventHandler(handler);
  } else if (!isEventHandler(handler)) {
    handler = toEventHandler(handler, void 0, input.route);
  }
  return {
    route: withoutTrailingSlash(input.route),
    match: input.match,
    handler
  };
}
function handleHandlerResponse(event, val, jsonSpace) {
  if (val === null) {
    return sendNoContent(event);
  }
  if (val) {
    if (isWebResponse(val)) {
      return sendWebResponse(event, val);
    }
    if (isStream(val)) {
      return sendStream(event, val);
    }
    if (val.buffer) {
      return send(event, val);
    }
    if (val.arrayBuffer && typeof val.arrayBuffer === "function") {
      return val.arrayBuffer().then((arrayBuffer) => {
        return send(event, Buffer.from(arrayBuffer), val.type);
      });
    }
    if (val instanceof Error) {
      throw createError$1(val);
    }
    if (typeof val.end === "function") {
      return true;
    }
  }
  const valType = typeof val;
  if (valType === "string") {
    return send(event, val, MIMES.html);
  }
  if (valType === "object" || valType === "boolean" || valType === "number") {
    return send(event, JSON.stringify(val, void 0, jsonSpace), MIMES.json);
  }
  if (valType === "bigint") {
    return send(event, val.toString(), MIMES.json);
  }
  throw createError$1({
    statusCode: 500,
    statusMessage: `[h3] Cannot send ${valType} as response.`
  });
}
function cachedFn(fn) {
  let cache;
  return () => {
    if (!cache) {
      cache = fn();
    }
    return cache;
  };
}
function websocketOptions(evResolver, appOptions) {
  return {
    ...appOptions.websocket,
    async resolve(info) {
      const url = info.request?.url || info.url || "/";
      const { pathname } = typeof url === "string" ? parseURL(url) : url;
      const resolved = await evResolver(pathname);
      return resolved?.handler?.__websocket__ || {};
    }
  };
}

const RouterMethods = [
  "connect",
  "delete",
  "get",
  "head",
  "options",
  "post",
  "put",
  "trace",
  "patch"
];
function createRouter(opts = {}) {
  const _router = createRouter$1({});
  const routes = {};
  let _matcher;
  const router = {};
  const addRoute = (path, handler, method) => {
    let route = routes[path];
    if (!route) {
      routes[path] = route = { path, handlers: {} };
      _router.insert(path, route);
    }
    if (Array.isArray(method)) {
      for (const m of method) {
        addRoute(path, handler, m);
      }
    } else {
      route.handlers[method] = toEventHandler(handler, void 0, path);
    }
    return router;
  };
  router.use = router.add = (path, handler, method) => addRoute(path, handler, method || "all");
  for (const method of RouterMethods) {
    router[method] = (path, handle) => router.add(path, handle, method);
  }
  const matchHandler = (path = "/", method = "get") => {
    const qIndex = path.indexOf("?");
    if (qIndex !== -1) {
      path = path.slice(0, Math.max(0, qIndex));
    }
    const matched = _router.lookup(path);
    if (!matched || !matched.handlers) {
      return {
        error: createError$1({
          statusCode: 404,
          name: "Not Found",
          statusMessage: `Cannot find any route matching ${path || "/"}.`
        })
      };
    }
    let handler = matched.handlers[method] || matched.handlers.all;
    if (!handler) {
      if (!_matcher) {
        _matcher = toRouteMatcher(_router);
      }
      const _matches = _matcher.matchAll(path).reverse();
      for (const _match of _matches) {
        if (_match.handlers[method]) {
          handler = _match.handlers[method];
          matched.handlers[method] = matched.handlers[method] || handler;
          break;
        }
        if (_match.handlers.all) {
          handler = _match.handlers.all;
          matched.handlers.all = matched.handlers.all || handler;
          break;
        }
      }
    }
    if (!handler) {
      return {
        error: createError$1({
          statusCode: 405,
          name: "Method Not Allowed",
          statusMessage: `Method ${method} is not allowed on this route.`
        })
      };
    }
    return { matched, handler };
  };
  const isPreemptive = opts.preemptive || opts.preemtive;
  router.handler = eventHandler((event) => {
    const match = matchHandler(
      event.path,
      event.method.toLowerCase()
    );
    if ("error" in match) {
      if (isPreemptive) {
        throw match.error;
      } else {
        return;
      }
    }
    event.context.matchedRoute = match.matched;
    const params = match.matched.params || {};
    event.context.params = params;
    return Promise.resolve(match.handler(event)).then((res) => {
      if (res === void 0 && isPreemptive) {
        return null;
      }
      return res;
    });
  });
  router.handler.__resolve__ = async (path) => {
    path = withLeadingSlash(path);
    const match = matchHandler(path);
    if ("error" in match) {
      return;
    }
    let res = {
      route: match.matched.path,
      handler: match.handler
    };
    if (match.handler.__resolve__) {
      const _res = await match.handler.__resolve__(path);
      if (!_res) {
        return;
      }
      res = { ...res, ..._res };
    }
    return res;
  };
  return router;
}
function toNodeListener(app) {
  const toNodeHandle = async function(req, res) {
    const event = createEvent(req, res);
    try {
      await app.handler(event);
    } catch (_error) {
      const error = createError$1(_error);
      if (!isError(_error)) {
        error.unhandled = true;
      }
      setResponseStatus(event, error.statusCode, error.statusMessage);
      if (app.options.onError) {
        await app.options.onError(error, event);
      }
      if (event.handled) {
        return;
      }
      if (error.unhandled || error.fatal) {
        console.error("[h3]", error.fatal ? "[fatal]" : "[unhandled]", error);
      }
      if (app.options.onBeforeResponse && !event._onBeforeResponseCalled) {
        await app.options.onBeforeResponse(event, { body: error });
      }
      await sendError(event, error, !!app.options.debug);
      if (app.options.onAfterResponse && !event._onAfterResponseCalled) {
        await app.options.onAfterResponse(event, { body: error });
      }
    }
  };
  return toNodeHandle;
}

function flatHooks(configHooks, hooks = {}, parentName) {
  for (const key in configHooks) {
    const subHook = configHooks[key];
    const name = parentName ? `${parentName}:${key}` : key;
    if (typeof subHook === "object" && subHook !== null) {
      flatHooks(subHook, hooks, name);
    } else if (typeof subHook === "function") {
      hooks[name] = subHook;
    }
  }
  return hooks;
}
const defaultTask = { run: (function_) => function_() };
const _createTask = () => defaultTask;
const createTask = typeof console.createTask !== "undefined" ? console.createTask : _createTask;
function serialTaskCaller(hooks, args) {
  const name = args.shift();
  const task = createTask(name);
  return hooks.reduce(
    (promise, hookFunction) => promise.then(() => task.run(() => hookFunction(...args))),
    Promise.resolve()
  );
}
function parallelTaskCaller(hooks, args) {
  const name = args.shift();
  const task = createTask(name);
  return Promise.all(hooks.map((hook) => task.run(() => hook(...args))));
}
function callEachWith(callbacks, arg0) {
  for (const callback of [...callbacks]) {
    callback(arg0);
  }
}

class Hookable {
  constructor() {
    this._hooks = {};
    this._before = void 0;
    this._after = void 0;
    this._deprecatedMessages = void 0;
    this._deprecatedHooks = {};
    this.hook = this.hook.bind(this);
    this.callHook = this.callHook.bind(this);
    this.callHookWith = this.callHookWith.bind(this);
  }
  hook(name, function_, options = {}) {
    if (!name || typeof function_ !== "function") {
      return () => {
      };
    }
    const originalName = name;
    let dep;
    while (this._deprecatedHooks[name]) {
      dep = this._deprecatedHooks[name];
      name = dep.to;
    }
    if (dep && !options.allowDeprecated) {
      let message = dep.message;
      if (!message) {
        message = `${originalName} hook has been deprecated` + (dep.to ? `, please use ${dep.to}` : "");
      }
      if (!this._deprecatedMessages) {
        this._deprecatedMessages = /* @__PURE__ */ new Set();
      }
      if (!this._deprecatedMessages.has(message)) {
        console.warn(message);
        this._deprecatedMessages.add(message);
      }
    }
    if (!function_.name) {
      try {
        Object.defineProperty(function_, "name", {
          get: () => "_" + name.replace(/\W+/g, "_") + "_hook_cb",
          configurable: true
        });
      } catch {
      }
    }
    this._hooks[name] = this._hooks[name] || [];
    this._hooks[name].push(function_);
    return () => {
      if (function_) {
        this.removeHook(name, function_);
        function_ = void 0;
      }
    };
  }
  hookOnce(name, function_) {
    let _unreg;
    let _function = (...arguments_) => {
      if (typeof _unreg === "function") {
        _unreg();
      }
      _unreg = void 0;
      _function = void 0;
      return function_(...arguments_);
    };
    _unreg = this.hook(name, _function);
    return _unreg;
  }
  removeHook(name, function_) {
    if (this._hooks[name]) {
      const index = this._hooks[name].indexOf(function_);
      if (index !== -1) {
        this._hooks[name].splice(index, 1);
      }
      if (this._hooks[name].length === 0) {
        delete this._hooks[name];
      }
    }
  }
  deprecateHook(name, deprecated) {
    this._deprecatedHooks[name] = typeof deprecated === "string" ? { to: deprecated } : deprecated;
    const _hooks = this._hooks[name] || [];
    delete this._hooks[name];
    for (const hook of _hooks) {
      this.hook(name, hook);
    }
  }
  deprecateHooks(deprecatedHooks) {
    Object.assign(this._deprecatedHooks, deprecatedHooks);
    for (const name in deprecatedHooks) {
      this.deprecateHook(name, deprecatedHooks[name]);
    }
  }
  addHooks(configHooks) {
    const hooks = flatHooks(configHooks);
    const removeFns = Object.keys(hooks).map(
      (key) => this.hook(key, hooks[key])
    );
    return () => {
      for (const unreg of removeFns.splice(0, removeFns.length)) {
        unreg();
      }
    };
  }
  removeHooks(configHooks) {
    const hooks = flatHooks(configHooks);
    for (const key in hooks) {
      this.removeHook(key, hooks[key]);
    }
  }
  removeAllHooks() {
    for (const key in this._hooks) {
      delete this._hooks[key];
    }
  }
  callHook(name, ...arguments_) {
    arguments_.unshift(name);
    return this.callHookWith(serialTaskCaller, name, ...arguments_);
  }
  callHookParallel(name, ...arguments_) {
    arguments_.unshift(name);
    return this.callHookWith(parallelTaskCaller, name, ...arguments_);
  }
  callHookWith(caller, name, ...arguments_) {
    const event = this._before || this._after ? { name, args: arguments_, context: {} } : void 0;
    if (this._before) {
      callEachWith(this._before, event);
    }
    const result = caller(
      name in this._hooks ? [...this._hooks[name]] : [],
      arguments_
    );
    if (result instanceof Promise) {
      return result.finally(() => {
        if (this._after && event) {
          callEachWith(this._after, event);
        }
      });
    }
    if (this._after && event) {
      callEachWith(this._after, event);
    }
    return result;
  }
  beforeEach(function_) {
    this._before = this._before || [];
    this._before.push(function_);
    return () => {
      if (this._before !== void 0) {
        const index = this._before.indexOf(function_);
        if (index !== -1) {
          this._before.splice(index, 1);
        }
      }
    };
  }
  afterEach(function_) {
    this._after = this._after || [];
    this._after.push(function_);
    return () => {
      if (this._after !== void 0) {
        const index = this._after.indexOf(function_);
        if (index !== -1) {
          this._after.splice(index, 1);
        }
      }
    };
  }
}
function createHooks() {
  return new Hookable();
}

const s$1=globalThis.Headers,i=globalThis.AbortController,l=globalThis.fetch||(()=>{throw new Error("[node-fetch-native] Failed to fetch: `globalThis.fetch` is not available!")});

class FetchError extends Error {
  constructor(message, opts) {
    super(message, opts);
    this.name = "FetchError";
    if (opts?.cause && !this.cause) {
      this.cause = opts.cause;
    }
  }
}
function createFetchError(ctx) {
  const errorMessage = ctx.error?.message || ctx.error?.toString() || "";
  const method = ctx.request?.method || ctx.options?.method || "GET";
  const url = ctx.request?.url || String(ctx.request) || "/";
  const requestStr = `[${method}] ${JSON.stringify(url)}`;
  const statusStr = ctx.response ? `${ctx.response.status} ${ctx.response.statusText}` : "<no response>";
  const message = `${requestStr}: ${statusStr}${errorMessage ? ` ${errorMessage}` : ""}`;
  const fetchError = new FetchError(
    message,
    ctx.error ? { cause: ctx.error } : void 0
  );
  for (const key of ["request", "options", "response"]) {
    Object.defineProperty(fetchError, key, {
      get() {
        return ctx[key];
      }
    });
  }
  for (const [key, refKey] of [
    ["data", "_data"],
    ["status", "status"],
    ["statusCode", "status"],
    ["statusText", "statusText"],
    ["statusMessage", "statusText"]
  ]) {
    Object.defineProperty(fetchError, key, {
      get() {
        return ctx.response && ctx.response[refKey];
      }
    });
  }
  return fetchError;
}

const payloadMethods = new Set(
  Object.freeze(["PATCH", "POST", "PUT", "DELETE"])
);
function isPayloadMethod(method = "GET") {
  return payloadMethods.has(method.toUpperCase());
}
function isJSONSerializable(value) {
  if (value === void 0) {
    return false;
  }
  const t = typeof value;
  if (t === "string" || t === "number" || t === "boolean" || t === null) {
    return true;
  }
  if (t !== "object") {
    return false;
  }
  if (Array.isArray(value)) {
    return true;
  }
  if (value.buffer) {
    return false;
  }
  return value.constructor && value.constructor.name === "Object" || typeof value.toJSON === "function";
}
const textTypes = /* @__PURE__ */ new Set([
  "image/svg",
  "application/xml",
  "application/xhtml",
  "application/html"
]);
const JSON_RE = /^application\/(?:[\w!#$%&*.^`~-]*\+)?json(;.+)?$/i;
function detectResponseType(_contentType = "") {
  if (!_contentType) {
    return "json";
  }
  const contentType = _contentType.split(";").shift() || "";
  if (JSON_RE.test(contentType)) {
    return "json";
  }
  if (textTypes.has(contentType) || contentType.startsWith("text/")) {
    return "text";
  }
  return "blob";
}
function resolveFetchOptions(request, input, defaults, Headers) {
  const headers = mergeHeaders(
    input?.headers ?? request?.headers,
    defaults?.headers,
    Headers
  );
  let query;
  if (defaults?.query || defaults?.params || input?.params || input?.query) {
    query = {
      ...defaults?.params,
      ...defaults?.query,
      ...input?.params,
      ...input?.query
    };
  }
  return {
    ...defaults,
    ...input,
    query,
    params: query,
    headers
  };
}
function mergeHeaders(input, defaults, Headers) {
  if (!defaults) {
    return new Headers(input);
  }
  const headers = new Headers(defaults);
  if (input) {
    for (const [key, value] of Symbol.iterator in input || Array.isArray(input) ? input : new Headers(input)) {
      headers.set(key, value);
    }
  }
  return headers;
}
async function callHooks(context, hooks) {
  if (hooks) {
    if (Array.isArray(hooks)) {
      for (const hook of hooks) {
        await hook(context);
      }
    } else {
      await hooks(context);
    }
  }
}

const retryStatusCodes = /* @__PURE__ */ new Set([
  408,
  // Request Timeout
  409,
  // Conflict
  425,
  // Too Early (Experimental)
  429,
  // Too Many Requests
  500,
  // Internal Server Error
  502,
  // Bad Gateway
  503,
  // Service Unavailable
  504
  // Gateway Timeout
]);
const nullBodyResponses = /* @__PURE__ */ new Set([101, 204, 205, 304]);
function createFetch(globalOptions = {}) {
  const {
    fetch = globalThis.fetch,
    Headers = globalThis.Headers,
    AbortController = globalThis.AbortController
  } = globalOptions;
  async function onError(context) {
    const isAbort = context.error && context.error.name === "AbortError" && !context.options.timeout || false;
    if (context.options.retry !== false && !isAbort) {
      let retries;
      if (typeof context.options.retry === "number") {
        retries = context.options.retry;
      } else {
        retries = isPayloadMethod(context.options.method) ? 0 : 1;
      }
      const responseCode = context.response && context.response.status || 500;
      if (retries > 0 && (Array.isArray(context.options.retryStatusCodes) ? context.options.retryStatusCodes.includes(responseCode) : retryStatusCodes.has(responseCode))) {
        const retryDelay = typeof context.options.retryDelay === "function" ? context.options.retryDelay(context) : context.options.retryDelay || 0;
        if (retryDelay > 0) {
          await new Promise((resolve) => setTimeout(resolve, retryDelay));
        }
        return $fetchRaw(context.request, {
          ...context.options,
          retry: retries - 1
        });
      }
    }
    const error = createFetchError(context);
    if (Error.captureStackTrace) {
      Error.captureStackTrace(error, $fetchRaw);
    }
    throw error;
  }
  const $fetchRaw = async function $fetchRaw2(_request, _options = {}) {
    const context = {
      request: _request,
      options: resolveFetchOptions(
        _request,
        _options,
        globalOptions.defaults,
        Headers
      ),
      response: void 0,
      error: void 0
    };
    if (context.options.method) {
      context.options.method = context.options.method.toUpperCase();
    }
    if (context.options.onRequest) {
      await callHooks(context, context.options.onRequest);
    }
    if (typeof context.request === "string") {
      if (context.options.baseURL) {
        context.request = withBase(context.request, context.options.baseURL);
      }
      if (context.options.query) {
        context.request = withQuery(context.request, context.options.query);
        delete context.options.query;
      }
      if ("query" in context.options) {
        delete context.options.query;
      }
      if ("params" in context.options) {
        delete context.options.params;
      }
    }
    if (context.options.body && isPayloadMethod(context.options.method)) {
      if (isJSONSerializable(context.options.body)) {
        context.options.body = typeof context.options.body === "string" ? context.options.body : JSON.stringify(context.options.body);
        context.options.headers = new Headers(context.options.headers || {});
        if (!context.options.headers.has("content-type")) {
          context.options.headers.set("content-type", "application/json");
        }
        if (!context.options.headers.has("accept")) {
          context.options.headers.set("accept", "application/json");
        }
      } else if (
        // ReadableStream Body
        "pipeTo" in context.options.body && typeof context.options.body.pipeTo === "function" || // Node.js Stream Body
        typeof context.options.body.pipe === "function"
      ) {
        if (!("duplex" in context.options)) {
          context.options.duplex = "half";
        }
      }
    }
    let abortTimeout;
    if (!context.options.signal && context.options.timeout) {
      const controller = new AbortController();
      abortTimeout = setTimeout(() => {
        const error = new Error(
          "[TimeoutError]: The operation was aborted due to timeout"
        );
        error.name = "TimeoutError";
        error.code = 23;
        controller.abort(error);
      }, context.options.timeout);
      context.options.signal = controller.signal;
    }
    try {
      context.response = await fetch(
        context.request,
        context.options
      );
    } catch (error) {
      context.error = error;
      if (context.options.onRequestError) {
        await callHooks(
          context,
          context.options.onRequestError
        );
      }
      return await onError(context);
    } finally {
      if (abortTimeout) {
        clearTimeout(abortTimeout);
      }
    }
    const hasBody = (context.response.body || // https://github.com/unjs/ofetch/issues/324
    // https://github.com/unjs/ofetch/issues/294
    // https://github.com/JakeChampion/fetch/issues/1454
    context.response._bodyInit) && !nullBodyResponses.has(context.response.status) && context.options.method !== "HEAD";
    if (hasBody) {
      const responseType = (context.options.parseResponse ? "json" : context.options.responseType) || detectResponseType(context.response.headers.get("content-type") || "");
      switch (responseType) {
        case "json": {
          const data = await context.response.text();
          const parseFunction = context.options.parseResponse || destr;
          context.response._data = parseFunction(data);
          break;
        }
        case "stream": {
          context.response._data = context.response.body || context.response._bodyInit;
          break;
        }
        default: {
          context.response._data = await context.response[responseType]();
        }
      }
    }
    if (context.options.onResponse) {
      await callHooks(
        context,
        context.options.onResponse
      );
    }
    if (!context.options.ignoreResponseError && context.response.status >= 400 && context.response.status < 600) {
      if (context.options.onResponseError) {
        await callHooks(
          context,
          context.options.onResponseError
        );
      }
      return await onError(context);
    }
    return context.response;
  };
  const $fetch = async function $fetch2(request, options) {
    const r = await $fetchRaw(request, options);
    return r._data;
  };
  $fetch.raw = $fetchRaw;
  $fetch.native = (...args) => fetch(...args);
  $fetch.create = (defaultOptions = {}, customGlobalOptions = {}) => createFetch({
    ...globalOptions,
    ...customGlobalOptions,
    defaults: {
      ...globalOptions.defaults,
      ...customGlobalOptions.defaults,
      ...defaultOptions
    }
  });
  return $fetch;
}

function createNodeFetch() {
  const useKeepAlive = JSON.parse(process.env.FETCH_KEEP_ALIVE || "false");
  if (!useKeepAlive) {
    return l;
  }
  const agentOptions = { keepAlive: true };
  const httpAgent = new http.Agent(agentOptions);
  const httpsAgent = new https.Agent(agentOptions);
  const nodeFetchOptions = {
    agent(parsedURL) {
      return parsedURL.protocol === "http:" ? httpAgent : httpsAgent;
    }
  };
  return function nodeFetchWithKeepAlive(input, init) {
    return l(input, { ...nodeFetchOptions, ...init });
  };
}
const fetch = globalThis.fetch ? (...args) => globalThis.fetch(...args) : createNodeFetch();
const Headers$1 = globalThis.Headers || s$1;
const AbortController = globalThis.AbortController || i;
createFetch({ fetch, Headers: Headers$1, AbortController });

function wrapToPromise(value) {
  if (!value || typeof value.then !== "function") {
    return Promise.resolve(value);
  }
  return value;
}
function asyncCall(function_, ...arguments_) {
  try {
    return wrapToPromise(function_(...arguments_));
  } catch (error) {
    return Promise.reject(error);
  }
}
function isPrimitive(value) {
  const type = typeof value;
  return value === null || type !== "object" && type !== "function";
}
function isPureObject(value) {
  const proto = Object.getPrototypeOf(value);
  return !proto || proto.isPrototypeOf(Object);
}
function stringify(value) {
  if (isPrimitive(value)) {
    return String(value);
  }
  if (isPureObject(value) || Array.isArray(value)) {
    return JSON.stringify(value);
  }
  if (typeof value.toJSON === "function") {
    return stringify(value.toJSON());
  }
  throw new Error("[unstorage] Cannot stringify value!");
}
const BASE64_PREFIX = "base64:";
function serializeRaw(value) {
  if (typeof value === "string") {
    return value;
  }
  return BASE64_PREFIX + base64Encode(value);
}
function deserializeRaw(value) {
  if (typeof value !== "string") {
    return value;
  }
  if (!value.startsWith(BASE64_PREFIX)) {
    return value;
  }
  return base64Decode(value.slice(BASE64_PREFIX.length));
}
function base64Decode(input) {
  if (globalThis.Buffer) {
    return Buffer.from(input, "base64");
  }
  return Uint8Array.from(
    globalThis.atob(input),
    (c) => c.codePointAt(0)
  );
}
function base64Encode(input) {
  if (globalThis.Buffer) {
    return Buffer.from(input).toString("base64");
  }
  return globalThis.btoa(String.fromCodePoint(...input));
}

const storageKeyProperties = [
  "has",
  "hasItem",
  "get",
  "getItem",
  "getItemRaw",
  "set",
  "setItem",
  "setItemRaw",
  "del",
  "remove",
  "removeItem",
  "getMeta",
  "setMeta",
  "removeMeta",
  "getKeys",
  "clear",
  "mount",
  "unmount"
];
function prefixStorage(storage, base) {
  base = normalizeBaseKey(base);
  if (!base) {
    return storage;
  }
  const nsStorage = { ...storage };
  for (const property of storageKeyProperties) {
    nsStorage[property] = (key = "", ...args) => (
      // @ts-ignore
      storage[property](base + key, ...args)
    );
  }
  nsStorage.getKeys = (key = "", ...arguments_) => storage.getKeys(base + key, ...arguments_).then((keys) => keys.map((key2) => key2.slice(base.length)));
  nsStorage.getItems = async (items, commonOptions) => {
    const prefixedItems = items.map(
      (item) => typeof item === "string" ? base + item : { ...item, key: base + item.key }
    );
    const results = await storage.getItems(prefixedItems, commonOptions);
    return results.map((entry) => ({
      key: entry.key.slice(base.length),
      value: entry.value
    }));
  };
  nsStorage.setItems = async (items, commonOptions) => {
    const prefixedItems = items.map((item) => ({
      key: base + item.key,
      value: item.value,
      options: item.options
    }));
    return storage.setItems(prefixedItems, commonOptions);
  };
  return nsStorage;
}
function normalizeKey$1(key) {
  if (!key) {
    return "";
  }
  return key.split("?")[0]?.replace(/[/\\]/g, ":").replace(/:+/g, ":").replace(/^:|:$/g, "") || "";
}
function joinKeys(...keys) {
  return normalizeKey$1(keys.join(":"));
}
function normalizeBaseKey(base) {
  base = normalizeKey$1(base);
  return base ? base + ":" : "";
}
function filterKeyByDepth(key, depth) {
  if (depth === void 0) {
    return true;
  }
  let substrCount = 0;
  let index = key.indexOf(":");
  while (index > -1) {
    substrCount++;
    index = key.indexOf(":", index + 1);
  }
  return substrCount <= depth;
}
function filterKeyByBase(key, base) {
  if (base) {
    return key.startsWith(base) && key[key.length - 1] !== "$";
  }
  return key[key.length - 1] !== "$";
}

function defineDriver$1(factory) {
  return factory;
}

const DRIVER_NAME$1 = "memory";
const memory = defineDriver$1(() => {
  const data = /* @__PURE__ */ new Map();
  return {
    name: DRIVER_NAME$1,
    getInstance: () => data,
    hasItem(key) {
      return data.has(key);
    },
    getItem(key) {
      return data.get(key) ?? null;
    },
    getItemRaw(key) {
      return data.get(key) ?? null;
    },
    setItem(key, value) {
      data.set(key, value);
    },
    setItemRaw(key, value) {
      data.set(key, value);
    },
    removeItem(key) {
      data.delete(key);
    },
    getKeys() {
      return [...data.keys()];
    },
    clear() {
      data.clear();
    },
    dispose() {
      data.clear();
    }
  };
});

function createStorage(options = {}) {
  const context = {
    mounts: { "": options.driver || memory() },
    mountpoints: [""],
    watching: false,
    watchListeners: [],
    unwatch: {}
  };
  const getMount = (key) => {
    for (const base of context.mountpoints) {
      if (key.startsWith(base)) {
        return {
          base,
          relativeKey: key.slice(base.length),
          driver: context.mounts[base]
        };
      }
    }
    return {
      base: "",
      relativeKey: key,
      driver: context.mounts[""]
    };
  };
  const getMounts = (base, includeParent) => {
    return context.mountpoints.filter(
      (mountpoint) => mountpoint.startsWith(base) || includeParent && base.startsWith(mountpoint)
    ).map((mountpoint) => ({
      relativeBase: base.length > mountpoint.length ? base.slice(mountpoint.length) : void 0,
      mountpoint,
      driver: context.mounts[mountpoint]
    }));
  };
  const onChange = (event, key) => {
    if (!context.watching) {
      return;
    }
    key = normalizeKey$1(key);
    for (const listener of context.watchListeners) {
      listener(event, key);
    }
  };
  const startWatch = async () => {
    if (context.watching) {
      return;
    }
    context.watching = true;
    for (const mountpoint in context.mounts) {
      context.unwatch[mountpoint] = await watch(
        context.mounts[mountpoint],
        onChange,
        mountpoint
      );
    }
  };
  const stopWatch = async () => {
    if (!context.watching) {
      return;
    }
    for (const mountpoint in context.unwatch) {
      await context.unwatch[mountpoint]();
    }
    context.unwatch = {};
    context.watching = false;
  };
  const runBatch = (items, commonOptions, cb) => {
    const batches = /* @__PURE__ */ new Map();
    const getBatch = (mount) => {
      let batch = batches.get(mount.base);
      if (!batch) {
        batch = {
          driver: mount.driver,
          base: mount.base,
          items: []
        };
        batches.set(mount.base, batch);
      }
      return batch;
    };
    for (const item of items) {
      const isStringItem = typeof item === "string";
      const key = normalizeKey$1(isStringItem ? item : item.key);
      const value = isStringItem ? void 0 : item.value;
      const options2 = isStringItem || !item.options ? commonOptions : { ...commonOptions, ...item.options };
      const mount = getMount(key);
      getBatch(mount).items.push({
        key,
        value,
        relativeKey: mount.relativeKey,
        options: options2
      });
    }
    return Promise.all([...batches.values()].map((batch) => cb(batch))).then(
      (r) => r.flat()
    );
  };
  const storage = {
    // Item
    hasItem(key, opts = {}) {
      key = normalizeKey$1(key);
      const { relativeKey, driver } = getMount(key);
      return asyncCall(driver.hasItem, relativeKey, opts);
    },
    getItem(key, opts = {}) {
      key = normalizeKey$1(key);
      const { relativeKey, driver } = getMount(key);
      return asyncCall(driver.getItem, relativeKey, opts).then(
        (value) => destr(value)
      );
    },
    getItems(items, commonOptions = {}) {
      return runBatch(items, commonOptions, (batch) => {
        if (batch.driver.getItems) {
          return asyncCall(
            batch.driver.getItems,
            batch.items.map((item) => ({
              key: item.relativeKey,
              options: item.options
            })),
            commonOptions
          ).then(
            (r) => r.map((item) => ({
              key: joinKeys(batch.base, item.key),
              value: destr(item.value)
            }))
          );
        }
        return Promise.all(
          batch.items.map((item) => {
            return asyncCall(
              batch.driver.getItem,
              item.relativeKey,
              item.options
            ).then((value) => ({
              key: item.key,
              value: destr(value)
            }));
          })
        );
      });
    },
    getItemRaw(key, opts = {}) {
      key = normalizeKey$1(key);
      const { relativeKey, driver } = getMount(key);
      if (driver.getItemRaw) {
        return asyncCall(driver.getItemRaw, relativeKey, opts);
      }
      return asyncCall(driver.getItem, relativeKey, opts).then(
        (value) => deserializeRaw(value)
      );
    },
    async setItem(key, value, opts = {}) {
      if (value === void 0) {
        return storage.removeItem(key);
      }
      key = normalizeKey$1(key);
      const { relativeKey, driver } = getMount(key);
      if (!driver.setItem) {
        return;
      }
      await asyncCall(driver.setItem, relativeKey, stringify(value), opts);
      if (!driver.watch) {
        onChange("update", key);
      }
    },
    async setItems(items, commonOptions) {
      await runBatch(items, commonOptions, async (batch) => {
        if (batch.driver.setItems) {
          return asyncCall(
            batch.driver.setItems,
            batch.items.map((item) => ({
              key: item.relativeKey,
              value: stringify(item.value),
              options: item.options
            })),
            commonOptions
          );
        }
        if (!batch.driver.setItem) {
          return;
        }
        await Promise.all(
          batch.items.map((item) => {
            return asyncCall(
              batch.driver.setItem,
              item.relativeKey,
              stringify(item.value),
              item.options
            );
          })
        );
      });
    },
    async setItemRaw(key, value, opts = {}) {
      if (value === void 0) {
        return storage.removeItem(key, opts);
      }
      key = normalizeKey$1(key);
      const { relativeKey, driver } = getMount(key);
      if (driver.setItemRaw) {
        await asyncCall(driver.setItemRaw, relativeKey, value, opts);
      } else if (driver.setItem) {
        await asyncCall(driver.setItem, relativeKey, serializeRaw(value), opts);
      } else {
        return;
      }
      if (!driver.watch) {
        onChange("update", key);
      }
    },
    async removeItem(key, opts = {}) {
      if (typeof opts === "boolean") {
        opts = { removeMeta: opts };
      }
      key = normalizeKey$1(key);
      const { relativeKey, driver } = getMount(key);
      if (!driver.removeItem) {
        return;
      }
      await asyncCall(driver.removeItem, relativeKey, opts);
      if (opts.removeMeta || opts.removeMata) {
        await asyncCall(driver.removeItem, relativeKey + "$", opts);
      }
      if (!driver.watch) {
        onChange("remove", key);
      }
    },
    // Meta
    async getMeta(key, opts = {}) {
      if (typeof opts === "boolean") {
        opts = { nativeOnly: opts };
      }
      key = normalizeKey$1(key);
      const { relativeKey, driver } = getMount(key);
      const meta = /* @__PURE__ */ Object.create(null);
      if (driver.getMeta) {
        Object.assign(meta, await asyncCall(driver.getMeta, relativeKey, opts));
      }
      if (!opts.nativeOnly) {
        const value = await asyncCall(
          driver.getItem,
          relativeKey + "$",
          opts
        ).then((value_) => destr(value_));
        if (value && typeof value === "object") {
          if (typeof value.atime === "string") {
            value.atime = new Date(value.atime);
          }
          if (typeof value.mtime === "string") {
            value.mtime = new Date(value.mtime);
          }
          Object.assign(meta, value);
        }
      }
      return meta;
    },
    setMeta(key, value, opts = {}) {
      return this.setItem(key + "$", value, opts);
    },
    removeMeta(key, opts = {}) {
      return this.removeItem(key + "$", opts);
    },
    // Keys
    async getKeys(base, opts = {}) {
      base = normalizeBaseKey(base);
      const mounts = getMounts(base, true);
      let maskedMounts = [];
      const allKeys = [];
      let allMountsSupportMaxDepth = true;
      for (const mount of mounts) {
        if (!mount.driver.flags?.maxDepth) {
          allMountsSupportMaxDepth = false;
        }
        const rawKeys = await asyncCall(
          mount.driver.getKeys,
          mount.relativeBase,
          opts
        );
        for (const key of rawKeys) {
          const fullKey = mount.mountpoint + normalizeKey$1(key);
          if (!maskedMounts.some((p) => fullKey.startsWith(p))) {
            allKeys.push(fullKey);
          }
        }
        maskedMounts = [
          mount.mountpoint,
          ...maskedMounts.filter((p) => !p.startsWith(mount.mountpoint))
        ];
      }
      const shouldFilterByDepth = opts.maxDepth !== void 0 && !allMountsSupportMaxDepth;
      return allKeys.filter(
        (key) => (!shouldFilterByDepth || filterKeyByDepth(key, opts.maxDepth)) && filterKeyByBase(key, base)
      );
    },
    // Utils
    async clear(base, opts = {}) {
      base = normalizeBaseKey(base);
      await Promise.all(
        getMounts(base, false).map(async (m) => {
          if (m.driver.clear) {
            return asyncCall(m.driver.clear, m.relativeBase, opts);
          }
          if (m.driver.removeItem) {
            const keys = await m.driver.getKeys(m.relativeBase || "", opts);
            return Promise.all(
              keys.map((key) => m.driver.removeItem(key, opts))
            );
          }
        })
      );
    },
    async dispose() {
      await Promise.all(
        Object.values(context.mounts).map((driver) => dispose(driver))
      );
    },
    async watch(callback) {
      await startWatch();
      context.watchListeners.push(callback);
      return async () => {
        context.watchListeners = context.watchListeners.filter(
          (listener) => listener !== callback
        );
        if (context.watchListeners.length === 0) {
          await stopWatch();
        }
      };
    },
    async unwatch() {
      context.watchListeners = [];
      await stopWatch();
    },
    // Mount
    mount(base, driver) {
      base = normalizeBaseKey(base);
      if (base && context.mounts[base]) {
        throw new Error(`already mounted at ${base}`);
      }
      if (base) {
        context.mountpoints.push(base);
        context.mountpoints.sort((a, b) => b.length - a.length);
      }
      context.mounts[base] = driver;
      if (context.watching) {
        Promise.resolve(watch(driver, onChange, base)).then((unwatcher) => {
          context.unwatch[base] = unwatcher;
        }).catch(console.error);
      }
      return storage;
    },
    async unmount(base, _dispose = true) {
      base = normalizeBaseKey(base);
      if (!base || !context.mounts[base]) {
        return;
      }
      if (context.watching && base in context.unwatch) {
        context.unwatch[base]?.();
        delete context.unwatch[base];
      }
      if (_dispose) {
        await dispose(context.mounts[base]);
      }
      context.mountpoints = context.mountpoints.filter((key) => key !== base);
      delete context.mounts[base];
    },
    getMount(key = "") {
      key = normalizeKey$1(key) + ":";
      const m = getMount(key);
      return {
        driver: m.driver,
        base: m.base
      };
    },
    getMounts(base = "", opts = {}) {
      base = normalizeKey$1(base);
      const mounts = getMounts(base, opts.parents);
      return mounts.map((m) => ({
        driver: m.driver,
        base: m.mountpoint
      }));
    },
    // Aliases
    keys: (base, opts = {}) => storage.getKeys(base, opts),
    get: (key, opts = {}) => storage.getItem(key, opts),
    set: (key, value, opts = {}) => storage.setItem(key, value, opts),
    has: (key, opts = {}) => storage.hasItem(key, opts),
    del: (key, opts = {}) => storage.removeItem(key, opts),
    remove: (key, opts = {}) => storage.removeItem(key, opts)
  };
  return storage;
}
function watch(driver, onChange, base) {
  return driver.watch ? driver.watch((event, key) => onChange(event, base + key)) : () => {
  };
}
async function dispose(driver) {
  if (typeof driver.dispose === "function") {
    await asyncCall(driver.dispose);
  }
}

const _assets = {

};

const normalizeKey = function normalizeKey(key) {
  if (!key) {
    return "";
  }
  return key.split("?")[0]?.replace(/[/\\]/g, ":").replace(/:+/g, ":").replace(/^:|:$/g, "") || "";
};

const assets$1 = {
  getKeys() {
    return Promise.resolve(Object.keys(_assets))
  },
  hasItem (id) {
    id = normalizeKey(id);
    return Promise.resolve(id in _assets)
  },
  getItem (id) {
    id = normalizeKey(id);
    return Promise.resolve(_assets[id] ? _assets[id].import() : null)
  },
  getMeta (id) {
    id = normalizeKey(id);
    return Promise.resolve(_assets[id] ? _assets[id].meta : {})
  }
};

function defineDriver(factory) {
  return factory;
}
function createError(driver, message, opts) {
  const err = new Error(`[unstorage] [${driver}] ${message}`, opts);
  if (Error.captureStackTrace) {
    Error.captureStackTrace(err, createError);
  }
  return err;
}
function createRequiredError(driver, name) {
  if (Array.isArray(name)) {
    return createError(
      driver,
      `Missing some of the required options ${name.map((n) => "`" + n + "`").join(", ")}`
    );
  }
  return createError(driver, `Missing required option \`${name}\`.`);
}

function ignoreNotfound(err) {
  return err.code === "ENOENT" || err.code === "EISDIR" ? null : err;
}
function ignoreExists(err) {
  return err.code === "EEXIST" ? null : err;
}
async function writeFile(path, data, encoding) {
  await ensuredir(dirname$1(path));
  return promises.writeFile(path, data, encoding);
}
function readFile(path, encoding) {
  return promises.readFile(path, encoding).catch(ignoreNotfound);
}
function unlink(path) {
  return promises.unlink(path).catch(ignoreNotfound);
}
function readdir(dir) {
  return promises.readdir(dir, { withFileTypes: true }).catch(ignoreNotfound).then((r) => r || []);
}
async function ensuredir(dir) {
  if (existsSync(dir)) {
    return;
  }
  await ensuredir(dirname$1(dir)).catch(ignoreExists);
  await promises.mkdir(dir).catch(ignoreExists);
}
async function readdirRecursive(dir, ignore, maxDepth) {
  if (ignore && ignore(dir)) {
    return [];
  }
  const entries = await readdir(dir);
  const files = [];
  await Promise.all(
    entries.map(async (entry) => {
      const entryPath = resolve$1(dir, entry.name);
      if (entry.isDirectory()) {
        if (maxDepth === void 0 || maxDepth > 0) {
          const dirFiles = await readdirRecursive(
            entryPath,
            ignore,
            maxDepth === void 0 ? void 0 : maxDepth - 1
          );
          files.push(...dirFiles.map((f) => entry.name + "/" + f));
        }
      } else {
        if (!(ignore && ignore(entry.name))) {
          files.push(entry.name);
        }
      }
    })
  );
  return files;
}
async function rmRecursive(dir) {
  const entries = await readdir(dir);
  await Promise.all(
    entries.map((entry) => {
      const entryPath = resolve$1(dir, entry.name);
      if (entry.isDirectory()) {
        return rmRecursive(entryPath).then(() => promises.rmdir(entryPath));
      } else {
        return promises.unlink(entryPath);
      }
    })
  );
}

const PATH_TRAVERSE_RE = /\.\.:|\.\.$/;
const DRIVER_NAME = "fs-lite";
const unstorage_47drivers_47fs_45lite = defineDriver((opts = {}) => {
  if (!opts.base) {
    throw createRequiredError(DRIVER_NAME, "base");
  }
  opts.base = resolve$1(opts.base);
  const r = (key) => {
    if (PATH_TRAVERSE_RE.test(key)) {
      throw createError(
        DRIVER_NAME,
        `Invalid key: ${JSON.stringify(key)}. It should not contain .. segments`
      );
    }
    const resolved = join(opts.base, key.replace(/:/g, "/"));
    return resolved;
  };
  return {
    name: DRIVER_NAME,
    options: opts,
    flags: {
      maxDepth: true
    },
    hasItem(key) {
      return existsSync(r(key));
    },
    getItem(key) {
      return readFile(r(key), "utf8");
    },
    getItemRaw(key) {
      return readFile(r(key));
    },
    async getMeta(key) {
      const { atime, mtime, size, birthtime, ctime } = await promises.stat(r(key)).catch(() => ({}));
      return { atime, mtime, size, birthtime, ctime };
    },
    setItem(key, value) {
      if (opts.readOnly) {
        return;
      }
      return writeFile(r(key), value, "utf8");
    },
    setItemRaw(key, value) {
      if (opts.readOnly) {
        return;
      }
      return writeFile(r(key), value);
    },
    removeItem(key) {
      if (opts.readOnly) {
        return;
      }
      return unlink(r(key));
    },
    getKeys(_base, topts) {
      return readdirRecursive(r("."), opts.ignore, topts?.maxDepth);
    },
    async clear() {
      if (opts.readOnly || opts.noClear) {
        return;
      }
      await rmRecursive(r("."));
    }
  };
});

const storage = createStorage({});

storage.mount('/assets', assets$1);

storage.mount('data', unstorage_47drivers_47fs_45lite({"driver":"fsLite","base":"./.data/kv"}));

function useStorage(base = "") {
  return base ? prefixStorage(storage, base) : storage;
}

const e=globalThis.process?.getBuiltinModule?.("crypto")?.hash,r="sha256",s="base64url";function digest(t){if(e)return e(r,t,s);const o=createHash(r).update(t);return globalThis.process?.versions?.webcontainer?o.digest().toString(s):o.digest(s)}

const Hasher = /* @__PURE__ */ (() => {
  class Hasher2 {
    buff = "";
    #context = /* @__PURE__ */ new Map();
    write(str) {
      this.buff += str;
    }
    dispatch(value) {
      const type = value === null ? "null" : typeof value;
      return this[type](value);
    }
    object(object) {
      if (object && typeof object.toJSON === "function") {
        return this.object(object.toJSON());
      }
      const objString = Object.prototype.toString.call(object);
      let objType = "";
      const objectLength = objString.length;
      objType = objectLength < 10 ? "unknown:[" + objString + "]" : objString.slice(8, objectLength - 1);
      objType = objType.toLowerCase();
      let objectNumber = null;
      if ((objectNumber = this.#context.get(object)) === void 0) {
        this.#context.set(object, this.#context.size);
      } else {
        return this.dispatch("[CIRCULAR:" + objectNumber + "]");
      }
      if (typeof Buffer !== "undefined" && Buffer.isBuffer && Buffer.isBuffer(object)) {
        this.write("buffer:");
        return this.write(object.toString("utf8"));
      }
      if (objType !== "object" && objType !== "function" && objType !== "asyncfunction") {
        if (this[objType]) {
          this[objType](object);
        } else {
          this.unknown(object, objType);
        }
      } else {
        const keys = Object.keys(object).sort();
        const extraKeys = [];
        this.write("object:" + (keys.length + extraKeys.length) + ":");
        const dispatchForKey = (key) => {
          this.dispatch(key);
          this.write(":");
          this.dispatch(object[key]);
          this.write(",");
        };
        for (const key of keys) {
          dispatchForKey(key);
        }
        for (const key of extraKeys) {
          dispatchForKey(key);
        }
      }
    }
    array(arr, unordered) {
      unordered = unordered === void 0 ? false : unordered;
      this.write("array:" + arr.length + ":");
      if (!unordered || arr.length <= 1) {
        for (const entry of arr) {
          this.dispatch(entry);
        }
        return;
      }
      const contextAdditions = /* @__PURE__ */ new Map();
      const entries = arr.map((entry) => {
        const hasher = new Hasher2();
        hasher.dispatch(entry);
        for (const [key, value] of hasher.#context) {
          contextAdditions.set(key, value);
        }
        return hasher.toString();
      });
      this.#context = contextAdditions;
      entries.sort();
      return this.array(entries, false);
    }
    date(date) {
      return this.write("date:" + date.toJSON());
    }
    symbol(sym) {
      return this.write("symbol:" + sym.toString());
    }
    unknown(value, type) {
      this.write(type);
      if (!value) {
        return;
      }
      this.write(":");
      if (value && typeof value.entries === "function") {
        return this.array(
          [...value.entries()],
          true
          /* ordered */
        );
      }
    }
    error(err) {
      return this.write("error:" + err.toString());
    }
    boolean(bool) {
      return this.write("bool:" + bool);
    }
    string(string) {
      this.write("string:" + string.length + ":");
      this.write(string);
    }
    function(fn) {
      this.write("fn:");
      if (isNativeFunction(fn)) {
        this.dispatch("[native]");
      } else {
        this.dispatch(fn.toString());
      }
    }
    number(number) {
      return this.write("number:" + number);
    }
    null() {
      return this.write("Null");
    }
    undefined() {
      return this.write("Undefined");
    }
    regexp(regex) {
      return this.write("regex:" + regex.toString());
    }
    arraybuffer(arr) {
      this.write("arraybuffer:");
      return this.dispatch(new Uint8Array(arr));
    }
    url(url) {
      return this.write("url:" + url.toString());
    }
    map(map) {
      this.write("map:");
      const arr = [...map];
      return this.array(arr, false);
    }
    set(set) {
      this.write("set:");
      const arr = [...set];
      return this.array(arr, false);
    }
    bigint(number) {
      return this.write("bigint:" + number.toString());
    }
  }
  for (const type of [
    "uint8array",
    "uint8clampedarray",
    "unt8array",
    "uint16array",
    "unt16array",
    "uint32array",
    "unt32array",
    "float32array",
    "float64array"
  ]) {
    Hasher2.prototype[type] = function(arr) {
      this.write(type + ":");
      return this.array([...arr], false);
    };
  }
  function isNativeFunction(f) {
    if (typeof f !== "function") {
      return false;
    }
    return Function.prototype.toString.call(f).slice(
      -15
      /* "[native code] }".length */
    ) === "[native code] }";
  }
  return Hasher2;
})();
function serialize(object) {
  const hasher = new Hasher();
  hasher.dispatch(object);
  return hasher.buff;
}
function hash(value) {
  return digest(typeof value === "string" ? value : serialize(value)).replace(/[-_]/g, "").slice(0, 10);
}

function defaultCacheOptions() {
  return {
    name: "_",
    base: "/cache",
    swr: true,
    maxAge: 1
  };
}
function defineCachedFunction(fn, opts = {}) {
  opts = { ...defaultCacheOptions(), ...opts };
  const pending = {};
  const group = opts.group || "nitro/functions";
  const name = opts.name || fn.name || "_";
  const integrity = opts.integrity || hash([fn, opts]);
  const validate = opts.validate || ((entry) => entry.value !== void 0);
  async function get(key, resolver, shouldInvalidateCache, event) {
    const cacheKey = [opts.base, group, name, key + ".json"].filter(Boolean).join(":").replace(/:\/$/, ":index");
    let entry = await useStorage().getItem(cacheKey).catch((error) => {
      console.error(`[cache] Cache read error.`, error);
      useNitroApp().captureError(error, { event, tags: ["cache"] });
    }) || {};
    if (typeof entry !== "object") {
      entry = {};
      const error = new Error("Malformed data read from cache.");
      console.error("[cache]", error);
      useNitroApp().captureError(error, { event, tags: ["cache"] });
    }
    const ttl = (opts.maxAge ?? 0) * 1e3;
    if (ttl) {
      entry.expires = Date.now() + ttl;
    }
    const expired = shouldInvalidateCache || entry.integrity !== integrity || ttl && Date.now() - (entry.mtime || 0) > ttl || validate(entry) === false;
    const _resolve = async () => {
      const isPending = pending[key];
      if (!isPending) {
        if (entry.value !== void 0 && (opts.staleMaxAge || 0) >= 0 && opts.swr === false) {
          entry.value = void 0;
          entry.integrity = void 0;
          entry.mtime = void 0;
          entry.expires = void 0;
        }
        pending[key] = Promise.resolve(resolver());
      }
      try {
        entry.value = await pending[key];
      } catch (error) {
        if (!isPending) {
          delete pending[key];
        }
        throw error;
      }
      if (!isPending) {
        entry.mtime = Date.now();
        entry.integrity = integrity;
        delete pending[key];
        if (validate(entry) !== false) {
          let setOpts;
          if (opts.maxAge && !opts.swr) {
            setOpts = { ttl: opts.maxAge };
          }
          const promise = useStorage().setItem(cacheKey, entry, setOpts).catch((error) => {
            console.error(`[cache] Cache write error.`, error);
            useNitroApp().captureError(error, { event, tags: ["cache"] });
          });
          if (event?.waitUntil) {
            event.waitUntil(promise);
          }
        }
      }
    };
    const _resolvePromise = expired ? _resolve() : Promise.resolve();
    if (entry.value === void 0) {
      await _resolvePromise;
    } else if (expired && event && event.waitUntil) {
      event.waitUntil(_resolvePromise);
    }
    if (opts.swr && validate(entry) !== false) {
      _resolvePromise.catch((error) => {
        console.error(`[cache] SWR handler error.`, error);
        useNitroApp().captureError(error, { event, tags: ["cache"] });
      });
      return entry;
    }
    return _resolvePromise.then(() => entry);
  }
  return async (...args) => {
    const shouldBypassCache = await opts.shouldBypassCache?.(...args);
    if (shouldBypassCache) {
      return fn(...args);
    }
    const key = await (opts.getKey || getKey)(...args);
    const shouldInvalidateCache = await opts.shouldInvalidateCache?.(...args);
    const entry = await get(
      key,
      () => fn(...args),
      shouldInvalidateCache,
      args[0] && isEvent(args[0]) ? args[0] : void 0
    );
    let value = entry.value;
    if (opts.transform) {
      value = await opts.transform(entry, ...args) || value;
    }
    return value;
  };
}
function cachedFunction(fn, opts = {}) {
  return defineCachedFunction(fn, opts);
}
function getKey(...args) {
  return args.length > 0 ? hash(args) : "";
}
function escapeKey(key) {
  return String(key).replace(/\W/g, "");
}
function defineCachedEventHandler(handler, opts = defaultCacheOptions()) {
  const variableHeaderNames = (opts.varies || []).filter(Boolean).map((h) => h.toLowerCase()).sort();
  const _opts = {
    ...opts,
    getKey: async (event) => {
      const customKey = await opts.getKey?.(event);
      if (customKey) {
        return escapeKey(customKey);
      }
      const _path = event.node.req.originalUrl || event.node.req.url || event.path;
      let _pathname;
      try {
        _pathname = escapeKey(decodeURI(parseURL(_path).pathname)).slice(0, 16) || "index";
      } catch {
        _pathname = "-";
      }
      const _hashedPath = `${_pathname}.${hash(_path)}`;
      const _headers = variableHeaderNames.map((header) => [header, event.node.req.headers[header]]).map(([name, value]) => `${escapeKey(name)}.${hash(value)}`);
      return [_hashedPath, ..._headers].join(":");
    },
    validate: (entry) => {
      if (!entry.value) {
        return false;
      }
      if (entry.value.code >= 400) {
        return false;
      }
      if (entry.value.body === void 0) {
        return false;
      }
      if (entry.value.headers.etag === "undefined" || entry.value.headers["last-modified"] === "undefined") {
        return false;
      }
      return true;
    },
    group: opts.group || "nitro/handlers",
    integrity: opts.integrity || hash([handler, opts])
  };
  const _cachedHandler = cachedFunction(
    async (incomingEvent) => {
      const variableHeaders = {};
      for (const header of variableHeaderNames) {
        const value = incomingEvent.node.req.headers[header];
        if (value !== void 0) {
          variableHeaders[header] = value;
        }
      }
      const reqProxy = cloneWithProxy(incomingEvent.node.req, {
        headers: variableHeaders
      });
      const resHeaders = {};
      let _resSendBody;
      const resProxy = cloneWithProxy(incomingEvent.node.res, {
        statusCode: 200,
        writableEnded: false,
        writableFinished: false,
        headersSent: false,
        closed: false,
        getHeader(name) {
          return resHeaders[name];
        },
        setHeader(name, value) {
          resHeaders[name] = value;
          return this;
        },
        getHeaderNames() {
          return Object.keys(resHeaders);
        },
        hasHeader(name) {
          return name in resHeaders;
        },
        removeHeader(name) {
          delete resHeaders[name];
        },
        getHeaders() {
          return resHeaders;
        },
        end(chunk, arg2, arg3) {
          if (typeof chunk === "string") {
            _resSendBody = chunk;
          }
          if (typeof arg2 === "function") {
            arg2();
          }
          if (typeof arg3 === "function") {
            arg3();
          }
          return this;
        },
        write(chunk, arg2, arg3) {
          if (typeof chunk === "string") {
            _resSendBody = chunk;
          }
          if (typeof arg2 === "function") {
            arg2(void 0);
          }
          if (typeof arg3 === "function") {
            arg3();
          }
          return true;
        },
        writeHead(statusCode, headers2) {
          this.statusCode = statusCode;
          if (headers2) {
            if (Array.isArray(headers2) || typeof headers2 === "string") {
              throw new TypeError("Raw headers  is not supported.");
            }
            for (const header in headers2) {
              const value = headers2[header];
              if (value !== void 0) {
                this.setHeader(
                  header,
                  value
                );
              }
            }
          }
          return this;
        }
      });
      const event = createEvent(reqProxy, resProxy);
      event.fetch = (url, fetchOptions) => fetchWithEvent(event, url, fetchOptions, {
        fetch: useNitroApp().localFetch
      });
      event.$fetch = (url, fetchOptions) => fetchWithEvent(event, url, fetchOptions, {
        fetch: globalThis.$fetch
      });
      event.waitUntil = incomingEvent.waitUntil;
      event.context = incomingEvent.context;
      event.context.cache = {
        options: _opts
      };
      const body = await handler(event) || _resSendBody;
      const headers = event.node.res.getHeaders();
      headers.etag = String(
        headers.Etag || headers.etag || `W/"${hash(body)}"`
      );
      headers["last-modified"] = String(
        headers["Last-Modified"] || headers["last-modified"] || (/* @__PURE__ */ new Date()).toUTCString()
      );
      const cacheControl = [];
      if (opts.swr) {
        if (opts.maxAge) {
          cacheControl.push(`s-maxage=${opts.maxAge}`);
        }
        if (opts.staleMaxAge) {
          cacheControl.push(`stale-while-revalidate=${opts.staleMaxAge}`);
        } else {
          cacheControl.push("stale-while-revalidate");
        }
      } else if (opts.maxAge) {
        cacheControl.push(`max-age=${opts.maxAge}`);
      }
      if (cacheControl.length > 0) {
        headers["cache-control"] = cacheControl.join(", ");
      }
      const cacheEntry = {
        code: event.node.res.statusCode,
        headers,
        body
      };
      return cacheEntry;
    },
    _opts
  );
  return defineEventHandler(async (event) => {
    if (opts.headersOnly) {
      if (handleCacheHeaders(event, { maxAge: opts.maxAge })) {
        return;
      }
      return handler(event);
    }
    const response = await _cachedHandler(
      event
    );
    if (event.node.res.headersSent || event.node.res.writableEnded) {
      return response.body;
    }
    if (handleCacheHeaders(event, {
      modifiedTime: new Date(response.headers["last-modified"]),
      etag: response.headers.etag,
      maxAge: opts.maxAge
    })) {
      return;
    }
    event.node.res.statusCode = response.code;
    for (const name in response.headers) {
      const value = response.headers[name];
      if (name === "set-cookie") {
        event.node.res.appendHeader(
          name,
          splitCookiesString(value)
        );
      } else {
        if (value !== void 0) {
          event.node.res.setHeader(name, value);
        }
      }
    }
    return response.body;
  });
}
function cloneWithProxy(obj, overrides) {
  return new Proxy(obj, {
    get(target, property, receiver) {
      if (property in overrides) {
        return overrides[property];
      }
      return Reflect.get(target, property, receiver);
    },
    set(target, property, value, receiver) {
      if (property in overrides) {
        overrides[property] = value;
        return true;
      }
      return Reflect.set(target, property, value, receiver);
    }
  });
}
const cachedEventHandler = defineCachedEventHandler;

function klona(x) {
	if (typeof x !== 'object') return x;

	var k, tmp, str=Object.prototype.toString.call(x);

	if (str === '[object Object]') {
		if (x.constructor !== Object && typeof x.constructor === 'function') {
			tmp = new x.constructor();
			for (k in x) {
				if (x.hasOwnProperty(k) && tmp[k] !== x[k]) {
					tmp[k] = klona(x[k]);
				}
			}
		} else {
			tmp = {}; // null
			for (k in x) {
				if (k === '__proto__') {
					Object.defineProperty(tmp, k, {
						value: klona(x[k]),
						configurable: true,
						enumerable: true,
						writable: true,
					});
				} else {
					tmp[k] = klona(x[k]);
				}
			}
		}
		return tmp;
	}

	if (str === '[object Array]') {
		k = x.length;
		for (tmp=Array(k); k--;) {
			tmp[k] = klona(x[k]);
		}
		return tmp;
	}

	if (str === '[object Set]') {
		tmp = new Set;
		x.forEach(function (val) {
			tmp.add(klona(val));
		});
		return tmp;
	}

	if (str === '[object Map]') {
		tmp = new Map;
		x.forEach(function (val, key) {
			tmp.set(klona(key), klona(val));
		});
		return tmp;
	}

	if (str === '[object Date]') {
		return new Date(+x);
	}

	if (str === '[object RegExp]') {
		tmp = new RegExp(x.source, x.flags);
		tmp.lastIndex = x.lastIndex;
		return tmp;
	}

	if (str === '[object DataView]') {
		return new x.constructor( klona(x.buffer) );
	}

	if (str === '[object ArrayBuffer]') {
		return x.slice(0);
	}

	// ArrayBuffer.isView(x)
	// ~> `new` bcuz `Buffer.slice` => ref
	if (str.slice(-6) === 'Array]') {
		return new x.constructor(x);
	}

	return x;
}

const inlineAppConfig = {};



const appConfig = defuFn(inlineAppConfig);

const NUMBER_CHAR_RE = /\d/;
const STR_SPLITTERS = ["-", "_", "/", "."];
function isUppercase(char = "") {
  if (NUMBER_CHAR_RE.test(char)) {
    return void 0;
  }
  return char !== char.toLowerCase();
}
function splitByCase(str, separators) {
  const splitters = STR_SPLITTERS;
  const parts = [];
  if (!str || typeof str !== "string") {
    return parts;
  }
  let buff = "";
  let previousUpper;
  let previousSplitter;
  for (const char of str) {
    const isSplitter = splitters.includes(char);
    if (isSplitter === true) {
      parts.push(buff);
      buff = "";
      previousUpper = void 0;
      continue;
    }
    const isUpper = isUppercase(char);
    if (previousSplitter === false) {
      if (previousUpper === false && isUpper === true) {
        parts.push(buff);
        buff = char;
        previousUpper = isUpper;
        continue;
      }
      if (previousUpper === true && isUpper === false && buff.length > 1) {
        const lastChar = buff.at(-1);
        parts.push(buff.slice(0, Math.max(0, buff.length - 1)));
        buff = lastChar + char;
        previousUpper = isUpper;
        continue;
      }
    }
    buff += char;
    previousUpper = isUpper;
    previousSplitter = isSplitter;
  }
  parts.push(buff);
  return parts;
}
function kebabCase(str, joiner) {
  return str ? (Array.isArray(str) ? str : splitByCase(str)).map((p) => p.toLowerCase()).join(joiner) : "";
}
function snakeCase(str) {
  return kebabCase(str || "", "_");
}

function getEnv(key, opts) {
  const envKey = snakeCase(key).toUpperCase();
  return destr(
    process.env[opts.prefix + envKey] ?? process.env[opts.altPrefix + envKey]
  );
}
function _isObject(input) {
  return typeof input === "object" && !Array.isArray(input);
}
function applyEnv(obj, opts, parentKey = "") {
  for (const key in obj) {
    const subKey = parentKey ? `${parentKey}_${key}` : key;
    const envValue = getEnv(subKey, opts);
    if (_isObject(obj[key])) {
      if (_isObject(envValue)) {
        obj[key] = { ...obj[key], ...envValue };
        applyEnv(obj[key], opts, subKey);
      } else if (envValue === void 0) {
        applyEnv(obj[key], opts, subKey);
      } else {
        obj[key] = envValue ?? obj[key];
      }
    } else {
      obj[key] = envValue ?? obj[key];
    }
    if (opts.envExpansion && typeof obj[key] === "string") {
      obj[key] = _expandFromEnv(obj[key]);
    }
  }
  return obj;
}
const envExpandRx = /\{\{([^{}]*)\}\}/g;
function _expandFromEnv(value) {
  return value.replace(envExpandRx, (match, key) => {
    return process.env[key] || match;
  });
}

const _inlineRuntimeConfig = {
  "app": {
    "baseURL": "/"
  },
  "nitro": {
    "routeRules": {
      "/api/**": {
        "cors": true,
        "headers": {
          "access-control-allow-origin": "*",
          "access-control-allow-methods": "*",
          "access-control-allow-headers": "*",
          "access-control-max-age": "0",
          "Access-Control-Allow-Credentials": "true",
          "Access-Control-Allow-Headers": "Accept, Authorization, Content-Length, Content-Type, If-Match, If-Modified-Since, If-None-Match, If-Unmodified-Since, X-CSRF-TOKEN, X-Requested-With",
          "Access-Control-Allow-Methods": "GET,HEAD,PUT,PATCH,POST,DELETE",
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Expose-Headers": "*"
        }
      }
    }
  },
  "dbHost": "localhost",
  "dbPort": "3306",
  "dbUser": "mengtu",
  "dbPassword": "cimu2025...",
  "dbName": "mengtu",
  "jwtSecret": "mengtutv_jwt_secret_production_key_2024_secure_baota",
  "jwtExpiresIn": "24h",
  "jwtAdminExpiresIn": "12h",
  "accessTokenSecret": "mengtutv_access_token_secret_production_2024_baota",
  "refreshTokenSecret": "mengtutv_refresh_token_secret_production_2024_baota",
  "uploadDir": "/www/wwwroot/api.qinghee.com.cn/uploads",
  "maxFileSize": "10485760",
  "allowedFileTypes": "image/jpeg,image/png,image/gif,application/pdf,video/mp4",
  "appUrl": "https://www.qinghee.com.cn",
  "apiUrl": "https://api.qinghee.com.cn/api",
  "cosSecretId": "your_cos_secret_id",
  "cosSecretKey": "your_cos_secret_key",
  "cosRegion": "ap-shanghai",
  "cosBucket": "mengtu-production",
  "cosDirectory": "mengtutv",
  "ossProvider": "cos",
  "logLevel": "warn"
};
const envOptions = {
  prefix: "NITRO_",
  altPrefix: _inlineRuntimeConfig.nitro.envPrefix ?? process.env.NITRO_ENV_PREFIX ?? "_",
  envExpansion: _inlineRuntimeConfig.nitro.envExpansion ?? process.env.NITRO_ENV_EXPANSION ?? false
};
const _sharedRuntimeConfig = _deepFreeze(
  applyEnv(klona(_inlineRuntimeConfig), envOptions)
);
function useRuntimeConfig(event) {
  {
    return _sharedRuntimeConfig;
  }
}
_deepFreeze(klona(appConfig));
function _deepFreeze(object) {
  const propNames = Object.getOwnPropertyNames(object);
  for (const name of propNames) {
    const value = object[name];
    if (value && typeof value === "object") {
      _deepFreeze(value);
    }
  }
  return Object.freeze(object);
}
new Proxy(/* @__PURE__ */ Object.create(null), {
  get: (_, prop) => {
    console.warn(
      "Please use `useRuntimeConfig()` instead of accessing config directly."
    );
    const runtimeConfig = useRuntimeConfig();
    if (prop in runtimeConfig) {
      return runtimeConfig[prop];
    }
    return void 0;
  }
});

const config = useRuntimeConfig();
const _routeRulesMatcher = toRouteMatcher(
  createRouter$1({ routes: config.nitro.routeRules })
);
function createRouteRulesHandler(ctx) {
  return eventHandler((event) => {
    const routeRules = getRouteRules(event);
    if (routeRules.headers) {
      setHeaders(event, routeRules.headers);
    }
    if (routeRules.redirect) {
      let target = routeRules.redirect.to;
      if (target.endsWith("/**")) {
        let targetPath = event.path;
        const strpBase = routeRules.redirect._redirectStripBase;
        if (strpBase) {
          targetPath = withoutBase(targetPath, strpBase);
        }
        target = joinURL(target.slice(0, -3), targetPath);
      } else if (event.path.includes("?")) {
        const query = getQuery$1(event.path);
        target = withQuery(target, query);
      }
      return sendRedirect(event, target, routeRules.redirect.statusCode);
    }
    if (routeRules.proxy) {
      let target = routeRules.proxy.to;
      if (target.endsWith("/**")) {
        let targetPath = event.path;
        const strpBase = routeRules.proxy._proxyStripBase;
        if (strpBase) {
          targetPath = withoutBase(targetPath, strpBase);
        }
        target = joinURL(target.slice(0, -3), targetPath);
      } else if (event.path.includes("?")) {
        const query = getQuery$1(event.path);
        target = withQuery(target, query);
      }
      return proxyRequest(event, target, {
        fetch: ctx.localFetch,
        ...routeRules.proxy
      });
    }
  });
}
function getRouteRules(event) {
  event.context._nitro = event.context._nitro || {};
  if (!event.context._nitro.routeRules) {
    event.context._nitro.routeRules = getRouteRulesForPath(
      withoutBase(event.path.split("?")[0], useRuntimeConfig().app.baseURL)
    );
  }
  return event.context._nitro.routeRules;
}
function getRouteRulesForPath(path) {
  return defu({}, ..._routeRulesMatcher.matchAll(path).reverse());
}

function _captureError(error, type) {
  console.error(`[${type}]`, error);
  useNitroApp().captureError(error, { tags: [type] });
}
function trapUnhandledNodeErrors() {
  process.on(
    "unhandledRejection",
    (error) => _captureError(error, "unhandledRejection")
  );
  process.on(
    "uncaughtException",
    (error) => _captureError(error, "uncaughtException")
  );
}
function joinHeaders(value) {
  return Array.isArray(value) ? value.join(", ") : String(value);
}
function normalizeFetchResponse(response) {
  if (!response.headers.has("set-cookie")) {
    return response;
  }
  return new Response(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers: normalizeCookieHeaders(response.headers)
  });
}
function normalizeCookieHeader(header = "") {
  return splitCookiesString(joinHeaders(header));
}
function normalizeCookieHeaders(headers) {
  const outgoingHeaders = new Headers();
  for (const [name, header] of headers) {
    if (name === "set-cookie") {
      for (const cookie of normalizeCookieHeader(header)) {
        outgoingHeaders.append("set-cookie", cookie);
      }
    } else {
      outgoingHeaders.set(name, joinHeaders(header));
    }
  }
  return outgoingHeaders;
}

const errorHandler$2 = function(error, event) {
  event.node.res.end(`[Error Handler] ${error.stack}`);
};

function defineNitroErrorHandler(handler) {
  return handler;
}

const errorHandler$1 = defineNitroErrorHandler(
  function defaultNitroErrorHandler(error, event) {
    const res = defaultHandler(error, event);
    setResponseHeaders(event, res.headers);
    setResponseStatus(event, res.status, res.statusText);
    return send(event, JSON.stringify(res.body, null, 2));
  }
);
function defaultHandler(error, event, opts) {
  const isSensitive = error.unhandled || error.fatal;
  const statusCode = error.statusCode || 500;
  const statusMessage = error.statusMessage || "Server Error";
  const url = getRequestURL(event, { xForwardedHost: true, xForwardedProto: true });
  if (statusCode === 404) {
    const baseURL = "/";
    if (/^\/[^/]/.test(baseURL) && !url.pathname.startsWith(baseURL)) {
      const redirectTo = `${baseURL}${url.pathname.slice(1)}${url.search}`;
      return {
        status: 302,
        statusText: "Found",
        headers: { location: redirectTo },
        body: `Redirecting...`
      };
    }
  }
  if (isSensitive && !opts?.silent) {
    const tags = [error.unhandled && "[unhandled]", error.fatal && "[fatal]"].filter(Boolean).join(" ");
    console.error(`[request error] ${tags} [${event.method}] ${url}
`, error);
  }
  const headers = {
    "content-type": "application/json",
    // Prevent browser from guessing the MIME types of resources.
    "x-content-type-options": "nosniff",
    // Prevent error page from being embedded in an iframe
    "x-frame-options": "DENY",
    // Prevent browsers from sending the Referer header
    "referrer-policy": "no-referrer",
    // Disable the execution of any js
    "content-security-policy": "script-src 'none'; frame-ancestors 'none';"
  };
  setResponseStatus(event, statusCode, statusMessage);
  if (statusCode === 404 || !getResponseHeader(event, "cache-control")) {
    headers["cache-control"] = "no-cache";
  }
  const body = {
    error: true,
    url: url.href,
    statusCode,
    statusMessage,
    message: isSensitive ? "Server Error" : error.message,
    data: isSensitive ? void 0 : error.data
  };
  return {
    status: statusCode,
    statusText: statusMessage,
    headers,
    body
  };
}

const errorHandlers = [errorHandler$2, errorHandler$1];

async function errorHandler(error, event) {
  for (const handler of errorHandlers) {
    try {
      await handler(error, event, { defaultHandler });
      if (event.handled) {
        return; // Response handled
      }
    } catch(error) {
      // Handler itself thrown, log and continue
      console.error(error);
    }
  }
  // H3 will handle fallback
}

var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => __defNormalProp(obj, key + "" , value);
class Logger {
  constructor() {
    __publicField(this, "level");
    const config = useRuntimeConfig();
    const logLevel = (config.logLevel || "info").toLowerCase();
    switch (logLevel) {
      case "debug":
        this.level = 0 /* DEBUG */;
        break;
      case "info":
        this.level = 1 /* INFO */;
        break;
      case "warn":
        this.level = 2 /* WARN */;
        break;
      case "error":
        this.level = 3 /* ERROR */;
        break;
      default:
        this.level = 1 /* INFO */;
    }
  }
  formatMessage(level, message, meta) {
    const timestamp = (/* @__PURE__ */ new Date()).toISOString();
    const metaStr = meta ? ` ${JSON.stringify(meta)}` : "";
    return `[${timestamp}] ${level.toUpperCase()}: ${message}${metaStr}`;
  }
  debug(message, meta) {
    if (this.level <= 0 /* DEBUG */) {
      console.log(this.formatMessage("debug", message, meta));
    }
  }
  info(message, meta) {
    if (this.level <= 1 /* INFO */) {
      console.log(this.formatMessage("info", message, meta));
    }
  }
  warn(message, meta) {
    if (this.level <= 2 /* WARN */) {
      console.warn(this.formatMessage("warn", message, meta));
    }
  }
  error(message, meta) {
    if (this.level <= 3 /* ERROR */) {
      console.error(this.formatMessage("error", message, meta));
    }
  }
}
const logger = new Logger();
async function logAuditAction(data) {
  try {
    const { query } = await Promise.resolve().then(function () { return database; });
    await query(
      `INSERT INTO audit_logs 
       (user_id, username, user_type, action, description, ip, user_agent, 
        path, method, status_code, response_time, metadata, created_at) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
      [
        data.userId || null,
        data.username || null,
        data.userType || null,
        data.action,
        data.description,
        data.ip || null,
        data.userAgent || null,
        data.path || null,
        data.method || null,
        data.statusCode || null,
        data.responseTime || null,
        data.metadata ? JSON.stringify(data.metadata) : null
      ]
    );
    logger.info("\u5BA1\u8BA1\u65E5\u5FD7\u5DF2\u8BB0\u5F55", { action: data.action, userId: data.userId });
  } catch (error) {
    logger.error("\u8BB0\u5F55\u5BA1\u8BA1\u65E5\u5FD7\u5931\u8D25", { error: error.message, data });
  }
}
function getClientIP(event) {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j;
  try {
    return getRequestIP(event, { xForwardedFor: true }) || void 0;
  } catch {
    const headers = ((_b = (_a = event.node) == null ? void 0 : _a.req) == null ? void 0 : _b.headers) || {};
    return ((_d = (_c = headers["x-forwarded-for"]) == null ? void 0 : _c.split(",")[0]) == null ? void 0 : _d.trim()) || headers["x-real-ip"] || headers["x-client-ip"] || ((_g = (_f = (_e = event.node) == null ? void 0 : _e.req) == null ? void 0 : _f.connection) == null ? void 0 : _g.remoteAddress) || ((_j = (_i = (_h = event.node) == null ? void 0 : _h.req) == null ? void 0 : _i.socket) == null ? void 0 : _j.remoteAddress) || void 0;
  }
}
function extractAuditInfo(event) {
  var _a, _b, _c, _d;
  return {
    ip: getClientIP(event) || void 0,
    userAgent: getHeader(event, "user-agent") || void 0,
    path: ((_b = (_a = event.node) == null ? void 0 : _a.req) == null ? void 0 : _b.url) || void 0,
    method: ((_d = (_c = event.node) == null ? void 0 : _c.req) == null ? void 0 : _d.method) || void 0
  };
}
async function logAdminAction(adminId, action, description, metadata) {
  try {
    await logAuditAction({
      userId: adminId,
      userType: "admin",
      action,
      description,
      metadata
    });
  } catch (error) {
    logger.error("\u8BB0\u5F55\u7BA1\u7406\u5458\u64CD\u4F5C\u65E5\u5FD7\u5931\u8D25", { error: error.message, adminId, action });
  }
}

let pool = null;
function getPool() {
  if (!pool) {
    const config = useRuntimeConfig();
    pool = mysql.createPool({
      host: config.dbHost,
      port: parseInt(config.dbPort),
      user: config.dbUser,
      password: config.dbPassword,
      database: config.dbName,
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0,
      charset: "utf8mb4",
      timezone: "+08:00"
    });
    logger.info("\u6570\u636E\u5E93\u8FDE\u63A5\u6C60\u5DF2\u521B\u5EFA");
  }
  return pool;
}
async function query(sql, params) {
  const pool2 = getPool();
  const [results] = await pool2.execute(sql, params);
  return results;
}
async function executeTransaction(transactionCallback) {
  const pool2 = getPool();
  const connection = await pool2.getConnection();
  try {
    await connection.beginTransaction();
    const result = await transactionCallback(connection);
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}
async function queryInTransaction(connection, sql, params) {
  const [results] = await connection.execute(sql, params);
  return results;
}
async function transaction(callback) {
  const pool2 = getPool();
  const connection = await pool2.getConnection();
  try {
    await connection.beginTransaction();
    const result = await callback(connection);
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}

const database = /*#__PURE__*/Object.freeze({
  __proto__: null,
  executeTransaction: executeTransaction,
  getPool: getPool,
  query: query,
  queryInTransaction: queryInTransaction,
  transaction: transaction
});

let jwtConfigCache = null;
let jwtConfigCacheTime = 0;
const JWT_CONFIG_CACHE_TTL = 5 * 60 * 1e3;
async function getJWTConfig() {
  const now = Date.now();
  if (jwtConfigCache && now - jwtConfigCacheTime < JWT_CONFIG_CACHE_TTL) {
    console.log("\u{1F511} \u4F7F\u7528\u7F13\u5B58\u7684JWT\u914D\u7F6E:", {
      jwtSecret: jwtConfigCache.jwtSecret ? "***\u5DF2\u8BBE\u7F6E***" : "\u672A\u8BBE\u7F6E",
      jwtExpiresIn: jwtConfigCache.jwtExpiresIn,
      jwtAdminExpiresIn: jwtConfigCache.jwtAdminExpiresIn,
      accessTokenSecret: jwtConfigCache.accessTokenSecret ? "***\u5DF2\u8BBE\u7F6E***" : "\u672A\u8BBE\u7F6E",
      refreshTokenSecret: jwtConfigCache.refreshTokenSecret ? "***\u5DF2\u8BBE\u7F6E***" : "\u672A\u8BBE\u7F6E",
      cacheAge: Math.floor((now - jwtConfigCacheTime) / 1e3) + "\u79D2"
    });
    return jwtConfigCache;
  }
  try {
    const result = await query(
      "SELECT setting_value FROM system_settings WHERE setting_key = ?",
      ["security_settings"]
    );
    let jwtConfig = {
      jwtSecret: "mengtutv_jwt_secret_production_key",
      jwtExpiresIn: "24h",
      jwtAdminExpiresIn: "12h",
      accessTokenSecret: "mengtutv_access_token_secret",
      refreshTokenSecret: "mengtutv_refresh_token_secret"
    };
    if (result.length > 0 && result[0].setting_value) {
      try {
        const securitySettings = JSON.parse(result[0].setting_value);
        jwtConfig = {
          jwtSecret: securitySettings.jwtSecret || jwtConfig.jwtSecret,
          jwtExpiresIn: securitySettings.jwtExpiresIn || jwtConfig.jwtExpiresIn,
          jwtAdminExpiresIn: securitySettings.jwtAdminExpiresIn || jwtConfig.jwtAdminExpiresIn,
          accessTokenSecret: securitySettings.accessTokenSecret || jwtConfig.accessTokenSecret,
          refreshTokenSecret: securitySettings.refreshTokenSecret || jwtConfig.refreshTokenSecret
        };
      } catch (parseError) {
        console.error("\u89E3\u6790JWT\u914D\u7F6E\u5931\u8D25\uFF0C\u4F7F\u7528\u9ED8\u8BA4\u914D\u7F6E:", parseError);
      }
    }
    jwtConfigCache = jwtConfig;
    jwtConfigCacheTime = now;
    console.log("\u{1F511} \u4ECE\u6570\u636E\u5E93\u52A0\u8F7DJWT\u914D\u7F6E:", {
      jwtSecret: jwtConfig.jwtSecret ? "***\u5DF2\u8BBE\u7F6E***" : "\u672A\u8BBE\u7F6E",
      jwtExpiresIn: jwtConfig.jwtExpiresIn,
      jwtAdminExpiresIn: jwtConfig.jwtAdminExpiresIn,
      accessTokenSecret: jwtConfig.accessTokenSecret ? "***\u5DF2\u8BBE\u7F6E***" : "\u672A\u8BBE\u7F6E",
      refreshTokenSecret: jwtConfig.refreshTokenSecret ? "***\u5DF2\u8BBE\u7F6E***" : "\u672A\u8BBE\u7F6E",
      source: "\u6570\u636E\u5E93"
    });
    return jwtConfig;
  } catch (error) {
    console.error("\u83B7\u53D6JWT\u914D\u7F6E\u5931\u8D25\uFF0C\u4F7F\u7528\u9ED8\u8BA4\u914D\u7F6E:", error);
    const defaultConfig = {
      jwtSecret: "mengtutv_jwt_secret_production_key",
      jwtExpiresIn: "24h",
      jwtAdminExpiresIn: "12h",
      accessTokenSecret: "mengtutv_access_token_secret",
      refreshTokenSecret: "mengtutv_refresh_token_secret"
    };
    jwtConfigCache = defaultConfig;
    jwtConfigCacheTime = now;
    console.log("\u{1F511} \u4F7F\u7528\u9ED8\u8BA4JWT\u914D\u7F6E:", {
      jwtSecret: defaultConfig.jwtSecret ? "***\u5DF2\u8BBE\u7F6E***" : "\u672A\u8BBE\u7F6E",
      jwtExpiresIn: defaultConfig.jwtExpiresIn,
      jwtAdminExpiresIn: defaultConfig.jwtAdminExpiresIn,
      accessTokenSecret: defaultConfig.accessTokenSecret ? "***\u5DF2\u8BBE\u7F6E***" : "\u672A\u8BBE\u7F6E",
      refreshTokenSecret: defaultConfig.refreshTokenSecret ? "***\u5DF2\u8BBE\u7F6E***" : "\u672A\u8BBE\u7F6E",
      source: "\u9ED8\u8BA4\u914D\u7F6E"
    });
    return defaultConfig;
  }
}
async function initJWTConfig() {
  try {
    console.log("\u{1F680} \u5F00\u59CB\u521D\u59CB\u5316JWT\u914D\u7F6E...");
    await getJWTConfig();
    console.log("\u2705 JWT\u914D\u7F6E\u521D\u59CB\u5316\u6210\u529F");
  } catch (error) {
    console.error("\u274C JWT\u914D\u7F6E\u521D\u59CB\u5316\u5931\u8D25:", error);
  }
}
async function generateUserAccessToken(user) {
  var _a;
  const jwtConfig = await getJWTConfig();
  const payload = {
    id: user.id,
    username: user.username,
    email: user.email,
    avatar: user.avatar,
    status: user.status,
    type: "user"
  };
  console.log("\u{1F3AB} \u751F\u6210\u7528\u6237\u8BBF\u95EE\u4EE4\u724C:", {
    userId: user.id,
    username: user.username,
    expiresIn: jwtConfig.jwtExpiresIn,
    secretLength: ((_a = jwtConfig.jwtSecret) == null ? void 0 : _a.length) || 0
  });
  const token = jwt.sign(payload, jwtConfig.jwtSecret, { expiresIn: jwtConfig.jwtExpiresIn });
  console.log("\u2705 \u7528\u6237\u4EE4\u724C\u751F\u6210\u6210\u529F:", {
    tokenLength: token.length,
    tokenPrefix: token.substring(0, 20) + "..."
  });
  return token;
}
async function generateAdminAccessToken(admin) {
  const jwtConfig = await getJWTConfig();
  const payload = {
    id: admin.id,
    username: admin.username,
    email: admin.email,
    real_name: admin.real_name,
    avatar: admin.avatar,
    home_path: admin.home_path,
    status: admin.status,
    type: "admin"
  };
  return jwt.sign(payload, jwtConfig.jwtSecret, { expiresIn: jwtConfig.jwtAdminExpiresIn });
}
async function generateUserRefreshToken(user) {
  const jwtConfig = await getJWTConfig();
  return jwt.sign(
    { userId: user.id, username: user.username, type: "user" },
    jwtConfig.refreshTokenSecret,
    { expiresIn: "30d" }
  );
}
async function generateAdminRefreshToken(admin) {
  const jwtConfig = await getJWTConfig();
  return jwt.sign(
    { userId: admin.id, username: admin.username, type: "admin" },
    jwtConfig.refreshTokenSecret,
    { expiresIn: "7d" }
    // 管理员刷新令牌有效期较短
  );
}
function verifyUserAccessToken(event) {
  const authHeader = getHeader(event, "Authorization");
  console.log("\u{1F50D} \u9A8C\u8BC1\u7528\u6237\u8BBF\u95EE\u4EE4\u724C:", {
    hasAuthHeader: !!authHeader,
    authHeaderPrefix: (authHeader == null ? void 0 : authHeader.substring(0, 20)) || "none",
    url: event.node.req.url
  });
  if (!(authHeader == null ? void 0 : authHeader.startsWith("Bearer "))) {
    console.log("\u274C \u65E0\u6548\u7684Authorization\u5934\u90E8\u683C\u5F0F");
    return null;
  }
  const token = authHeader.split(" ")[1];
  try {
    const jwtSecret = (jwtConfigCache == null ? void 0 : jwtConfigCache.jwtSecret) || useRuntimeConfig().jwtSecret;
    console.log("\u{1F511} \u4F7F\u7528JWT\u5BC6\u94A5\u9A8C\u8BC1:", {
      tokenLength: token.length,
      tokenPrefix: token.substring(0, 20) + "...",
      secretSource: (jwtConfigCache == null ? void 0 : jwtConfigCache.jwtSecret) ? "\u6570\u636E\u5E93\u7F13\u5B58" : "\u73AF\u5883\u53D8\u91CF",
      secretLength: (jwtSecret == null ? void 0 : jwtSecret.length) || 0
    });
    const decoded = jwt.verify(token, jwtSecret);
    if (decoded.type !== "user") {
      console.log("\u274C \u4EE4\u724C\u7C7B\u578B\u4E0D\u5339\u914D:", decoded.type);
      return null;
    }
    console.log("\u2705 \u7528\u6237\u4EE4\u724C\u9A8C\u8BC1\u6210\u529F:", {
      userId: decoded.id,
      username: decoded.username,
      type: decoded.type,
      exp: new Date(decoded.exp * 1e3).toISOString()
    });
    return decoded;
  } catch (error) {
    console.error("\u274C \u7528\u6237\u4EE4\u724C\u9A8C\u8BC1\u5931\u8D25:", {
      error: error.message,
      tokenPrefix: token.substring(0, 20) + "..."
    });
    return null;
  }
}
function verifyAdminAccessToken(event) {
  const authHeader = getHeader(event, "Authorization");
  if (!(authHeader == null ? void 0 : authHeader.startsWith("Bearer "))) {
    return null;
  }
  const token = authHeader.split(" ")[1];
  try {
    const jwtSecret = (jwtConfigCache == null ? void 0 : jwtConfigCache.jwtSecret) || useRuntimeConfig().jwtSecret;
    const decoded = jwt.verify(token, jwtSecret);
    if (decoded.type !== "admin") {
      return null;
    }
    return decoded;
  } catch (error) {
    console.error("Admin token verification failed:", error);
    return null;
  }
}
function verifyRefreshToken(token) {
  try {
    const refreshTokenSecret = (jwtConfigCache == null ? void 0 : jwtConfigCache.refreshTokenSecret) || useRuntimeConfig().refreshTokenSecret || useRuntimeConfig().jwtSecret;
    const decoded = jwt.verify(token, refreshTokenSecret);
    return decoded;
  } catch (error) {
    console.error("Refresh token verification failed:", error);
    return null;
  }
}
function verifyToken(token) {
  try {
    const config = useRuntimeConfig();
    const decoded = jwt.verify(token, config.jwtSecret);
    return decoded;
  } catch (error) {
    console.error("Token verification failed:", error);
    return null;
  }
}
async function hashPassword(password) {
  const saltRounds = 10;
  return bcrypt.hash(password, saltRounds);
}
async function verifyPassword(password, hash) {
  console.log("\u5BC6\u7801\u9A8C\u8BC1\u8BE6\u60C5:");
  console.log("\u8F93\u5165\u5BC6\u7801\u957F\u5EA6:", password.length);
  console.log("\u5B58\u50A8\u7684\u54C8\u5E0C\u503C:", hash);
  console.log("\u54C8\u5E0C\u503C\u683C\u5F0F:", hash.startsWith("$2") ? "bcrypt\u683C\u5F0F" : "\u5176\u4ED6\u683C\u5F0F");
  try {
    const result = await bcrypt.compare(password, hash);
    console.log("bcrypt.compare\u7ED3\u679C:", result);
    return result;
  } catch (error) {
    console.error("\u5BC6\u7801\u9A8C\u8BC1\u51FA\u9519:", error);
    return false;
  }
}
async function findUserByCredentials(identifier) {
  try {
    console.log("\u67E5\u627E\u7528\u6237:", identifier);
    const users = await query(
      `SELECT id, username, email, phone, password_hash, status, user_type,
              created_at, updated_at
       FROM users
       WHERE (email = ? OR phone = ?) AND status = 1`,
      [identifier, identifier]
    );
    console.log("\u6570\u636E\u5E93\u67E5\u8BE2\u7ED3\u679C:", users.length > 0 ? "\u627E\u5230\u7528\u6237" : "\u672A\u627E\u5230\u7528\u6237");
    if (users.length > 0) {
      console.log("\u7528\u6237\u4FE1\u606F:", {
        id: users[0].id,
        username: users[0].username,
        email: users[0].email,
        user_type: users[0].user_type,
        status: users[0].status
      });
    }
    return users.length > 0 ? users[0] : null;
  } catch (error) {
    console.error("Error finding user:", error);
    return null;
  }
}
async function findAdminByCredentials(identifier) {
  try {
    const admins = await query(
      `SELECT id, username, email, phone, password, real_name, avatar,
              home_path, status, created_at, updated_at, last_login_at
       FROM admins
       WHERE (username = ? OR email = ? OR phone = ?) AND status = 1`,
      [identifier, identifier, identifier]
    );
    return admins.length > 0 ? admins[0] : null;
  } catch (error) {
    console.error("Error finding admin:", error);
    return null;
  }
}
async function findUserById(id) {
  try {
    const users = await query(
      `SELECT id, username, email, phone, avatar, status,
              created_at, updated_at
       FROM users
       WHERE id = ? AND status = 1`,
      [id]
    );
    return users.length > 0 ? users[0] : null;
  } catch (error) {
    console.error("Error finding user by ID:", error);
    return null;
  }
}
async function findAdminById(id) {
  try {
    const admins = await query(
      `SELECT id, username, email, phone, real_name, avatar,
              home_path, status, created_at, updated_at, last_login_at
       FROM admins
       WHERE id = ? AND status = 1`,
      [id]
    );
    return admins.length > 0 ? admins[0] : null;
  } catch (error) {
    console.error("Error finding admin by ID:", error);
    return null;
  }
}
async function updateUserLastLogin(userId) {
  try {
    await query(
      "UPDATE users SET updated_at = NOW() WHERE id = ?",
      [userId]
    );
  } catch (error) {
    console.error("Error updating user last login:", error);
  }
}
async function updateAdminLastLogin(adminId) {
  try {
    await query(
      "UPDATE admins SET last_login_at = NOW(), updated_at = NOW() WHERE id = ?",
      [adminId]
    );
  } catch (error) {
    console.error("Error updating admin last login:", error);
  }
}
async function getAdminPermissions(adminId) {
  try {
    const permissions = await query(
      `SELECT DISTINCT arp.permission_code
       FROM admin_role_relations arr
       JOIN admin_role_permissions arp ON arr.role_id = arp.role_id
       WHERE arr.admin_id = ?`,
      [adminId]
    );
    return permissions.map((p) => p.permission_code);
  } catch (error) {
    console.error("Error getting admin permissions:", error);
    return [];
  }
}
function generateAccessToken(user) {
  return generateUserAccessToken(user);
}
function generateRefreshToken(user) {
  return generateUserRefreshToken(user);
}
async function verifyAdminToken(event) {
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      return {
        success: false,
        message: "\u7BA1\u7406\u5458\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"
      };
    }
    const admin = await findAdminById(adminPayload.id);
    if (!admin || admin.status !== 1) {
      return {
        success: false,
        message: "\u7BA1\u7406\u5458\u8D26\u6237\u5DF2\u88AB\u7981\u7528"
      };
    }
    return {
      success: true,
      message: "\u8BA4\u8BC1\u6210\u529F",
      admin: adminPayload
    };
  } catch (error) {
    console.error("\u7BA1\u7406\u5458\u4EE4\u724C\u9A8C\u8BC1\u5931\u8D25:", error);
    return {
      success: false,
      message: "\u8BA4\u8BC1\u5931\u8D25"
    };
  }
}

const _r3Gx3RO1AbyDMX4pPkDNsoLGXuUvF4jkd342QFwmxaE = async () => {
  console.log("\u{1F680} \u5F00\u59CB\u521D\u59CB\u5316\u5E94\u7528...");
  await initJWTConfig();
  console.log("\u2705 \u5E94\u7528\u521D\u59CB\u5316\u5B8C\u6210");
};

const plugins = [
  _r3Gx3RO1AbyDMX4pPkDNsoLGXuUvF4jkd342QFwmxaE
];

const assets = {};

const _DRIVE_LETTER_START_RE = /^[A-Za-z]:\//;
function normalizeWindowsPath(input = "") {
  if (!input) {
    return input;
  }
  return input.replace(/\\/g, "/").replace(_DRIVE_LETTER_START_RE, (r) => r.toUpperCase());
}
const _IS_ABSOLUTE_RE = /^[/\\](?![/\\])|^[/\\]{2}(?!\.)|^[A-Za-z]:[/\\]/;
const _DRIVE_LETTER_RE = /^[A-Za-z]:$/;
function cwd() {
  if (typeof process !== "undefined" && typeof process.cwd === "function") {
    return process.cwd().replace(/\\/g, "/");
  }
  return "/";
}
const resolve = function(...arguments_) {
  arguments_ = arguments_.map((argument) => normalizeWindowsPath(argument));
  let resolvedPath = "";
  let resolvedAbsolute = false;
  for (let index = arguments_.length - 1; index >= -1 && !resolvedAbsolute; index--) {
    const path = index >= 0 ? arguments_[index] : cwd();
    if (!path || path.length === 0) {
      continue;
    }
    resolvedPath = `${path}/${resolvedPath}`;
    resolvedAbsolute = isAbsolute(path);
  }
  resolvedPath = normalizeString(resolvedPath, !resolvedAbsolute);
  if (resolvedAbsolute && !isAbsolute(resolvedPath)) {
    return `/${resolvedPath}`;
  }
  return resolvedPath.length > 0 ? resolvedPath : ".";
};
function normalizeString(path, allowAboveRoot) {
  let res = "";
  let lastSegmentLength = 0;
  let lastSlash = -1;
  let dots = 0;
  let char = null;
  for (let index = 0; index <= path.length; ++index) {
    if (index < path.length) {
      char = path[index];
    } else if (char === "/") {
      break;
    } else {
      char = "/";
    }
    if (char === "/") {
      if (lastSlash === index - 1 || dots === 1) ; else if (dots === 2) {
        if (res.length < 2 || lastSegmentLength !== 2 || res[res.length - 1] !== "." || res[res.length - 2] !== ".") {
          if (res.length > 2) {
            const lastSlashIndex = res.lastIndexOf("/");
            if (lastSlashIndex === -1) {
              res = "";
              lastSegmentLength = 0;
            } else {
              res = res.slice(0, lastSlashIndex);
              lastSegmentLength = res.length - 1 - res.lastIndexOf("/");
            }
            lastSlash = index;
            dots = 0;
            continue;
          } else if (res.length > 0) {
            res = "";
            lastSegmentLength = 0;
            lastSlash = index;
            dots = 0;
            continue;
          }
        }
        if (allowAboveRoot) {
          res += res.length > 0 ? "/.." : "..";
          lastSegmentLength = 2;
        }
      } else {
        if (res.length > 0) {
          res += `/${path.slice(lastSlash + 1, index)}`;
        } else {
          res = path.slice(lastSlash + 1, index);
        }
        lastSegmentLength = index - lastSlash - 1;
      }
      lastSlash = index;
      dots = 0;
    } else if (char === "." && dots !== -1) {
      ++dots;
    } else {
      dots = -1;
    }
  }
  return res;
}
const isAbsolute = function(p) {
  return _IS_ABSOLUTE_RE.test(p);
};
const dirname = function(p) {
  const segments = normalizeWindowsPath(p).replace(/\/$/, "").split("/").slice(0, -1);
  if (segments.length === 1 && _DRIVE_LETTER_RE.test(segments[0])) {
    segments[0] += "/";
  }
  return segments.join("/") || (isAbsolute(p) ? "/" : ".");
};

function readAsset (id) {
  const serverDir = dirname(fileURLToPath(globalThis._importMeta_.url));
  return promises.readFile(resolve(serverDir, assets[id].path))
}

const publicAssetBases = {};

function isPublicAssetURL(id = '') {
  if (assets[id]) {
    return true
  }
  for (const base in publicAssetBases) {
    if (id.startsWith(base)) { return true }
  }
  return false
}

function getAsset (id) {
  return assets[id]
}

const METHODS = /* @__PURE__ */ new Set(["HEAD", "GET"]);
const EncodingMap = { gzip: ".gz", br: ".br" };
const _ch4GQ6 = eventHandler((event) => {
  if (event.method && !METHODS.has(event.method)) {
    return;
  }
  let id = decodePath(
    withLeadingSlash(withoutTrailingSlash(parseURL(event.path).pathname))
  );
  let asset;
  const encodingHeader = String(
    getRequestHeader(event, "accept-encoding") || ""
  );
  const encodings = [
    ...encodingHeader.split(",").map((e) => EncodingMap[e.trim()]).filter(Boolean).sort(),
    ""
  ];
  if (encodings.length > 1) {
    appendResponseHeader(event, "Vary", "Accept-Encoding");
  }
  for (const encoding of encodings) {
    for (const _id of [id + encoding, joinURL(id, "index.html" + encoding)]) {
      const _asset = getAsset(_id);
      if (_asset) {
        asset = _asset;
        id = _id;
        break;
      }
    }
  }
  if (!asset) {
    if (isPublicAssetURL(id)) {
      removeResponseHeader(event, "Cache-Control");
      throw createError$1({ statusCode: 404 });
    }
    return;
  }
  const ifNotMatch = getRequestHeader(event, "if-none-match") === asset.etag;
  if (ifNotMatch) {
    setResponseStatus(event, 304, "Not Modified");
    return "";
  }
  const ifModifiedSinceH = getRequestHeader(event, "if-modified-since");
  const mtimeDate = new Date(asset.mtime);
  if (ifModifiedSinceH && asset.mtime && new Date(ifModifiedSinceH) >= mtimeDate) {
    setResponseStatus(event, 304, "Not Modified");
    return "";
  }
  if (asset.type && !getResponseHeader(event, "Content-Type")) {
    setResponseHeader(event, "Content-Type", asset.type);
  }
  if (asset.etag && !getResponseHeader(event, "ETag")) {
    setResponseHeader(event, "ETag", asset.etag);
  }
  if (asset.mtime && !getResponseHeader(event, "Last-Modified")) {
    setResponseHeader(event, "Last-Modified", mtimeDate.toUTCString());
  }
  if (asset.encoding && !getResponseHeader(event, "Content-Encoding")) {
    setResponseHeader(event, "Content-Encoding", asset.encoding);
  }
  if (asset.size > 0 && !getResponseHeader(event, "Content-Length")) {
    setResponseHeader(event, "Content-Length", asset.size);
  }
  return readAsset(id);
});

const _QOUi2R = defineEventHandler(async (event) => {
  const startTime = Date.now();
  setHeader(event, "Access-Control-Allow-Origin", "*");
  setHeader(event, "Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  setHeader(event, "Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
  setHeader(event, "Access-Control-Allow-Credentials", "true");
  if (getMethod(event) === "OPTIONS") {
    setResponseStatus(event, 204);
    return "";
  }
  const auditInfo = extractAuditInfo(event);
  logger.info(`${getMethod(event)} ${getRequestURL(event).pathname}`, {
    ip: auditInfo.ip,
    userAgent: auditInfo.userAgent
  });
  event.node.res.on("finish", () => {
    const responseTime = Date.now() - startTime;
    logger.debug(`Request completed in ${responseTime}ms`, {
      method: getMethod(event),
      path: getRequestURL(event).pathname,
      statusCode: event.node.res.statusCode,
      responseTime
    });
  });
});

const _g17vFA = defineEventHandler(async (event) => {
  const url = event.node.req.url || "";
  if (!url.startsWith("/api/admin/") && !url.startsWith("/api/auth/admin/")) {
    return;
  }
  if (url === "/api/auth/admin/login" || url === "/api/auth/admin/logout" || url.startsWith("/api/admin/dramas") || url.startsWith("/api/admin/talent-management") || url.startsWith("/api/admin/brand-management") || url.startsWith("/api/admin/news") || url.startsWith("/api/admin/banners") || url.startsWith("/api/admin/content-management/tags") || url.startsWith("/api/admin/content-management/platforms") || url.startsWith("/api/admin/recharge-records") || url.startsWith("/api/admin/posts")) {
    return;
  }
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError$1({
        statusCode: 401,
        statusMessage: "\u7BA1\u7406\u5458\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"
      });
    }
    const admin = await findAdminById(adminPayload.id);
    if (!admin || admin.status !== 1) {
      throw createError$1({
        statusCode: 403,
        statusMessage: "\u7BA1\u7406\u5458\u8D26\u6237\u5DF2\u88AB\u7981\u7528"
      });
    }
    event.context.admin = adminPayload;
    event.context.adminInfo = admin;
  } catch (error) {
    logger.warn("\u7BA1\u7406\u5458\u8BA4\u8BC1\u5931\u8D25", {
      url: event.node.req.url,
      error: error.message,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError$1({
      statusCode: 401,
      statusMessage: "\u8BA4\u8BC1\u5931\u8D25"
    });
  }
});

function useResponseSuccess(data, message = "ok") {
  return {
    success: true,
    code: 0,
    data,
    message,
    error: null
  };
}
function useResponseError(message, error = null) {
  return {
    success: false,
    code: -1,
    data: null,
    error,
    message
  };
}
function validationErrorResponse(event, message = "Validation Error", errors = null) {
  setResponseStatus(event, 400);
  return useResponseError(message, errors);
}
function serverErrorResponse(event, message = "Internal Server Error", error = null) {
  setResponseStatus(event, 500);
  return useResponseError(message, error);
}

const _Lg9gpB = defineEventHandler(async (event) => {
  return;
});

const _WHzsCB = defineEventHandler(async (event) => {
  var _a;
  if (!((_a = event.node.req.url) == null ? void 0 : _a.startsWith("/api/user/"))) {
    return;
  }
  const publicPaths = [
    "/api/auth/login",
    "/api/auth/register",
    "/api/auth/forgot-password",
    "/api/auth/reset-password"
  ];
  if (publicPaths.some((path) => {
    var _a2;
    return (_a2 = event.node.req.url) == null ? void 0 : _a2.startsWith(path);
  })) {
    return;
  }
  try {
    const userPayload = verifyUserAccessToken(event);
    if (!userPayload) {
      throw createError$1({
        statusCode: 401,
        statusMessage: "\u7528\u6237\u8BA4\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u767B\u5F55"
      });
    }
    const user = await findUserById(userPayload.id);
    if (!user || user.status !== 1) {
      throw createError$1({
        statusCode: 403,
        statusMessage: "\u7528\u6237\u8D26\u6237\u5DF2\u88AB\u7981\u7528"
      });
    }
    event.context.user = userPayload;
    event.context.userInfo = user;
  } catch (error) {
    logger.warn("\u7528\u6237\u8BA4\u8BC1\u5931\u8D25", {
      url: event.node.req.url,
      error: error.message,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError$1({
      statusCode: 401,
      statusMessage: "\u8BA4\u8BC1\u5931\u8D25"
    });
  }
});

const _lazy_PUKbFy = () => import('../routes/api/index.get.mjs');
const _lazy_IabVkQ = () => import('../routes/api/admin/index.get.mjs');
const _lazy_fEfHyC = () => import('../routes/api/admin/index.post.mjs');
const _lazy_wlcrEm = () => import('../routes/api/admin/actors/selector.get.mjs');
const _lazy_PevDWE = () => import('../routes/api/admin/index.get2.mjs');
const _lazy_mrr6ne = () => import('../routes/api/admin/audit-logs/stats.get.mjs');
const _lazy_CMOMBi = () => import('../routes/api/admin/banners/_id_.delete.mjs');
const _lazy_Cq7HYz = () => import('../routes/api/admin/banners/_id_.put.mjs');
const _lazy_qyx4su = () => import('../routes/api/admin/banners/batch-delete.post.mjs');
const _lazy_ZL2uWP = () => import('../routes/api/admin/index.get3.mjs');
const _lazy_1MR5rg = () => import('../routes/api/admin/index.post2.mjs');
const _lazy_0J5bXk = () => import('../routes/api/admin/banners/sort.post.mjs');
const _lazy_3r9n25 = () => import('../routes/api/admin/brand-management/_id_.delete.mjs');
const _lazy_sWRG1q = () => import('../routes/api/admin/brand-management/_id_.put.mjs');
const _lazy_oxFcQF = () => import('../routes/api/admin/brand-management/_id/status.put.mjs');
const _lazy_4KyJq6 = () => import('../routes/api/admin/index.get4.mjs');
const _lazy_Stg177 = () => import('../routes/api/admin/index.post3.mjs');
const _lazy_IabTru = () => import('../routes/api/admin/brand-management/selector.get.mjs');
const _lazy_4KpBkp = () => import('../routes/api/admin/content-management/platforms/_id_.delete.mjs');
const _lazy_GZ9Q6n = () => import('../routes/api/admin/content-management/platforms/_id_.put.mjs');
const _lazy_hGpaxs = () => import('../routes/api/admin/content-management/index.get.mjs');
const _lazy_HXJfFC = () => import('../routes/api/admin/content-management/index.post.mjs');
const _lazy_GmDtbE = () => import('../routes/api/admin/content-management/platforms/selector.get.mjs');
const _lazy_aAAQSU = () => import('../routes/api/admin/content-management/tags/_id_.delete.mjs');
const _lazy_jZuwuB = () => import('../routes/api/admin/content-management/tags/_id_.put.mjs');
const _lazy_V9WwaD = () => import('../routes/api/admin/content-management/index.get2.mjs');
const _lazy_U39uvH = () => import('../routes/api/admin/content-management/index.post2.mjs');
const _lazy_NL72MJ = () => import('../routes/api/admin/content-management/tags/selector.get.mjs');
const _lazy_jdqBuy = () => import('../routes/api/admin/dashboard.get.mjs');
const _lazy_QHlDa8 = () => import('../routes/api/admin/dramas/_id_.delete.mjs');
const _lazy_qj2aTy = () => import('../routes/api/admin/dramas/_id_.get.mjs');
const _lazy_xzwZML = () => import('../routes/api/admin/dramas/_id_.put.mjs');
const _lazy_Ml9d2v = () => import('../routes/api/admin/dramas/_id/additional-info.put.mjs');
const _lazy_mGch4r = () => import('../routes/api/admin/dramas/_id/documents.post.mjs');
const _lazy_FRYCdk = () => import('../routes/api/admin/dramas/_id/documents/_documentId_.delete.mjs');
const _lazy_uDNLdT = () => import('../routes/api/admin/dramas/_id/documents/_documentId_.put.mjs');
const _lazy_29i5Aj = () => import('../routes/api/admin/dramas/_id/documents/upload.post.mjs');
const _lazy_UzndH6 = () => import('../routes/api/admin/dramas/_id/funding.put.mjs');
const _lazy_bWm2XZ = () => import('../routes/api/admin/dramas/_id/investment-tiers.put.mjs');
const _lazy_I7HbUz = () => import('../routes/api/admin/dramas/_id/materials.get.mjs');
const _lazy_uWc9Cg = () => import('../routes/api/admin/dramas/_id/materials.post.mjs');
const _lazy_666ueW = () => import('../routes/api/admin/dramas/_id/materials/_materialId_.delete.mjs');
const _lazy_rP8d5O = () => import('../routes/api/admin/dramas/_id/schedule.put.mjs');
const _lazy_JyuLqZ = () => import('../routes/api/admin/index.get5.mjs');
const _lazy_LGEpqn = () => import('../routes/api/admin/index.post4.mjs');
const _lazy_RBr8Hc = () => import('../routes/api/admin/funds/_id_.delete.mjs');
const _lazy_qLrv8E = () => import('../routes/api/admin/funds/_id_.get.mjs');
const _lazy_GXZTzB = () => import('../routes/api/admin/funds/_id_.put.mjs');
const _lazy_SZYWv5 = () => import('../routes/api/admin/funds/_id/documents.post.mjs');
const _lazy_Zo3NVd = () => import('../routes/api/admin/funds/_id/documents/_documentId_.delete.mjs');
const _lazy_LFK_2R = () => import('../routes/api/admin/funds/_id/documents/_documentId_.put.mjs');
const _lazy_ZneGjK = () => import('../routes/api/admin/funds/_id/documents/upload.post.mjs');
const _lazy_9k6jse = () => import('../routes/api/admin/funds/_id/faqs.post.mjs');
const _lazy_zkfRPz = () => import('../routes/api/admin/funds/_id/faqs/_faqId_.delete.mjs');
const _lazy_sP05_C = () => import('../routes/api/admin/funds/_id/faqs/_faqId_.put.mjs');
const _lazy_u5Qsm3 = () => import('../routes/api/admin/funds/_id/fees.post.mjs');
const _lazy_f3fhvK = () => import('../routes/api/admin/funds/_id/fees/_feeId_.delete.mjs');
const _lazy_Uu34Ll = () => import('../routes/api/admin/funds/_id/fees/_feeId_.put.mjs');
const _lazy_OdKn8f = () => import('../routes/api/admin/funds/_id/highlights.post.mjs');
const _lazy_r7IYHA = () => import('../routes/api/admin/funds/_id/highlights/_highlightId_.delete.mjs');
const _lazy_GMgWxn = () => import('../routes/api/admin/funds/_id/highlights/_highlightId_.put.mjs');
const _lazy_r3IF8b = () => import('../routes/api/admin/funds/_id/performances.post.mjs');
const _lazy_bShDd1 = () => import('../routes/api/admin/funds/_id/performances/_performanceId_.delete.mjs');
const _lazy_ZFLDNx = () => import('../routes/api/admin/funds/_id/performances/_performanceId_.put.mjs');
const _lazy_rxiyrc = () => import('../routes/api/admin/funds/_id/success-cases.post.mjs');
const _lazy_tjo_CI = () => import('../routes/api/admin/funds/_id/success-cases/_caseId_.delete.mjs');
const _lazy_wSmEJg = () => import('../routes/api/admin/funds/_id/success-cases/_caseId_.put.mjs');
const _lazy_aGoMEb = () => import('../routes/api/admin/funds/_id/timelines.post.mjs');
const _lazy_YaZT_i = () => import('../routes/api/admin/funds/_id/timelines/_timelineId_.delete.mjs');
const _lazy_uEmiBo = () => import('../routes/api/admin/funds/_id/timelines/_timelineId_.put.mjs');
const _lazy_aDaa9Y = () => import('../routes/api/admin/funds/_id/usage-plans.post.mjs');
const _lazy_l0NWLs = () => import('../routes/api/admin/funds/_id/usage-plans/_planId_.delete.mjs');
const _lazy_ZswPZ0 = () => import('../routes/api/admin/funds/_id/usage-plans/_planId_.put.mjs');
const _lazy_7nEV76 = () => import('../routes/api/admin/index.get6.mjs');
const _lazy_L771RB = () => import('../routes/api/admin/index.post5.mjs');
const _lazy_IC9yJo = () => import('../routes/api/admin/funds/stats.get.mjs');
const _lazy_FUOtZH = () => import('../routes/api/admin/investment-records.get.mjs');
const _lazy_uTpFlr = () => import('../routes/api/admin/investment-records/_id/status.put.mjs');
const _lazy_kr_E7t = () => import('../routes/api/admin/index.get7.mjs');
const _lazy_h9V8TQ = () => import('../routes/api/admin/news/_id_.delete.mjs');
const _lazy_KoVH45 = () => import('../routes/api/admin/news/_id_.get.mjs');
const _lazy_UfmgGS = () => import('../routes/api/admin/news/_id_.put.mjs');
const _lazy_y9sh_2 = () => import('../routes/api/admin/news/_id/publish.post.mjs');
const _lazy_DCcjTr = () => import('../routes/api/admin/news/batch-operation.post.mjs');
const _lazy_AnUARu = () => import('../routes/api/admin/news/categories/all.get.mjs');
const _lazy_kQRgRZ = () => import('../routes/api/admin/index.get8.mjs');
const _lazy_PiHD5U = () => import('../routes/api/admin/index.post6.mjs');
const _lazy_HNhAQ2 = () => import('../routes/api/admin/news/stats.get.mjs');
const _lazy_DcOy7f = () => import('../routes/api/admin/news/tags/_id_.delete.mjs');
const _lazy_F7OvrX = () => import('../routes/api/admin/news/tags/all.get.mjs');
const _lazy_c7bHB8 = () => import('../routes/api/admin/news/index.get.mjs');
const _lazy_Qk5qmP = () => import('../routes/api/admin/news/index.post.mjs');
const _lazy_o2rXOt = () => import('../routes/api/admin/permissions.get.mjs');
const _lazy_mxozjo = () => import('../routes/api/admin/posts/_id_.delete.mjs');
const _lazy_tazvnl = () => import('../routes/api/admin/posts/_id_.put.mjs');
const _lazy_uvH1rY = () => import('../routes/api/admin/posts/_id/publish.post.mjs');
const _lazy_2x_Y1f = () => import('../routes/api/admin/posts/batch-operate.post.mjs');
const _lazy_Cr1u_o = () => import('../routes/api/admin/index.get9.mjs');
const _lazy_RO0ooO = () => import('../routes/api/admin/index.post7.mjs');
const _lazy_OAkg23 = () => import('../routes/api/admin/posts/stats.get.mjs');
const _lazy_NYiFE7 = () => import('../routes/api/admin/recharge-records.get.mjs');
const _lazy_UeM_UB = () => import('../routes/api/admin/recharge-records/_id/confirm-payment.put.mjs');
const _lazy_v89_2i = () => import('../routes/api/admin/recharge-records/_id/status.put.mjs');
const _lazy_nDpWPO = () => import('../routes/api/admin/roles.get.mjs');
const _lazy_ADZz4I = () => import('../routes/api/admin/roles.post.mjs');
const _lazy_s4hq48 = () => import('../routes/api/admin/roles/_id_.delete.mjs');
const _lazy_blmXSd = () => import('../routes/api/admin/roles/_id_.get.mjs');
const _lazy_TAGYrV = () => import('../routes/api/admin/roles/_id_.put.mjs');
const _lazy_We0oGH = () => import('../routes/api/admin/roles/_id/permissions.get.mjs');
const _lazy_Uia9C2 = () => import('../routes/api/admin/roles/_id/permissions.post.mjs');
const _lazy_7kI3oC = () => import('../routes/api/admin/roles/_id/status.patch.mjs');
const _lazy_VCaoaZ = () => import('../routes/api/admin/settings/contact.get.mjs');
const _lazy_bje_v2 = () => import('../routes/api/admin/settings/contact.post.mjs');
const _lazy_eFE1Si = () => import('../routes/api/admin/settings/cos.get.mjs');
const _lazy_iC0Hfb = () => import('../routes/api/admin/settings/cos.post.mjs');
const _lazy_E0StBN = () => import('../routes/api/admin/settings/email.get.mjs');
const _lazy_VhtNRJ = () => import('../routes/api/admin/settings/email.post.mjs');
const _lazy_NnKG53 = () => import('../routes/api/admin/settings/investment.get.mjs');
const _lazy_h3zNzb = () => import('../routes/api/admin/settings/investment.post.mjs');
const _lazy_0EsFrD = () => import('../routes/api/admin/settings/security.get.mjs');
const _lazy_eBrj1I = () => import('../routes/api/admin/settings/security.post.mjs');
const _lazy_Goxqvw = () => import('../routes/api/admin/settings/site.get.mjs');
const _lazy_edmGIG = () => import('../routes/api/admin/settings/site.post.mjs');
const _lazy_EKlbiN = () => import('../routes/api/admin/settings/sms.get.mjs');
const _lazy_nhRmbd = () => import('../routes/api/admin/settings/sms.post.mjs');
const _lazy_HQ017X = () => import('../routes/api/admin/settings/system-info.get.mjs');
const _lazy_vMxhRD = () => import('../routes/api/admin/system/menus.get.mjs');
const _lazy_bw6jcM = () => import('../routes/api/admin/system/menus.post.mjs');
const _lazy_avVPxB = () => import('../routes/api/admin/system/menus/_id_.delete.mjs');
const _lazy_OyUwCJ = () => import('../routes/api/admin/system/menus/_id_.get.mjs');
const _lazy_VjhodB = () => import('../routes/api/admin/system/menus/_id_.put.mjs');
const _lazy_HMUDaw = () => import('../routes/api/admin/system/menus/_id/status.patch.mjs');
const _lazy_dO__eO = () => import('../routes/api/admin/system/menus/name-exists.get.mjs');
const _lazy_kq8RXl = () => import('../routes/api/admin/system/menus/path-exists.get.mjs');
const _lazy_g7DVq6 = () => import('../routes/api/admin/system/menus/sort.patch.mjs');
const _lazy_3aHIum = () => import('../routes/api/admin/talent-management/_id_.delete.mjs');
const _lazy_OBFIi2 = () => import('../routes/api/admin/talent-management/_id_.get.mjs');
const _lazy_OeBLYA = () => import('../routes/api/admin/talent-management/_id_.put.mjs');
const _lazy_sFNSYi = () => import('../routes/api/admin/index.get10.mjs');
const _lazy_KVmoMX = () => import('../routes/api/admin/index.post8.mjs');
const _lazy_EfIJg2 = () => import('../routes/api/admin/upload/actor-avatar.post.mjs');
const _lazy_jL5N4z = () => import('../routes/api/admin/upload/banner-image.post.mjs');
const _lazy_8Twwwg = () => import('../routes/api/admin/upload/brand-image.post.mjs');
const _lazy_Y7sbJ7 = () => import('../routes/api/admin/upload/common.post.mjs');
const _lazy_fkIHjf = () => import('../routes/api/admin/upload/drama-cover.post.mjs');
const _lazy_67UG6U = () => import('../routes/api/admin/upload/drama-material.post.mjs');
const _lazy_ecP11U = () => import('../routes/api/admin/upload/file.post.mjs');
const _lazy_y0ugTp = () => import('../routes/api/admin/upload/news-image.post.mjs');
const _lazy_aD3wjp = () => import('../routes/api/admin/upload/platform-image.post.mjs');
const _lazy_G7XNf7 = () => import('../routes/api/admin/upload/site-asset.post.mjs');
const _lazy_eL7wQQ = () => import('../routes/api/admin/upload/site-logo.post.mjs');
const _lazy__wDeMz = () => import('../routes/api/admin/upload/site/favicon.post.mjs');
const _lazy_tDTquO = () => import('../routes/api/admin/upload/site/logo.post.mjs');
const _lazy_ZbntbL = () => import('../routes/api/admin/user-management/_id_.delete.mjs');
const _lazy_hQTMl0 = () => import('../routes/api/admin/user-management/_id_.get.mjs');
const _lazy_KnPXnR = () => import('../routes/api/admin/user-management/_id_.put.mjs');
const _lazy_WyHnjR = () => import('../routes/api/admin/user-management/_id/reset-password.put.mjs');
const _lazy_QH0zqi = () => import('../routes/api/admin/user-management/_id/status.put.mjs');
const _lazy_4NFx1J = () => import('../routes/api/admin/user-management/batch-delete.post.mjs');
const _lazy_zSmgj0 = () => import('../routes/api/admin/user-management/batch-status.post.mjs');
const _lazy_fb4aKT = () => import('../routes/api/admin/user-management/export.get.mjs');
const _lazy_6A3ucj = () => import('../routes/api/admin/index.get11.mjs');
const _lazy_kqVMkO = () => import('../routes/api/admin/index.post9.mjs');
const _lazy_BkeLfh = () => import('../routes/api/admin/user-management/stats.get.mjs');
const _lazy_wf2Uhv = () => import('../routes/api/auth/admin/info.get.mjs');
const _lazy_tZekeS = () => import('../routes/api/auth/admin/login.post.mjs');
const _lazy_2pSZ47 = () => import('../routes/api/auth/admin/logout.post.mjs');
const _lazy_PM2WU1 = () => import('../routes/api/auth/codes.get.mjs');
const _lazy_HnVYqM = () => import('../routes/api/auth/forgot-password.post.mjs');
const _lazy_Z164ph = () => import('../routes/api/auth/login.post.mjs');
const _lazy_v5PJY4 = () => import('../routes/api/auth/logout.post.mjs');
const _lazy_zGBDse = () => import('../routes/api/auth/refresh.post.mjs');
const _lazy_odYuJI = () => import('../routes/api/auth/register.post.mjs');
const _lazy_ZFzhrG = () => import('../routes/api/index.get2.mjs');
const _lazy_MwWa67 = () => import('../routes/api/debug/project.get.mjs');
const _lazy_VpwEH1 = () => import('../routes/api/dramas/_id_.get.mjs');
const _lazy_N7oh23 = () => import('../routes/api/index.get3.mjs');
const _lazy_WroOAz = () => import('../routes/api/funds/_code_.get.mjs');
const _lazy_SckIYE = () => import('../routes/api/index.get4.mjs');
const _lazy_2mnT4O = () => import('../routes/api/health.get.mjs');
const _lazy_RunIwg = () => import('../routes/api/index.get5.mjs');
const _lazy_MeqRBH = () => import('../routes/api/public/banners.get.mjs');
const _lazy_aZ92f3 = () => import('../routes/api/public/config.get.mjs');
const _lazy_SQojmg = () => import('../routes/api/public/news.get.mjs');
const _lazy_JC3hB4 = () => import('../routes/api/public/news/_id_.get.mjs');
const _lazy_USd0Gg = () => import('../routes/api/public/news/_id/view.post.mjs');
const _lazy_RmYBaP = () => import('../routes/api/public/news/categories.get.mjs');
const _lazy_BBLvNA = () => import('../routes/api/public/posts/agreement/_identifier_.get.mjs');
const _lazy_C4cDnT = () => import('../routes/api/public/settings/contact.get.mjs');
const _lazy_56MmOw = () => import('../routes/api/public/index.get.mjs');
const _lazy_lTJhZ7 = () => import('../routes/api/public/settings/site.get.mjs');
const _lazy_1XO5RM = () => import('../routes/api/public/system-info.get.mjs');
const _lazy_EW85fm = () => import('../routes/api/index.get6.mjs');
const _lazy_IBqPUS = () => import('../routes/api/users/change-password.post.mjs');
const _lazy_CMl8gp = () => import('../routes/api/users/dashboard.get.mjs');
const _lazy_HTeUHv = () => import('../routes/api/users/investments/create.post.mjs');
const _lazy_HH9khf = () => import('../routes/api/users/investments/records.get.mjs');
const _lazy_nJSFX2 = () => import('../routes/api/users/profile.get.mjs');
const _lazy_uPp7jp = () => import('../routes/api/users/profile.put.mjs');
const _lazy_43s9Fd = () => import('../routes/api/users/wallet/balance.get.mjs');
const _lazy_dHDUm0 = () => import('../routes/api/users/wallet/bank-cards.get.mjs');
const _lazy_IKbd3M = () => import('../routes/api/users/wallet/bank-cards.post.mjs');
const _lazy_Mw6TED = () => import('../routes/api/users/wallet/payment-methods.get.mjs');
const _lazy_Plzzjv = () => import('../routes/api/users/wallet/recharge.post.mjs');
const _lazy_NvfwRC = () => import('../routes/api/users/wallet/recharge/_orderId_.get.mjs');
const _lazy_AFVWCq = () => import('../routes/api/users/wallet/recharge/records.get.mjs');
const _lazy_WNGoQ0 = () => import('../routes/api/users/wallet/recharge/summary.get.mjs');
const _lazy_fJILX9 = () => import('../routes/api/users/wallet/withdraw.post.mjs');
const _lazy_cEDym1 = () => import('../routes/api/website/recent-investments.get.mjs');
const _lazy_sTm5xj = () => import('../routes/api/website/stats.get.mjs');
const _lazy_vZG9JB = () => import('../routes/_..._.mjs');

const handlers = [
  { route: '', handler: _ch4GQ6, lazy: false, middleware: true, method: undefined },
  { route: '', handler: _QOUi2R, lazy: false, middleware: true, method: undefined },
  { route: '', handler: _g17vFA, lazy: false, middleware: true, method: undefined },
  { route: '', handler: _Lg9gpB, lazy: false, middleware: true, method: undefined },
  { route: '', handler: _WHzsCB, lazy: false, middleware: true, method: undefined },
  { route: '/api/actors', handler: _lazy_PUKbFy, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/actors', handler: _lazy_IabVkQ, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/actors', handler: _lazy_fEfHyC, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/actors/selector', handler: _lazy_wlcrEm, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/audit-logs', handler: _lazy_PevDWE, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/audit-logs/stats', handler: _lazy_mrr6ne, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/banners/:id', handler: _lazy_CMOMBi, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/banners/:id', handler: _lazy_Cq7HYz, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/banners/batch-delete', handler: _lazy_qyx4su, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/banners', handler: _lazy_ZL2uWP, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/banners', handler: _lazy_1MR5rg, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/banners/sort', handler: _lazy_0J5bXk, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/brand-management/:id', handler: _lazy_3r9n25, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/brand-management/:id', handler: _lazy_sWRG1q, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/brand-management/:id/status', handler: _lazy_oxFcQF, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/brand-management', handler: _lazy_4KyJq6, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/brand-management', handler: _lazy_Stg177, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/brand-management/selector', handler: _lazy_IabTru, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/content-management/platforms/:id', handler: _lazy_4KpBkp, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/content-management/platforms/:id', handler: _lazy_GZ9Q6n, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/content-management/platforms', handler: _lazy_hGpaxs, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/content-management/platforms', handler: _lazy_HXJfFC, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/content-management/platforms/selector', handler: _lazy_GmDtbE, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/content-management/tags/:id', handler: _lazy_aAAQSU, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/content-management/tags/:id', handler: _lazy_jZuwuB, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/content-management/tags', handler: _lazy_V9WwaD, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/content-management/tags', handler: _lazy_U39uvH, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/content-management/tags/selector', handler: _lazy_NL72MJ, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/dashboard', handler: _lazy_jdqBuy, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/dramas/:id', handler: _lazy_QHlDa8, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/dramas/:id', handler: _lazy_qj2aTy, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/dramas/:id', handler: _lazy_xzwZML, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/dramas/:id/additional-info', handler: _lazy_Ml9d2v, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/dramas/:id/documents', handler: _lazy_mGch4r, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/dramas/:id/documents/:documentId', handler: _lazy_FRYCdk, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/dramas/:id/documents/:documentId', handler: _lazy_uDNLdT, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/dramas/:id/documents/upload', handler: _lazy_29i5Aj, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/dramas/:id/funding', handler: _lazy_UzndH6, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/dramas/:id/investment-tiers', handler: _lazy_bWm2XZ, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/dramas/:id/materials', handler: _lazy_I7HbUz, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/dramas/:id/materials', handler: _lazy_uWc9Cg, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/dramas/:id/materials/:materialId', handler: _lazy_666ueW, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/dramas/:id/schedule', handler: _lazy_rP8d5O, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/dramas', handler: _lazy_JyuLqZ, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/dramas', handler: _lazy_LGEpqn, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/funds/:id', handler: _lazy_RBr8Hc, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/funds/:id', handler: _lazy_qLrv8E, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/funds/:id', handler: _lazy_GXZTzB, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/funds/:id/documents', handler: _lazy_SZYWv5, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/funds/:id/documents/:documentId', handler: _lazy_Zo3NVd, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/funds/:id/documents/:documentId', handler: _lazy_LFK_2R, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/funds/:id/documents/upload', handler: _lazy_ZneGjK, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/funds/:id/faqs', handler: _lazy_9k6jse, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/funds/:id/faqs/:faqId', handler: _lazy_zkfRPz, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/funds/:id/faqs/:faqId', handler: _lazy_sP05_C, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/funds/:id/fees', handler: _lazy_u5Qsm3, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/funds/:id/fees/:feeId', handler: _lazy_f3fhvK, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/funds/:id/fees/:feeId', handler: _lazy_Uu34Ll, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/funds/:id/highlights', handler: _lazy_OdKn8f, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/funds/:id/highlights/:highlightId', handler: _lazy_r7IYHA, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/funds/:id/highlights/:highlightId', handler: _lazy_GMgWxn, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/funds/:id/performances', handler: _lazy_r3IF8b, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/funds/:id/performances/:performanceId', handler: _lazy_bShDd1, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/funds/:id/performances/:performanceId', handler: _lazy_ZFLDNx, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/funds/:id/success-cases', handler: _lazy_rxiyrc, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/funds/:id/success-cases/:caseId', handler: _lazy_tjo_CI, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/funds/:id/success-cases/:caseId', handler: _lazy_wSmEJg, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/funds/:id/timelines', handler: _lazy_aGoMEb, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/funds/:id/timelines/:timelineId', handler: _lazy_YaZT_i, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/funds/:id/timelines/:timelineId', handler: _lazy_uEmiBo, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/funds/:id/usage-plans', handler: _lazy_aDaa9Y, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/funds/:id/usage-plans/:planId', handler: _lazy_l0NWLs, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/funds/:id/usage-plans/:planId', handler: _lazy_ZswPZ0, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/funds', handler: _lazy_7nEV76, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/funds', handler: _lazy_L771RB, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/funds/stats', handler: _lazy_IC9yJo, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/investment-records', handler: _lazy_FUOtZH, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/investment-records/:id/status', handler: _lazy_uTpFlr, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/menus', handler: _lazy_kr_E7t, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/news/:id', handler: _lazy_h9V8TQ, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/news/:id', handler: _lazy_KoVH45, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/news/:id', handler: _lazy_UfmgGS, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/news/:id/publish', handler: _lazy_y9sh_2, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/news/batch-operation', handler: _lazy_DCcjTr, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/news/categories/all', handler: _lazy_AnUARu, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/news', handler: _lazy_kQRgRZ, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/news', handler: _lazy_PiHD5U, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/news/stats', handler: _lazy_HNhAQ2, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/news/tags/:id', handler: _lazy_DcOy7f, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/news/tags/all', handler: _lazy_F7OvrX, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/news/tags', handler: _lazy_c7bHB8, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/news/tags', handler: _lazy_Qk5qmP, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/permissions', handler: _lazy_o2rXOt, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/posts/:id', handler: _lazy_mxozjo, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/posts/:id', handler: _lazy_tazvnl, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/posts/:id/publish', handler: _lazy_uvH1rY, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/posts/batch-operate', handler: _lazy_2x_Y1f, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/posts', handler: _lazy_Cr1u_o, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/posts', handler: _lazy_RO0ooO, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/posts/stats', handler: _lazy_OAkg23, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/recharge-records', handler: _lazy_NYiFE7, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/recharge-records/:id/confirm-payment', handler: _lazy_UeM_UB, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/recharge-records/:id/status', handler: _lazy_v89_2i, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/roles', handler: _lazy_nDpWPO, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/roles', handler: _lazy_ADZz4I, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/roles/:id', handler: _lazy_s4hq48, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/roles/:id', handler: _lazy_blmXSd, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/roles/:id', handler: _lazy_TAGYrV, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/roles/:id/permissions', handler: _lazy_We0oGH, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/roles/:id/permissions', handler: _lazy_Uia9C2, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/roles/:id/status', handler: _lazy_7kI3oC, lazy: true, middleware: false, method: "patch" },
  { route: '/api/admin/settings/contact', handler: _lazy_VCaoaZ, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/settings/contact', handler: _lazy_bje_v2, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/settings/cos', handler: _lazy_eFE1Si, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/settings/cos', handler: _lazy_iC0Hfb, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/settings/email', handler: _lazy_E0StBN, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/settings/email', handler: _lazy_VhtNRJ, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/settings/investment', handler: _lazy_NnKG53, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/settings/investment', handler: _lazy_h3zNzb, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/settings/security', handler: _lazy_0EsFrD, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/settings/security', handler: _lazy_eBrj1I, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/settings/site', handler: _lazy_Goxqvw, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/settings/site', handler: _lazy_edmGIG, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/settings/sms', handler: _lazy_EKlbiN, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/settings/sms', handler: _lazy_nhRmbd, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/settings/system-info', handler: _lazy_HQ017X, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/system/menus', handler: _lazy_vMxhRD, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/system/menus', handler: _lazy_bw6jcM, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/system/menus/:id', handler: _lazy_avVPxB, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/system/menus/:id', handler: _lazy_OyUwCJ, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/system/menus/:id', handler: _lazy_VjhodB, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/system/menus/:id/status', handler: _lazy_HMUDaw, lazy: true, middleware: false, method: "patch" },
  { route: '/api/admin/system/menus/name-exists', handler: _lazy_dO__eO, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/system/menus/path-exists', handler: _lazy_kq8RXl, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/system/menus/sort', handler: _lazy_g7DVq6, lazy: true, middleware: false, method: "patch" },
  { route: '/api/admin/talent-management/:id', handler: _lazy_3aHIum, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/talent-management/:id', handler: _lazy_OBFIi2, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/talent-management/:id', handler: _lazy_OeBLYA, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/talent-management', handler: _lazy_sFNSYi, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/talent-management', handler: _lazy_KVmoMX, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/upload/actor-avatar', handler: _lazy_EfIJg2, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/upload/banner-image', handler: _lazy_jL5N4z, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/upload/brand-image', handler: _lazy_8Twwwg, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/upload/common', handler: _lazy_Y7sbJ7, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/upload/drama-cover', handler: _lazy_fkIHjf, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/upload/drama-material', handler: _lazy_67UG6U, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/upload/file', handler: _lazy_ecP11U, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/upload/news-image', handler: _lazy_y0ugTp, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/upload/platform-image', handler: _lazy_aD3wjp, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/upload/site-asset', handler: _lazy_G7XNf7, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/upload/site-logo', handler: _lazy_eL7wQQ, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/upload/site/favicon', handler: _lazy__wDeMz, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/upload/site/logo', handler: _lazy_tDTquO, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/user-management/:id', handler: _lazy_ZbntbL, lazy: true, middleware: false, method: "delete" },
  { route: '/api/admin/user-management/:id', handler: _lazy_hQTMl0, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/user-management/:id', handler: _lazy_KnPXnR, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/user-management/:id/reset-password', handler: _lazy_WyHnjR, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/user-management/:id/status', handler: _lazy_QH0zqi, lazy: true, middleware: false, method: "put" },
  { route: '/api/admin/user-management/batch-delete', handler: _lazy_4NFx1J, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/user-management/batch-status', handler: _lazy_zSmgj0, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/user-management/export', handler: _lazy_fb4aKT, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/user-management', handler: _lazy_6A3ucj, lazy: true, middleware: false, method: "get" },
  { route: '/api/admin/user-management', handler: _lazy_kqVMkO, lazy: true, middleware: false, method: "post" },
  { route: '/api/admin/user-management/stats', handler: _lazy_BkeLfh, lazy: true, middleware: false, method: "get" },
  { route: '/api/auth/admin/info', handler: _lazy_wf2Uhv, lazy: true, middleware: false, method: "get" },
  { route: '/api/auth/admin/login', handler: _lazy_tZekeS, lazy: true, middleware: false, method: "post" },
  { route: '/api/auth/admin/logout', handler: _lazy_2pSZ47, lazy: true, middleware: false, method: "post" },
  { route: '/api/auth/codes', handler: _lazy_PM2WU1, lazy: true, middleware: false, method: "get" },
  { route: '/api/auth/forgot-password', handler: _lazy_HnVYqM, lazy: true, middleware: false, method: "post" },
  { route: '/api/auth/login', handler: _lazy_Z164ph, lazy: true, middleware: false, method: "post" },
  { route: '/api/auth/logout', handler: _lazy_v5PJY4, lazy: true, middleware: false, method: "post" },
  { route: '/api/auth/refresh', handler: _lazy_zGBDse, lazy: true, middleware: false, method: "post" },
  { route: '/api/auth/register', handler: _lazy_odYuJI, lazy: true, middleware: false, method: "post" },
  { route: '/api/brands', handler: _lazy_ZFzhrG, lazy: true, middleware: false, method: "get" },
  { route: '/api/debug/project', handler: _lazy_MwWa67, lazy: true, middleware: false, method: "get" },
  { route: '/api/dramas/:id', handler: _lazy_VpwEH1, lazy: true, middleware: false, method: "get" },
  { route: '/api/dramas', handler: _lazy_N7oh23, lazy: true, middleware: false, method: "get" },
  { route: '/api/funds/:code', handler: _lazy_WroOAz, lazy: true, middleware: false, method: "get" },
  { route: '/api/funds', handler: _lazy_SckIYE, lazy: true, middleware: false, method: "get" },
  { route: '/api/health', handler: _lazy_2mnT4O, lazy: true, middleware: false, method: "get" },
  { route: '/api/platforms', handler: _lazy_RunIwg, lazy: true, middleware: false, method: "get" },
  { route: '/api/public/banners', handler: _lazy_MeqRBH, lazy: true, middleware: false, method: "get" },
  { route: '/api/public/config', handler: _lazy_aZ92f3, lazy: true, middleware: false, method: "get" },
  { route: '/api/public/news', handler: _lazy_SQojmg, lazy: true, middleware: false, method: "get" },
  { route: '/api/public/news/:id', handler: _lazy_JC3hB4, lazy: true, middleware: false, method: "get" },
  { route: '/api/public/news/:id/view', handler: _lazy_USd0Gg, lazy: true, middleware: false, method: "post" },
  { route: '/api/public/news/categories', handler: _lazy_RmYBaP, lazy: true, middleware: false, method: "get" },
  { route: '/api/public/posts/agreement/:identifier', handler: _lazy_BBLvNA, lazy: true, middleware: false, method: "get" },
  { route: '/api/public/settings/contact', handler: _lazy_C4cDnT, lazy: true, middleware: false, method: "get" },
  { route: '/api/public/settings', handler: _lazy_56MmOw, lazy: true, middleware: false, method: "get" },
  { route: '/api/public/settings/site', handler: _lazy_lTJhZ7, lazy: true, middleware: false, method: "get" },
  { route: '/api/public/system-info', handler: _lazy_1XO5RM, lazy: true, middleware: false, method: "get" },
  { route: '/api/tags', handler: _lazy_EW85fm, lazy: true, middleware: false, method: "get" },
  { route: '/api/users/change-password', handler: _lazy_IBqPUS, lazy: true, middleware: false, method: "post" },
  { route: '/api/users/dashboard', handler: _lazy_CMl8gp, lazy: true, middleware: false, method: "get" },
  { route: '/api/users/investments/create', handler: _lazy_HTeUHv, lazy: true, middleware: false, method: "post" },
  { route: '/api/users/investments/records', handler: _lazy_HH9khf, lazy: true, middleware: false, method: "get" },
  { route: '/api/users/profile', handler: _lazy_nJSFX2, lazy: true, middleware: false, method: "get" },
  { route: '/api/users/profile', handler: _lazy_uPp7jp, lazy: true, middleware: false, method: "put" },
  { route: '/api/users/wallet/balance', handler: _lazy_43s9Fd, lazy: true, middleware: false, method: "get" },
  { route: '/api/users/wallet/bank-cards', handler: _lazy_dHDUm0, lazy: true, middleware: false, method: "get" },
  { route: '/api/users/wallet/bank-cards', handler: _lazy_IKbd3M, lazy: true, middleware: false, method: "post" },
  { route: '/api/users/wallet/payment-methods', handler: _lazy_Mw6TED, lazy: true, middleware: false, method: "get" },
  { route: '/api/users/wallet/recharge', handler: _lazy_Plzzjv, lazy: true, middleware: false, method: "post" },
  { route: '/api/users/wallet/recharge/:orderId', handler: _lazy_NvfwRC, lazy: true, middleware: false, method: "get" },
  { route: '/api/users/wallet/recharge/records', handler: _lazy_AFVWCq, lazy: true, middleware: false, method: "get" },
  { route: '/api/users/wallet/recharge/summary', handler: _lazy_WNGoQ0, lazy: true, middleware: false, method: "get" },
  { route: '/api/users/wallet/withdraw', handler: _lazy_fJILX9, lazy: true, middleware: false, method: "post" },
  { route: '/api/website/recent-investments', handler: _lazy_cEDym1, lazy: true, middleware: false, method: "get" },
  { route: '/api/website/stats', handler: _lazy_sTm5xj, lazy: true, middleware: false, method: "get" },
  { route: '/**', handler: _lazy_vZG9JB, lazy: true, middleware: false, method: undefined }
];

function createNitroApp() {
  const config = useRuntimeConfig();
  const hooks = createHooks();
  const captureError = (error, context = {}) => {
    const promise = hooks.callHookParallel("error", error, context).catch((error_) => {
      console.error("Error while capturing another error", error_);
    });
    if (context.event && isEvent(context.event)) {
      const errors = context.event.context.nitro?.errors;
      if (errors) {
        errors.push({ error, context });
      }
      if (context.event.waitUntil) {
        context.event.waitUntil(promise);
      }
    }
  };
  const h3App = createApp({
    debug: destr(false),
    onError: (error, event) => {
      captureError(error, { event, tags: ["request"] });
      return errorHandler(error, event);
    },
    onRequest: async (event) => {
      event.context.nitro = event.context.nitro || { errors: [] };
      const fetchContext = event.node.req?.__unenv__;
      if (fetchContext?._platform) {
        event.context = {
          _platform: fetchContext?._platform,
          // #3335
          ...fetchContext._platform,
          ...event.context
        };
      }
      if (!event.context.waitUntil && fetchContext?.waitUntil) {
        event.context.waitUntil = fetchContext.waitUntil;
      }
      event.fetch = (req, init) => fetchWithEvent(event, req, init, { fetch: localFetch });
      event.$fetch = (req, init) => fetchWithEvent(event, req, init, {
        fetch: $fetch
      });
      event.waitUntil = (promise) => {
        if (!event.context.nitro._waitUntilPromises) {
          event.context.nitro._waitUntilPromises = [];
        }
        event.context.nitro._waitUntilPromises.push(promise);
        if (event.context.waitUntil) {
          event.context.waitUntil(promise);
        }
      };
      event.captureError = (error, context) => {
        captureError(error, { event, ...context });
      };
      await nitroApp.hooks.callHook("request", event).catch((error) => {
        captureError(error, { event, tags: ["request"] });
      });
    },
    onBeforeResponse: async (event, response) => {
      await nitroApp.hooks.callHook("beforeResponse", event, response).catch((error) => {
        captureError(error, { event, tags: ["request", "response"] });
      });
    },
    onAfterResponse: async (event, response) => {
      await nitroApp.hooks.callHook("afterResponse", event, response).catch((error) => {
        captureError(error, { event, tags: ["request", "response"] });
      });
    }
  });
  const router = createRouter({
    preemptive: true
  });
  const nodeHandler = toNodeListener(h3App);
  const localCall = (aRequest) => b(nodeHandler, aRequest);
  const localFetch = (input, init) => {
    if (!input.toString().startsWith("/")) {
      return globalThis.fetch(input, init);
    }
    return C(
      nodeHandler,
      input,
      init
    ).then((response) => normalizeFetchResponse(response));
  };
  const $fetch = createFetch({
    fetch: localFetch,
    Headers: Headers$1,
    defaults: { baseURL: config.app.baseURL }
  });
  globalThis.$fetch = $fetch;
  h3App.use(createRouteRulesHandler({ localFetch }));
  for (const h of handlers) {
    let handler = h.lazy ? lazyEventHandler(h.handler) : h.handler;
    if (h.middleware || !h.route) {
      const middlewareBase = (config.app.baseURL + (h.route || "/")).replace(
        /\/+/g,
        "/"
      );
      h3App.use(middlewareBase, handler);
    } else {
      const routeRules = getRouteRulesForPath(
        h.route.replace(/:\w+|\*\*/g, "_")
      );
      if (routeRules.cache) {
        handler = cachedEventHandler(handler, {
          group: "nitro/routes",
          ...routeRules.cache
        });
      }
      router.use(h.route, handler, h.method);
    }
  }
  h3App.use(config.app.baseURL, router.handler);
  const app = {
    hooks,
    h3App,
    router,
    localCall,
    localFetch,
    captureError
  };
  return app;
}
function runNitroPlugins(nitroApp2) {
  for (const plugin of plugins) {
    try {
      plugin(nitroApp2);
    } catch (error) {
      nitroApp2.captureError(error, { tags: ["plugin"] });
      throw error;
    }
  }
}
const nitroApp = createNitroApp();
function useNitroApp() {
  return nitroApp;
}
runNitroPlugins(nitroApp);

const debug = (...args) => {
};
function GracefulShutdown(server, opts) {
  opts = opts || {};
  const options = Object.assign(
    {
      signals: "SIGINT SIGTERM",
      timeout: 3e4,
      development: false,
      forceExit: true,
      onShutdown: (signal) => Promise.resolve(signal),
      preShutdown: (signal) => Promise.resolve(signal)
    },
    opts
  );
  let isShuttingDown = false;
  const connections = {};
  let connectionCounter = 0;
  const secureConnections = {};
  let secureConnectionCounter = 0;
  let failed = false;
  let finalRun = false;
  function onceFactory() {
    let called = false;
    return (emitter, events, callback) => {
      function call() {
        if (!called) {
          called = true;
          return Reflect.apply(callback, this, arguments);
        }
      }
      for (const e of events) {
        emitter.on(e, call);
      }
    };
  }
  const signals = options.signals.split(" ").map((s) => s.trim()).filter((s) => s.length > 0);
  const once = onceFactory();
  once(process, signals, (signal) => {
    debug("received shut down signal", signal);
    shutdown(signal).then(() => {
      if (options.forceExit) {
        process.exit(failed ? 1 : 0);
      }
    }).catch((error) => {
      debug("server shut down error occurred", error);
      process.exit(1);
    });
  });
  function isFunction(functionToCheck) {
    const getType = Object.prototype.toString.call(functionToCheck);
    return /^\[object\s([A-Za-z]+)?Function]$/.test(getType);
  }
  function destroy(socket, force = false) {
    if (socket._isIdle && isShuttingDown || force) {
      socket.destroy();
      if (socket.server instanceof http.Server) {
        delete connections[socket._connectionId];
      } else {
        delete secureConnections[socket._connectionId];
      }
    }
  }
  function destroyAllConnections(force = false) {
    debug("Destroy Connections : " + (force ? "forced close" : "close"));
    let counter = 0;
    let secureCounter = 0;
    for (const key of Object.keys(connections)) {
      const socket = connections[key];
      const serverResponse = socket._httpMessage;
      if (serverResponse && !force) {
        if (!serverResponse.headersSent) {
          serverResponse.setHeader("connection", "close");
        }
      } else {
        counter++;
        destroy(socket);
      }
    }
    debug("Connections destroyed : " + counter);
    debug("Connection Counter    : " + connectionCounter);
    for (const key of Object.keys(secureConnections)) {
      const socket = secureConnections[key];
      const serverResponse = socket._httpMessage;
      if (serverResponse && !force) {
        if (!serverResponse.headersSent) {
          serverResponse.setHeader("connection", "close");
        }
      } else {
        secureCounter++;
        destroy(socket);
      }
    }
    debug("Secure Connections destroyed : " + secureCounter);
    debug("Secure Connection Counter    : " + secureConnectionCounter);
  }
  server.on("request", (req, res) => {
    req.socket._isIdle = false;
    if (isShuttingDown && !res.headersSent) {
      res.setHeader("connection", "close");
    }
    res.on("finish", () => {
      req.socket._isIdle = true;
      destroy(req.socket);
    });
  });
  server.on("connection", (socket) => {
    if (isShuttingDown) {
      socket.destroy();
    } else {
      const id = connectionCounter++;
      socket._isIdle = true;
      socket._connectionId = id;
      connections[id] = socket;
      socket.once("close", () => {
        delete connections[socket._connectionId];
      });
    }
  });
  server.on("secureConnection", (socket) => {
    if (isShuttingDown) {
      socket.destroy();
    } else {
      const id = secureConnectionCounter++;
      socket._isIdle = true;
      socket._connectionId = id;
      secureConnections[id] = socket;
      socket.once("close", () => {
        delete secureConnections[socket._connectionId];
      });
    }
  });
  process.on("close", () => {
    debug("closed");
  });
  function shutdown(sig) {
    function cleanupHttp() {
      destroyAllConnections();
      debug("Close http server");
      return new Promise((resolve, reject) => {
        server.close((err) => {
          if (err) {
            return reject(err);
          }
          return resolve(true);
        });
      });
    }
    debug("shutdown signal - " + sig);
    if (options.development) {
      debug("DEV-Mode - immediate forceful shutdown");
      return process.exit(0);
    }
    function finalHandler() {
      if (!finalRun) {
        finalRun = true;
        if (options.finally && isFunction(options.finally)) {
          debug("executing finally()");
          options.finally();
        }
      }
      return Promise.resolve();
    }
    function waitForReadyToShutDown(totalNumInterval) {
      debug(`waitForReadyToShutDown... ${totalNumInterval}`);
      if (totalNumInterval === 0) {
        debug(
          `Could not close connections in time (${options.timeout}ms), will forcefully shut down`
        );
        return Promise.resolve(true);
      }
      const allConnectionsClosed = Object.keys(connections).length === 0 && Object.keys(secureConnections).length === 0;
      if (allConnectionsClosed) {
        debug("All connections closed. Continue to shutting down");
        return Promise.resolve(false);
      }
      debug("Schedule the next waitForReadyToShutdown");
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve(waitForReadyToShutDown(totalNumInterval - 1));
        }, 250);
      });
    }
    if (isShuttingDown) {
      return Promise.resolve();
    }
    debug("shutting down");
    return options.preShutdown(sig).then(() => {
      isShuttingDown = true;
      cleanupHttp();
    }).then(() => {
      const pollIterations = options.timeout ? Math.round(options.timeout / 250) : 0;
      return waitForReadyToShutDown(pollIterations);
    }).then((force) => {
      debug("Do onShutdown now");
      if (force) {
        destroyAllConnections(force);
      }
      return options.onShutdown(sig);
    }).then(finalHandler).catch((error) => {
      const errString = typeof error === "string" ? error : JSON.stringify(error);
      debug(errString);
      failed = true;
      throw errString;
    });
  }
  function shutdownManual() {
    return shutdown("manual");
  }
  return shutdownManual;
}

function getGracefulShutdownConfig() {
  return {
    disabled: !!process.env.NITRO_SHUTDOWN_DISABLED,
    signals: (process.env.NITRO_SHUTDOWN_SIGNALS || "SIGTERM SIGINT").split(" ").map((s) => s.trim()),
    timeout: Number.parseInt(process.env.NITRO_SHUTDOWN_TIMEOUT || "", 10) || 3e4,
    forceExit: !process.env.NITRO_SHUTDOWN_NO_FORCE_EXIT
  };
}
function setupGracefulShutdown(listener, nitroApp) {
  const shutdownConfig = getGracefulShutdownConfig();
  if (shutdownConfig.disabled) {
    return;
  }
  GracefulShutdown(listener, {
    signals: shutdownConfig.signals.join(" "),
    timeout: shutdownConfig.timeout,
    forceExit: shutdownConfig.forceExit,
    onShutdown: async () => {
      await new Promise((resolve) => {
        const timeout = setTimeout(() => {
          console.warn("Graceful shutdown timeout, force exiting...");
          resolve();
        }, shutdownConfig.timeout);
        nitroApp.hooks.callHook("close").catch((error) => {
          console.error(error);
        }).finally(() => {
          clearTimeout(timeout);
          resolve();
        });
      });
    }
  });
}

export { findAdminById as A, getAdminPermissions as B, findAdminByCredentials as C, verifyPassword as D, generateAdminAccessToken as E, generateAdminRefreshToken as F, updateAdminLastLogin as G, setCookie as H, deleteCookie as I, getCookie as J, validationErrorResponse as K, findUserByCredentials as L, useResponseSuccess as M, extractAuditInfo as N, serverErrorResponse as O, useResponseError as P, generateUserAccessToken as Q, generateUserRefreshToken as R, updateUserLastLogin as S, verifyRefreshToken as T, findUserById as U, generateAccessToken as V, generateRefreshToken as W, setResponseStatus as X, verifyToken as Y, verifyUserAccessToken as Z, trapUnhandledNodeErrors as a, useNitroApp as b, defineEventHandler as c, destr as d, getClientIP as e, createError$1 as f, getQuery as g, logAuditAction as h, getHeader as i, getRouterParam as j, getPool as k, logger as l, transaction as m, readMultipartFormData as n, logAdminAction as o, executeTransaction as p, query as q, readBody as r, setupGracefulShutdown as s, toNodeListener as t, useRuntimeConfig as u, verifyAdminAccessToken as v, queryInTransaction as w, verifyAdminToken as x, hashPassword as y, setHeader as z };
//# sourceMappingURL=nitro.mjs.map
