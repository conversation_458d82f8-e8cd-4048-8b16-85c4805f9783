import { q as query, l as logger } from './nitro.mjs';

async function checkAdminPermission(adminId, permissionCode) {
  try {
    const result = await query(
      `SELECT 1 FROM admin_role_relations arr
       JOIN admin_role_permissions arp ON arr.role_id = arp.role_id
       WHERE arr.admin_id = ? AND arp.permission_code = ?
       LIMIT 1`,
      [adminId, permissionCode]
    );
    return result.length > 0;
  } catch (error) {
    logger.error("\u68C0\u67E5\u7BA1\u7406\u5458\u6743\u9650\u5931\u8D25", {
      adminId,
      permissionCode,
      error: error.message
    });
    return false;
  }
}
async function getAdminPermissionCodes(adminId) {
  try {
    const result = await query(
      `SELECT DISTINCT arp.permission_code 
       FROM admin_role_relations arr
       JOIN admin_role_permissions arp ON arr.role_id = arp.role_id
       WHERE arr.admin_id = ?`,
      [adminId]
    );
    return result.map((row) => row.permission_code);
  } catch (error) {
    logger.error("\u83B7\u53D6\u7BA1\u7406\u5458\u6743\u9650\u5931\u8D25", { adminId, error: error.message });
    return [];
  }
}
const PERMISSIONS = {
  // 系统管理
  SYSTEM_MENU_LIST: "System:Menu:List",
  SYSTEM_MENU_CREATE: "System:Menu:Create",
  SYSTEM_MENU_EDIT: "System:Menu:Edit",
  SYSTEM_MENU_DELETE: "System:Menu:Delete",
  // 用户管理
  USER_LIST: "AC_100110",
  USER_CREATE: "AC_100110",
  USER_EDIT: "AC_100110",
  USER_DELETE: "AC_100110",
  // 角色管理
  ROLE_MANAGE: "AC_100120",
  // 内容管理
  CONTENT_MANAGE: "AC_100020",
  // 短剧管理
  DRAMA_LIST: "Drama:List",
  DRAMA_CREATE: "Drama:Create",
  DRAMA_EDIT: "Drama:Edit",
  DRAMA_DELETE: "Drama:Delete",
  DRAMA_VIEW: "Drama:View",
  DRAMA_MATERIAL_MANAGE: "Drama:Material:Manage",
  // 数据统计
  DATA_ANALYTICS: "AC_100030",
  // 基础权限
  BASIC_ACCESS: "AC_100010"
};

export { PERMISSIONS as P, checkAdminPermission as c, getAdminPermissionCodes as g };
//# sourceMappingURL=permission.mjs.map
