import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, g as getQuery, q as query, h as logAuditAction, i as getHeader, e as getClient<PERSON>, l as logger, f as createError } from '../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const index_get = defineEventHandler(async (event) => {
  var _a, _b;
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const query$1 = getQuery(event);
    const {
      page = 1,
      pageSize = 10,
      search = "",
      companyType = "",
      status = ""
    } = query$1;
    console.log("\u5382\u724C\u7BA1\u7406\u641C\u7D22\u53C2\u6570:", {
      page,
      pageSize,
      search,
      companyType,
      status,
      originalQuery: query$1
    });
    const pageNum = Math.max(1, parseInt(page) || 1);
    const pageSizeNum = Math.min(100, Math.max(1, parseInt(pageSize) || 10));
    const offset = (pageNum - 1) * pageSizeNum;
    let whereClause = "WHERE 1=1";
    const params = [];
    if (search) {
      whereClause += " AND (brand_name LIKE ? OR company_name LIKE ? OR credit_code LIKE ?)";
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern, searchPattern);
    }
    if (companyType) {
      whereClause += " AND company_type = ?";
      params.push(companyType);
    }
    if (status) {
      whereClause += " AND status = ?";
      params.push(status);
    }
    const countResult = await query(
      `SELECT COUNT(*) as total FROM brands ${whereClause}`,
      params
    );
    const total = ((_a = countResult[0]) == null ? void 0 : _a.total) || 0;
    const brands = await query(
      `SELECT 
        id,
        brand_name,
        brand_logo,
        credit_code,
        company_name,
        company_type,
        registered_capital,
        legal_representative,
        establishment_date,
        business_term,
        registered_address,
        business_scope,
        registration_authority,
        license_issue_date,
        business_license_photo,
        status,
        created_at,
        updated_at
      FROM brands 
      ${whereClause} 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?`,
      [...params, pageSizeNum, offset]
    );
    const formattedBrands = brands.map((brand) => ({
      id: brand.id,
      brandName: brand.brand_name,
      brandLogo: brand.brand_logo,
      creditCode: brand.credit_code,
      companyName: brand.company_name,
      companyType: brand.company_type,
      registeredCapital: brand.registered_capital,
      legalRepresentative: brand.legal_representative,
      establishmentDate: brand.establishment_date,
      businessTerm: brand.business_term,
      registeredAddress: brand.registered_address,
      businessScope: brand.business_scope,
      registrationAuthority: brand.registration_authority,
      licenseIssueDate: brand.license_issue_date,
      businessLicensePhoto: brand.business_license_photo,
      status: brand.status,
      createdAt: brand.created_at,
      updatedAt: brand.updated_at
    }));
    await logAuditAction({
      action: "ADMIN_VIEW_BRAND_LIST",
      description: `\u7BA1\u7406\u5458\u67E5\u770B\u5382\u724C\u5217\u8868`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        page: pageNum,
        pageSize: pageSizeNum,
        search,
        companyType,
        status,
        total
      }
    });
    logger.info("\u7BA1\u7406\u5458\u83B7\u53D6\u5382\u724C\u5217\u8868\u6210\u529F", {
      adminId: admin.id,
      adminUsername: admin.username,
      total,
      page: pageNum,
      pageSize: pageSizeNum
    });
    return {
      success: true,
      data: {
        list: formattedBrands,
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total,
          totalPages: Math.ceil(total / pageSizeNum)
        }
      }
    };
  } catch (error) {
    logger.error("\u7BA1\u7406\u5458\u83B7\u53D6\u5382\u724C\u5217\u8868\u5931\u8D25", {
      error: error.message,
      adminId: (_b = event.context.admin) == null ? void 0 : _b.id,
      ip: getClientIP(event)
    });
    throw createError({
      statusCode: 500,
      statusMessage: "\u83B7\u53D6\u5382\u724C\u5217\u8868\u5931\u8D25"
    });
  }
});

export { index_get as default };
//# sourceMappingURL=index.get4.mjs.map
