import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, r as readBody, K as validationErrorResponse, L as findUserByCredentials, h as logAuditAction, N as extractAuditInfo, P as useResponseError, D as verifyPassword, Q as generateUserAccessToken, R as generateUserRefreshToken, S as updateUserLastLogin, l as logger, M as useResponseSuccess, O as serverErrorResponse } from '../../../_/nitro.mjs';
import { s as setRefreshTokenCookie } from '../../../_/cookie-utils.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const login_post = defineEventHandler(async (event) => {
  try {
    console.log("=== \u540E\u7AEF\u767B\u5F55\u8C03\u8BD5\u65E5\u5FD7 ===");
    console.log("1. \u6536\u5230\u767B\u5F55\u8BF7\u6C42");
    const body = await readBody(event);
    const { email, phone, password } = body;
    console.log("2. \u89E3\u6790\u8BF7\u6C42\u6570\u636E");
    console.log("\u90AE\u7BB1:", email);
    console.log("\u624B\u673A\u53F7:", phone);
    console.log("\u5BC6\u7801\u957F\u5EA6:", password ? password.length : 0);
    if (!email && !phone || !password) {
      console.log("3. \u9A8C\u8BC1\u5931\u8D25 - \u7F3A\u5C11\u5FC5\u8981\u53C2\u6570");
      return validationErrorResponse(
        event,
        "\u8BF7\u63D0\u4F9B\u90AE\u7BB1\u6216\u624B\u673A\u53F7\uFF0C\u4EE5\u53CA\u5BC6\u7801",
        {
          email: !email && !phone ? "\u8BF7\u63D0\u4F9B\u90AE\u7BB1\u6216\u624B\u673A\u53F7" : null,
          phone: !email && !phone ? "\u8BF7\u63D0\u4F9B\u90AE\u7BB1\u6216\u624B\u673A\u53F7" : null,
          password: !password ? "\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A" : null
        }
      );
    }
    console.log("3. \u8F93\u5165\u9A8C\u8BC1\u901A\u8FC7");
    console.log("4. \u67E5\u627E\u7528\u6237");
    const identifier = email || phone;
    const user = await findUserByCredentials(identifier);
    console.log("\u7528\u6237\u67E5\u627E\u7ED3\u679C:", user ? "\u627E\u5230\u7528\u6237" : "\u7528\u6237\u4E0D\u5B58\u5728");
    if (!user) {
      console.log("5. \u767B\u5F55\u5931\u8D25 - \u7528\u6237\u4E0D\u5B58\u5728");
      await logAuditAction({
        action: "USER_LOGIN_FAILED",
        description: `C\u7AEF\u7528\u6237\u4E0D\u5B58\u5728: ${identifier}`,
        ...extractAuditInfo(event)
      });
      return useResponseError("\u90AE\u7BB1/\u624B\u673A\u53F7\u6216\u5BC6\u7801\u9519\u8BEF");
    }
    console.log("5. \u9A8C\u8BC1\u5BC6\u7801");
    console.log("\u7528\u6237\u5BC6\u7801\u5B57\u6BB5\u60C5\u51B5:", {
      hasPasswordHash: !!user.password_hash,
      passwordHashLength: user.password_hash ? user.password_hash.length : 0
    });
    const storedPasswordHash = user.password_hash || "";
    console.log("\u4F7F\u7528\u7684\u5BC6\u7801\u5B57\u6BB5: password_hash");
    console.log("\u5B58\u50A8\u7684\u5BC6\u7801\u54C8\u5E0C:", storedPasswordHash);
    if (!storedPasswordHash) {
      console.log("6. \u767B\u5F55\u5931\u8D25 - \u7528\u6237\u6CA1\u6709\u8BBE\u7F6E\u5BC6\u7801");
      return useResponseError("\u7528\u6237\u5BC6\u7801\u672A\u8BBE\u7F6E\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458");
    }
    const isValidPassword = await verifyPassword(password, storedPasswordHash);
    console.log("\u5BC6\u7801\u9A8C\u8BC1\u7ED3\u679C:", isValidPassword ? "\u5BC6\u7801\u6B63\u786E" : "\u5BC6\u7801\u9519\u8BEF");
    if (!isValidPassword) {
      console.log("6. \u767B\u5F55\u5931\u8D25 - \u5BC6\u7801\u9519\u8BEF");
      await logAuditAction({
        userId: user.id,
        username: user.username,
        userType: "user",
        action: "USER_LOGIN_WRONG_PASSWORD",
        description: "C\u7AEF\u7528\u6237\u5BC6\u7801\u9519\u8BEF\u5C1D\u8BD5",
        ...extractAuditInfo(event)
      });
      return useResponseError("\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF");
    }
    console.log("6. \u5BC6\u7801\u9A8C\u8BC1\u901A\u8FC7\uFF0C\u751F\u6210\u4EE4\u724C");
    const accessToken = await generateUserAccessToken(user);
    const refreshToken = await generateUserRefreshToken(user);
    console.log("7. \u4EE4\u724C\u751F\u6210\u6210\u529F");
    setRefreshTokenCookie(event, refreshToken);
    await updateUserLastLogin(user.id);
    console.log("8. \u66F4\u65B0\u6700\u540E\u767B\u5F55\u65F6\u95F4");
    await logAuditAction({
      userId: user.id,
      username: user.username,
      userType: "user",
      action: "USER_LOGIN_SUCCESS",
      description: "C\u7AEF\u7528\u6237\u767B\u5F55\u6210\u529F",
      ...extractAuditInfo(event)
    });
    logger.info("C\u7AEF\u7528\u6237\u767B\u5F55\u6210\u529F", {
      userId: user.id,
      username: user.username
    });
    console.log("9. \u51C6\u5907\u8FD4\u56DE\u7528\u6237\u4FE1\u606F");
    const { password: _, ...userWithoutPassword } = user;
    const responseData = {
      token: accessToken,
      user: {
        ...userWithoutPassword,
        accessToken,
        realName: user.username,
        roles: ["user"],
        homePath: "/workspace"
      }
    };
    console.log("10. \u767B\u5F55\u6210\u529F\uFF0C\u8FD4\u56DE\u6570\u636E");
    console.log("\u8FD4\u56DE\u7684\u7528\u6237\u4FE1\u606F:", responseData.user);
    return useResponseSuccess(responseData, "\u767B\u5F55\u6210\u529F");
  } catch (error) {
    console.log("=== \u540E\u7AEF\u767B\u5F55\u9519\u8BEF ===");
    console.error("\u767B\u5F55\u8FC7\u7A0B\u4E2D\u53D1\u751F\u9519\u8BEF:", error);
    logger.error("\u767B\u5F55\u8FC7\u7A0B\u4E2D\u53D1\u751F\u9519\u8BEF", { error: error.message });
    await logAuditAction({
      action: "USER_LOGIN_ERROR",
      description: `C\u7AEF\u7528\u6237\u767B\u5F55\u7CFB\u7EDF\u9519\u8BEF: ${error.message}`,
      ...extractAuditInfo(event)
    });
    return serverErrorResponse(event, "\u767B\u5F55\u5931\u8D25\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5");
  }
});

export { login_post as default };
//# sourceMappingURL=login.post.mjs.map
