<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
// 移除Element Plus依赖，使用原生实现
import { getPublicDrama, getPublicTags, getPublicPlatforms } from '../api/dramaService';
import { getPublicActors } from '../api/actors';
import InvestmentConfirmDialog from '../components/investment/InvestmentConfirmDialog.vue';
import DramaDocuments from '../components/drama/DramaDocuments.vue';
import { requireLogin } from '../utils/authUtils';

import {
  AgreementType,
  shouldDefaultCheck,
  setAgreementAgreed,
  markAgreementCompleted
} from '../utils/agreementStorage';

// 定义props来接收路由参数
const props = defineProps({
  id: {
    type: String,
    required: false
  }
});

const route = useRoute();
const router = useRouter();
// 优先使用props中的id，如果没有则使用路由参数
const projectId = props.id || route.params.id;

// 当前查看的媒体索引
const currentMediaIndex = ref(0);
const autoPlayTimer = ref(null);
const isAutoPlaying = ref(true);

const showNextMedia = () => {
  if (currentMediaIndex.value < (projectDetail.value?.materials?.length || 1) - 1) {
    currentMediaIndex.value++;
  } else {
    currentMediaIndex.value = 0;
  }
};

const showPrevMedia = () => {
  if (currentMediaIndex.value > 0) {
    currentMediaIndex.value--;
  } else {
    currentMediaIndex.value = (projectDetail.value?.materials?.length || 1) - 1;
  }
};

// 开始自动播放
const startAutoPlay = () => {
  if (autoPlayTimer.value) {
    clearInterval(autoPlayTimer.value);
  }

  // 只有当有多个素材时才自动播放
  if (projectDetail.value?.materials?.length > 1) {
    autoPlayTimer.value = setInterval(() => {
      if (isAutoPlaying.value) {
        showNextMedia();
      }
    }, 4000); // 每4秒切换一次
  }
};

// 停止自动播放
const stopAutoPlay = () => {
  if (autoPlayTimer.value) {
    clearInterval(autoPlayTimer.value);
    autoPlayTimer.value = null;
  }
};

// 暂停/恢复自动播放
const toggleAutoPlay = () => {
  isAutoPlaying.value = !isAutoPlaying.value;
  if (isAutoPlaying.value) {
    startAutoPlay();
  } else {
    stopAutoPlay();
  }
};

// 手动切换时暂停自动播放一段时间
const handleManualSwitch = (direction) => {
  stopAutoPlay();
  isAutoPlaying.value = false;

  if (direction === 'next') {
    showNextMedia();
  } else {
    showPrevMedia();
  }

  // 5秒后恢复自动播放
  setTimeout(() => {
    isAutoPlaying.value = true;
    startAutoPlay();
  }, 5000);
};

// 判断当前显示的媒体是否为视频
const isVideoMedia = computed(() => {
  if (!projectDetail.value?.materials?.[currentMediaIndex.value]) return false;
  const url = projectDetail.value.materials[currentMediaIndex.value].url;
  return url.endsWith('.mp4') || url.endsWith('.webm') || url.includes('youtube.com') || url.includes('youku.com');
});

// 投资金额选择 - 默认选中3万元
const selectedAmount = ref(30000);
const customAmount = ref('');
const setAmount = (amount) => {
  selectedAmount.value = amount;
  customAmount.value = '';
};

// 设置自定义金额
const setCustomAmount = () => {
  const amount = parseInt(customAmount.value);
  const minAmount = projectDetail.value?.minInvestment || 50000;
  if (!isNaN(amount) && amount >= minAmount) {
    selectedAmount.value = amount;
  }
};

// 投资确认弹窗状态
const showInvestmentDialog = ref(false);

// 众筹协议相关
const agreeCrowdfundingTerms = ref(false);
const showAgreementError = ref(false);
const agreementContainer = ref(null);

// 打开投资确认弹窗
const openInvestmentDialog = () => {
  // 检查登录状态
  if (!requireLogin(router)) {
    return;
  }

  if (!selectedAmount.value) {
    alert('请先选择投资金额');
    return;
  }

  // 验证是否同意众筹协议
  if (!agreeCrowdfundingTerms.value) {
    showAgreementError.value = true;
    return;
  }

  showInvestmentDialog.value = true;
};

// 关闭投资确认弹窗
const closeInvestmentDialog = () => {
  showInvestmentDialog.value = false;
};

// 投资成功回调
const onInvestmentSuccess = (data) => {
  // 刷新项目数据
  fetchProjectDetail();

  // 标记众筹协议已完成
  markAgreementCompleted(AgreementType.CROWDFUNDING);

  // 重置选择的金额
  selectedAmount.value = null;
  customAmount.value = '';
};

// 打开协议页面
const openAgreement = (type) => {
  // 在新标签页中打开协议页面
  const routeUrl = router.resolve({ name: 'agreement', params: { type } });
  window.open(routeUrl.href, '_blank');
};

// 清除协议错误提示
const clearAgreementError = () => {
  showAgreementError.value = false;
};

// 初始化协议状态
const initAgreementStatus = async () => {
  try {
    const shouldCheck = await shouldDefaultCheck(AgreementType.CROWDFUNDING);
    agreeCrowdfundingTerms.value = shouldCheck;
  } catch (error) {
    console.error('初始化协议状态失败:', error);
    agreeCrowdfundingTerms.value = false;
  }
};

// 监听协议勾选状态变化
const handleAgreementChange = () => {
  setAgreementAgreed(AgreementType.CROWDFUNDING, agreeCrowdfundingTerms.value);
  clearAgreementError();
};

// 加载项目详情数据
const loading = ref(true);
const error = ref(null);
const projectDetail = ref(null);

// 营业执照预览相关
const showBusinessLicense = ref(false);
const businessLicenseUrl = ref('');

// 选择器数据
const allTags = ref([]);
const allActors = ref([]);
const allPlatforms = ref([]);

// 获取项目详情数据
const fetchProjectDetail = async () => {
  loading.value = true;
  error.value = null;

  try {
    const response = await getPublicDrama(projectId);

    if (response.data && response.data.success) {
      const dramaData = response.data.data;

      // 将API返回的数据格式化为组件所需的格式

      projectDetail.value = {
        id: dramaData.id,
        title: dramaData.title || '未命名短剧',
        expectedReturn: parseFloat(dramaData.expectedReturn) || 15.0,
        minInvestment: parseFloat(dramaData.minInvestment) || 50000,
        maxInvestment: dramaData.maxInvestment ? parseFloat(dramaData.maxInvestment) : null,
        fundingShare: parseFloat(dramaData.fundingShare) || 100,
        cover: dramaData.cover || null,
        coverColor: dramaData.coverColor || "#8667F0",
        tags: Array.isArray(dramaData.tags) ? dramaData.tags : (dramaData.tags ? JSON.parse(dramaData.tags) : []),
        fundingGoal: Number(dramaData.fundingGoal) || 0,
        currentFunding: Number(dramaData.currentFunding) || 0,
        remainingDays: Number(dramaData.remainingDays) || 0,
        description: dramaData.description || "",
        longDescription: dramaData.longDescription || dramaData.description || "",
        director: dramaData.director || "未指定",
        scriptwriter: dramaData.scriptwriter || "未指定",
        cast: Array.isArray(dramaData.cast) ? dramaData.cast : (dramaData.cast ? JSON.parse(dramaData.cast) : []),
        mainCast: Array.isArray(dramaData.cast) ? dramaData.cast : (dramaData.cast ? JSON.parse(dramaData.cast) : []),
        episodes: Number(dramaData.episodes) || 0,
        episodeLength: Number(dramaData.episodeLength) || 0,
        duration: `单集${Number(dramaData.episodeLength) || 0}分钟`,
        targetPlatform: Array.isArray(dramaData.targetPlatform) ? dramaData.targetPlatform : (dramaData.targetPlatform ? JSON.parse(dramaData.targetPlatform) : []),
        projectedViews: dramaData.projectedViews || "4000万+",
        materials: Array.isArray(dramaData.materials) && dramaData.materials.length > 0
          ? dramaData.materials
          : [{url: "https://via.placeholder.com/600x800?text=暂无素材", title: "暂无素材", type: "image"}],
        // 文档信息
        documents: Array.isArray(dramaData.documents) ? dramaData.documents : [],
        // 制作团队信息 - 从嵌套的productionTeam对象获取
        productionTeam: dramaData.productionTeam ? {
          // 公司信息
          productionCompany: dramaData.productionTeam.productionCompany || null,
          coProductionCompany: dramaData.productionTeam.coProductionCompany || null,

          // 人员信息
          executiveProducer: dramaData.productionTeam.executiveProducer || null,
          coExecutiveProducer: dramaData.productionTeam.coExecutiveProducer || null,
          chiefProducer: dramaData.productionTeam.chiefProducer || null,
          producer: dramaData.productionTeam.producer || null,
          coProducer: dramaData.productionTeam.coProducer || null,
          director: dramaData.productionTeam.director || null,
          scriptwriter: dramaData.productionTeam.scriptwriter || null,
          supervisor: dramaData.productionTeam.supervisor || null,
          coordinator: dramaData.productionTeam.coordinator || null
        } : {
          // 如果没有productionTeam数据，提供默认的空对象
          productionCompany: null,
          coProductionCompany: null,
          executiveProducer: null,
          coExecutiveProducer: null,
          chiefProducer: null,
          producer: null,
          coProducer: null,
          director: null,
          scriptwriter: null,
          supervisor: null,
          coordinator: null
        },

        // 投资人信息
        investors: dramaData.investors || { totalCount: 0, list: [] },

        // 投资权益档位信息
        investmentTiers: dramaData.investmentTiers || [],

        // 营业执照信息
        businessLicense: dramaData.businessLicense || null,

        // 制作排期信息
        schedule: {
          preProduction: dramaData.schedulePreProduction || "未指定",
          filming: dramaData.scheduleFilming || "未指定",
          postProduction: dramaData.schedulePostProduction || "未指定",
          release: dramaData.scheduleRelease || "未指定"
        },
        financials: {
          totalBudget: Number(dramaData.fundingGoal) || 0,
          projectedViews: dramaData.projectedViews || "未指定",
          roi: dramaData.roi || "未指定"
        },
        confirmedResources: Array.isArray(dramaData.confirmedResources) ? dramaData.confirmedResources : [],
        investmentTiers: Array.isArray(dramaData.investmentTiers) && dramaData.investmentTiers.length > 0
          ? dramaData.investmentTiers
          : [
              {
                minAmount: 50000,
                benefits: ["项目进度月报", "优先获得投资收益"]
              },
              {
                minAmount: 100000,
                benefits: ["片尾鸣谢", "定期财务报告", "项目进度月报", "优先获得投资收益"]
              },
              {
                minAmount: 500000,
                benefits: ["联合出品方冠名", "线下观影活动邀请", "片尾鸣谢", "定期财务报告", "项目进度月报", "优先获得投资收益"]
              },
              {
                minAmount: 1000000,
                benefits: ["定制衍生品开发决策权", "联合出品方冠名", "线下观影活动邀请", "片尾鸣谢", "定期财务报告", "项目进度月报", "优先获得投资收益"]
              }
            ],
        riskManagement: Array.isArray(dramaData.riskManagement) && dramaData.riskManagement.length > 0
          ? dramaData.riskManagement
          : [
              "资金托管：招商银行专项账户",
              "退出机制：开拍前可转让份额，开拍后按合同分期退出",
              "安全保障：项目投保《影视制作责任险》",
              "透明管理：资金使用每周公示，制作进度实时更新"
            ]
      };

      loading.value = false;
    } else {
      error.value = "获取项目详情失败";
      loading.value = false;
    }
  } catch (e) {
    console.error("加载项目详情失败:", e);
    error.value = "加载项目详情失败，请稍后重试";
    loading.value = false;
  }
};

// 加载选择器数据
const loadSelectorData = async () => {
  try {
    // 并行加载标签、演员和平台数据
    const [tagsRes, actorsRes, platformsRes] = await Promise.all([
      getPublicTags(),
      getPublicActors(),
      getPublicPlatforms()
    ]);

    if (tagsRes.data && tagsRes.data.success) {
      allTags.value = tagsRes.data.data || [];
    }

    if (actorsRes.data && actorsRes.data.success) {
      allActors.value = actorsRes.data.data.list || [];
    }

    if (platformsRes.data && platformsRes.data.success) {
      allPlatforms.value = platformsRes.data.data || [];
    }

    console.log('选择器数据加载完成:', {
      tags: allTags.value.length,
      actors: allActors.value.length,
      platforms: allPlatforms.value.length
    });
  } catch (error) {
    console.error('加载选择器数据失败:', error);
  }
};

// 页面加载时获取项目详情和选择器数据
onMounted(async () => {
  await loadSelectorData();
  await fetchProjectDetail();
  // 初始化协议状态
  await initAgreementStatus();
  // 数据加载完成后启动自动播放
  setTimeout(() => {
    startAutoPlay();
  }, 1000);
});

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoPlay();
});

// 统一的金额格式化函数
const formatCurrency = (value) => {
  if (typeof value !== 'number' || isNaN(value)) return '0';

  if (value >= 10000000) {
    return (value / 10000000).toFixed(2) + '千万';
  } else if (value >= 10000) {
    return (value / 10000).toFixed(2) + '万';
  } else {
    return value.toLocaleString();
  }
};

// 计算项目募资进度百分比
const fundingProgress = computed(() => {
  if (!projectDetail.value) return 0;
  return Math.min(Math.round((projectDetail.value.currentFunding / projectDetail.value.fundingGoal) * 100), 100);
});

// 计算剩余募资份额百分比 - 基于募资份额而不是金额
const remainingFundingPercentage = computed(() => {
  if (!projectDetail.value) return 100;

  // 获取募资份额和当前进度
  const fundingShare = projectDetail.value.fundingShare || 100; // 募资份额百分比
  const currentProgress = fundingProgress.value; // 当前募资进度百分比

  // 计算剩余份额 = 募资份额 * (1 - 当前进度/100)
  const remainingShare = fundingShare * (1 - currentProgress / 100);
  return Math.max(Math.round(remainingShare), 0);
});

// 获取去重的投资人列表
const uniqueInvestors = computed(() => {
  if (!projectDetail.value?.investors?.list) return [];

  // 按用户ID去重，保留最新的投资记录
  const investorMap = new Map();
  projectDetail.value.investors.list.forEach(investor => {
    if (!investorMap.has(investor.id)) {
      investorMap.set(investor.id, investor);
    }
  });

  return Array.from(investorMap.values());
});

// 根据进度确定颜色
const progressColor = computed(() => {
  const progress = fundingProgress.value;
  if (progress >= 80) return 'bg-red-500';
  if (progress >= 50) return 'bg-orange-500';
  return 'bg-primary';
});

// 删除重复的formatCurrency函数，使用上面已定义的版本

// 解析标签数据（根据ID获取标签信息）
const parseTagData = (data) => {
  if (!data) return [];

  // 如果是数组格式（ID数组），转换为标签对象数组
  if (Array.isArray(data)) {
    return data.map(id => {
      const tag = allTags.value.find(t => t.id === id);
      return tag || { id, name: `未知标签(ID:${id})`, fontColor: '#666666', backgroundColor: '#f3f4f6' };
    });
  }

  if (typeof data === 'string') {
    try {
      // 尝试解析JSON格式的ID数组
      const parsed = JSON.parse(data);
      if (Array.isArray(parsed) && parsed.every(item => typeof item === 'number')) {
        return parsed.map(id => {
          const tag = allTags.value.find(t => t.id === id);
          return tag || { id, name: `未知标签(ID:${id})`, fontColor: '#666666', backgroundColor: '#f3f4f6' };
        });
      }
    } catch {
      // JSON解析失败，按逗号分隔的字符串处理（向后兼容）
      return data.split(',').map(item => ({ name: item.trim(), fontColor: '#666666', backgroundColor: '#f3f4f6' })).filter(tag => tag.name);
    }
  }

  return [];
};

// 解析演员数据（支持多种格式）
const parseActorData = (data) => {
  if (!data) return [];

  // 如果是数组格式（ID数组），转换为演员对象数组
  if (Array.isArray(data)) {
    return data.map(id => {
      const actor = allActors.value.find(a => a.id === id);
      return actor || { id, name: `未知演员(ID:${id})`, avatarUrl: null };
    });
  }

  if (typeof data === 'string') {
    try {
      // 尝试解析JSON格式
      const parsed = JSON.parse(data);
      if (Array.isArray(parsed)) {
        return parsed.map(item => {
          // 如果是数字，说明是ID数组格式
          if (typeof item === 'number') {
            const actor = allActors.value.find(a => a.id === item);
            return actor || { id: item, name: `未知演员(ID:${item})`, avatarUrl: null };
          }
          // 如果是对象，说明已经包含了演员信息
          if (typeof item === 'object' && item !== null) {
            return {
              id: item.id,
              name: item.name || `未知演员(ID:${item.id})`,
              avatarUrl: item.avatarUrl || item.avatar_url || null
            };
          }
          return null;
        }).filter(Boolean);
      }
    } catch {
      // JSON解析失败，按逗号分隔的字符串处理（向后兼容）
      return data.split(',').map(item => ({ name: item.trim(), avatarUrl: null })).filter(actor => actor.name);
    }
  }

  return [];
};

// 解析平台数据（根据ID获取平台信息）
const parsePlatformData = (data) => {
  if (!data) return [];

  // 如果是数组格式（ID数组），转换为平台对象数组
  if (Array.isArray(data)) {
    return data.map(id => {
      const platform = allPlatforms.value.find(p => p.id === id);
      return platform || { id, platform_name: `未知平台(ID:${id})`, platform_logo_url: null };
    });
  }

  if (typeof data === 'string') {
    try {
      // 尝试解析JSON格式的ID数组
      const parsed = JSON.parse(data);
      if (Array.isArray(parsed) && parsed.every(item => typeof item === 'number')) {
        return parsed.map(id => {
          const platform = allPlatforms.value.find(p => p.id === id);
          return platform || { id, platform_name: `未知平台(ID:${id})`, platform_logo_url: null };
        });
      }
    } catch {
      // JSON解析失败，按逗号分隔的字符串处理（向后兼容）
      return data.split(',').map(item => ({ platform_name: item.trim(), platform_logo_url: null })).filter(platform => platform.platform_name);
    }
  }

  return [];
};

// 获取平台列表（根据目标平台或默认平台）
const getPlatformList = () => {
  const platforms = parsePlatformData(projectDetail.value?.targetPlatform);
  if (platforms.length > 0) {
    // 如果有目标平台数据，使用目标平台，最多显示8个
    return platforms.slice(0, 8);
  } else {
    // 默认显示常见的短剧平台
    return Array(8).fill({ platform_name: '红果短剧', platform_logo_url: null });
  }
};

// 格式化热度数值（以万为单位，保留2位小数）
const formatHotness = (value) => {
  if (!value) return '0.00万';

  // 如果已经是字符串格式（如"4000万+"），直接返回
  if (typeof value === 'string' && value.includes('万')) {
    return value;
  }

  // 如果是数字，转换为万为单位
  const numValue = parseFloat(value);
  if (isNaN(numValue)) return '0.00万';

  const wanValue = numValue / 10000;
  return `${wanValue.toFixed(2)}万`;
};

// 解析公司数据（支持多种格式）
const parseCompanyData = (data) => {
  if (!data) return [];

  // 如果是数组格式，直接返回
  if (Array.isArray(data)) {
    return data.filter(item => item && item.trim());
  }

  // 如果是字符串格式
  if (typeof data === 'string') {
    try {
      // 尝试解析JSON格式
      const parsed = JSON.parse(data);
      if (Array.isArray(parsed)) {
        return parsed.filter(item => item && item.trim());
      }
    } catch {
      // JSON解析失败，按逗号分隔处理
      return data.split(',').map(item => item.trim()).filter(item => item);
    }
  }

  return [];
};

// 格式化日期范围的辅助函数
const formatDateRange = (data) => {
  if (!data) return '待定';

  // 如果是字符串格式
  if (typeof data === 'string') {
    try {
      // 尝试解析JSON格式
      const parsed = JSON.parse(data);
      if (Array.isArray(parsed) && parsed.length >= 2) {
        const startDate = parsed[0];
        const endDate = parsed[1];
        return `${startDate} 至 ${endDate}`;
      }
    } catch {
      // JSON解析失败，直接返回原字符串
      return data;
    }
  }

  // 如果是数组格式
  if (Array.isArray(data) && data.length >= 2) {
    const startDate = data[0];
    const endDate = data[1];
    return `${startDate} 至 ${endDate}`;
  }

  return data || '待定';
};

// 获取头像背景色
const getAvatarBgColor = (index) => {
  const colors = [
    'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-purple-500',
    'bg-pink-500', 'bg-indigo-500', 'bg-red-500', 'bg-teal-500'
  ];
  return colors[index % colors.length];
};

// 处理投资权益文本，按换行符分割成数组
const getBenefitsList = (benefitsText) => {
  if (!benefitsText) return [];

  // 按换行符分割，过滤空行，去除前后空格
  return benefitsText
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0)
    .map(line => {
      // 如果行首没有 • 符号，则保持原样；如果有，则去除它
      return line.startsWith('•') ? line.substring(1).trim() : line;
    });
};

// 查看营业执照
const viewBusinessLicense = () => {
  if (!projectDetail.value?.businessLicense?.businessLicensePhoto) {
    alert('暂无营业执照信息');
    return;
  }

  // 设置图片URL并显示预览
  const imageUrl = projectDetail.value.businessLicense.businessLicensePhoto;
  const companyName = projectDetail.value.businessLicense.companyName || '出品公司';

  businessLicenseUrl.value = imageUrl;
  showBusinessLicense.value = true;

  console.log(`正在查看${companyName}的营业执照:`, imageUrl);
};

// 获取姓名首字母
const getInitials = (name) => {
  if (!name) return '?';
  return name.charAt(0).toUpperCase();
};

// 判断是否有团队成员
const hasTeamMembers = computed(() => {
  if (!projectDetail.value?.productionTeam) return false;

  const team = projectDetail.value.productionTeam;
  const roles = ['director', 'scriptwriter', 'producer', 'chiefProducer', 'executiveProducer', 'coExecutiveProducer', 'coProducer', 'supervisor', 'coordinator'];

  return roles.some(role => {
    const members = parseActorData(team[role]);
    return members && members.length > 0;
  });
});



// 判断是否有公司信息
const hasCompanyInfo = computed(() => {
  if (!projectDetail.value?.productionTeam) return false;

  const team = projectDetail.value.productionTeam;
  const productionCompanies = parseCompanyData(team.productionCompany);
  const coProductionCompanies = parseCompanyData(team.coProductionCompany);

  return productionCompanies.length > 0 || coProductionCompanies.length > 0;
});
</script>

<template>
  <div class="min-h-screen bg-gray-50 py-4">
    <div class="container mx-auto px-4">
      <!-- 加载状态 -->
      <div v-if="loading" class="flex items-center justify-center h-64">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>

      <!-- 错误提示 -->
      <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong class="font-bold">加载失败！</strong>
        <span class="block sm:inline">{{ error }}</span>
      </div>

      <!-- 项目详情 -->
      <div v-else>
        
        <!-- 主要内容区 - 双栏布局 -->
        <div class="flex flex-col lg:flex-row gap-8">
          <!-- 左栏：多媒体和基本信息 -->
          <div class="w-full lg:w-7/10">
            <!-- 项目素材预览区 -->
            <div class="mb-8">
              <!-- 大图/视频预览 -->
              <div class="bg-white rounded-xl shadow-md overflow-hidden mb-4 relative">
                <!-- 当素材是视频时 -->
                <div v-if="isVideoMedia" class="w-full aspect-video bg-black">
                  <video
                    v-if="!projectDetail.materials[currentMediaIndex].url.includes('youtube.com') && !projectDetail.materials[currentMediaIndex].url.includes('youku.com')"
                    :src="projectDetail.materials[currentMediaIndex].url"
                    class="w-full h-full object-cover"
                    controls
                  ></video>
                  <iframe
                    v-else
                    :src="projectDetail.materials[currentMediaIndex].url"
                    class="w-full h-full"
                    frameborder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowfullscreen
                  ></iframe>
                </div>

                <!-- 当素材是图片时 -->
                <img
                  v-else
                  :src="projectDetail.materials[currentMediaIndex].url"
                  :alt="projectDetail.materials[currentMediaIndex].title"
                  class="w-full h-[400px] object-cover"
                />

                <!-- 上一张/下一张按钮 -->
                <button
                  @click="handleManualSwitch('prev')"
                  class="absolute top-1/2 left-4 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                >
                  <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <button
                  @click="handleManualSwitch('next')"
                  class="absolute top-1/2 right-4 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                >
                  <svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </button>

                <!-- 自动播放控制按钮 -->
                <button
                  @click="toggleAutoPlay"
                  class="absolute top-4 right-4 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                  :title="isAutoPlaying ? '暂停自动播放' : '开始自动播放'"
                >
                  <svg v-if="isAutoPlaying" class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6" />
                  </svg>
                  <svg v-else class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z" />
                  </svg>
                </button>

                <!-- 进度指示器 -->
                <div v-if="projectDetail.materials && projectDetail.materials.length > 1" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  <div
                    v-for="(_, index) in projectDetail.materials"
                    :key="index"
                    class="w-2 h-2 rounded-full transition-all duration-300"
                    :class="index === currentMediaIndex ? 'bg-white' : 'bg-white/50'"
                  ></div>
                </div>
              </div>

              <!-- 缩略图栏 - 支持滚动 -->
              <div class="overflow-x-auto">
                <div class="flex gap-2 min-w-max">
                  <div
                    v-for="(material, index) in projectDetail.materials"
                    :key="index"
                    @click="currentMediaIndex = index"
                    class="flex-shrink-0 cursor-pointer"
                  >
                    <!-- 缩略图 -->
                    <div class="rounded-md overflow-hidden border-2 transition-all"
                         :class="index === currentMediaIndex ? 'border-primary' : 'border-transparent'"
                    >
                      <div class="relative">
                        <img
                          :src="material.type === 'video' ? (material.thumbnail || 'https://via.placeholder.com/160x90?text=视频') : material.url"
                          alt="Thumbnail"
                          class="w-full h-24 object-cover"
                        />
                        <div v-if="material.type === 'video'" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
                          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M8 5v14l11-7z" />
                          </svg>
                        </div>
                      </div>
                    </div>

                    <!-- 素材标题 - 移到缩略图下方 -->
                    <div class="mt-1 text-center">
                      <p class="text-xs text-gray-600 truncate max-w-[120px]">{{ material.title }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 剧本和计划书模块 -->
            <DramaDocuments :documents="projectDetail?.documents || []" />

            <!-- 创作团队模块 -->
            <div class="bg-white rounded-xl shadow-md p-6 mb-8">
              <!-- 出品公司 -->
              <div v-if="parseCompanyData(projectDetail.productionTeam?.productionCompany).length > 0" class="mb-6">
                <h3 class="text-xl font-bold mb-4">出品公司</h3>
                <div class="flex flex-wrap gap-3">
                  <div v-for="company in parseCompanyData(projectDetail.productionTeam?.productionCompany)"
                       :key="company"
                       class="px-4 py-2 bg-gray-100 border border-gray-300 rounded-full text-sm text-gray-700">
                    {{ company }}
                  </div>
                </div>
              </div>

              <!-- 联合出品公司 -->
              <div v-if="parseCompanyData(projectDetail.productionTeam?.coProductionCompany).length > 0" class="mb-6">
                <h3 class="text-xl font-bold mb-4">联合出品公司</h3>
                <div class="flex flex-wrap gap-3">
                  <div v-for="company in parseCompanyData(projectDetail.productionTeam?.coProductionCompany)"
                       :key="company"
                       class="px-4 py-2 bg-gray-100 border border-gray-300 rounded-full text-sm text-gray-700">
                    {{ company }}
                  </div>
                </div>
              </div>

              <!-- 创作团队人员 - 双列布局 -->
              <div class="grid grid-cols-2 gap-6">

                <!-- 出品人 -->
                <div v-if="parseActorData(projectDetail.productionTeam?.executiveProducer).length > 0">
                  <h3 class="text-xl font-bold mb-4">出品人</h3>
                  <div class="flex flex-wrap gap-4">
                    <div v-for="person in parseActorData(projectDetail.productionTeam?.executiveProducer)"
                         :key="person.id || person.name"
                         class="flex flex-col items-center actor-avatar cursor-pointer">
                      <div class="w-16 h-16 mb-2 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                        <img v-if="person.avatarUrl || person.avatar"
                             :src="person.avatarUrl || person.avatar"
                             :alt="person.name"
                             class="w-full h-full object-cover"
                             @error="$event.target.style.display = 'none'" />
                        <span v-else class="text-gray-500 text-xs">头像</span>
                      </div>
                      <div class="text-xs text-gray-700 text-center leading-tight max-w-[64px] truncate">{{ person.name }}</div>
                    </div>
                  </div>
                </div>

                <!-- 联合出品人 -->
                <div v-if="parseActorData(projectDetail.productionTeam?.coExecutiveProducer).length > 0">
                  <h3 class="text-xl font-bold mb-4">联合出品人</h3>
                  <div class="flex flex-wrap gap-4">
                    <div v-for="person in parseActorData(projectDetail.productionTeam?.coExecutiveProducer)"
                         :key="person.id || person.name"
                         class="flex flex-col items-center actor-avatar cursor-pointer">
                      <div class="w-16 h-16 mb-2 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                        <img v-if="person.avatarUrl || person.avatar"
                             :src="person.avatarUrl || person.avatar"
                             :alt="person.name"
                             class="w-full h-full object-cover"
                             @error="$event.target.style.display = 'none'" />
                        <span v-else class="text-gray-500 text-xs">头像</span>
                      </div>
                      <div class="text-xs text-gray-700 text-center leading-tight max-w-[64px] truncate">{{ person.name }}</div>
                    </div>
                  </div>
                </div>

                <!-- 总制片人 -->
                <div v-if="parseActorData(projectDetail.productionTeam?.chiefProducer).length > 0">
                  <h3 class="text-xl font-bold mb-4">总制片人</h3>
                  <div class="flex flex-wrap gap-4">
                    <div v-for="person in parseActorData(projectDetail.productionTeam?.chiefProducer)"
                         :key="person.id || person.name"
                         class="flex flex-col items-center actor-avatar cursor-pointer">
                      <div class="w-16 h-16 mb-2 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                        <img v-if="person.avatarUrl || person.avatar"
                             :src="person.avatarUrl || person.avatar"
                             :alt="person.name"
                             class="w-full h-full object-cover"
                             @error="$event.target.style.display = 'none'" />
                        <span v-else class="text-gray-500 text-xs">头像</span>
                      </div>
                      <div class="text-xs text-gray-700 text-center leading-tight max-w-[64px] truncate">{{ person.name }}</div>
                    </div>
                  </div>
                </div>

                <!-- 联合制片人 -->
                <div v-if="parseActorData(projectDetail.productionTeam?.coProducer).length > 0">
                  <h3 class="text-xl font-bold mb-4">联合制片人</h3>
                  <div class="flex flex-wrap gap-4">
                    <div v-for="person in parseActorData(projectDetail.productionTeam?.coProducer)"
                         :key="person.id || person.name"
                         class="flex flex-col items-center actor-avatar cursor-pointer">
                      <div class="w-16 h-16 mb-2 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                        <img v-if="person.avatarUrl || person.avatar"
                             :src="person.avatarUrl || person.avatar"
                             :alt="person.name"
                             class="w-full h-full object-cover"
                             @error="$event.target.style.display = 'none'" />
                        <span v-else class="text-gray-500 text-xs">头像</span>
                      </div>
                      <div class="text-xs text-gray-700 text-center leading-tight max-w-[64px] truncate">{{ person.name }}</div>
                    </div>
                  </div>
                </div>

                <!-- 导演 -->
                <div v-if="parseActorData(projectDetail.productionTeam?.director).length > 0">
                  <h3 class="text-xl font-bold mb-4">导演</h3>
                  <div class="flex flex-wrap gap-4">
                    <div v-for="person in parseActorData(projectDetail.productionTeam?.director)"
                         :key="person.id || person.name"
                         class="flex flex-col items-center actor-avatar cursor-pointer">
                      <div class="w-16 h-16 mb-2 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                        <img v-if="person.avatarUrl || person.avatar"
                             :src="person.avatarUrl || person.avatar"
                             :alt="person.name"
                             class="w-full h-full object-cover"
                             @error="$event.target.style.display = 'none'" />
                        <span v-else class="text-gray-500 text-xs">头像</span>
                      </div>
                      <div class="text-xs text-gray-700 text-center leading-tight max-w-[64px] truncate">{{ person.name }}</div>
                    </div>
                  </div>
                </div>

                <!-- 编剧 -->
                <div v-if="parseActorData(projectDetail.productionTeam?.scriptwriter).length > 0">
                  <h3 class="text-xl font-bold mb-4">编剧</h3>
                  <div class="flex flex-wrap gap-4">
                    <div v-for="person in parseActorData(projectDetail.productionTeam?.scriptwriter)"
                         :key="person.id || person.name"
                         class="flex flex-col items-center actor-avatar cursor-pointer">
                      <div class="w-16 h-16 mb-2 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                        <img v-if="person.avatarUrl || person.avatar"
                             :src="person.avatarUrl || person.avatar"
                             :alt="person.name"
                             class="w-full h-full object-cover"
                             @error="$event.target.style.display = 'none'" />
                        <span v-else class="text-gray-500 text-xs">头像</span>
                      </div>
                      <div class="text-xs text-gray-700 text-center leading-tight max-w-[64px] truncate">{{ person.name }}</div>
                    </div>
                  </div>
                </div>

                <!-- 监制 -->
                <div v-if="parseActorData(projectDetail.productionTeam?.supervisor).length > 0">
                  <h3 class="text-xl font-bold mb-4">监制</h3>
                  <div class="flex flex-wrap gap-4">
                    <div v-for="person in parseActorData(projectDetail.productionTeam?.supervisor)"
                         :key="person.id || person.name"
                         class="flex flex-col items-center actor-avatar cursor-pointer">
                      <div class="w-16 h-16 mb-2 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                        <img v-if="person.avatarUrl || person.avatar"
                             :src="person.avatarUrl || person.avatar"
                             :alt="person.name"
                             class="w-full h-full object-cover"
                             @error="$event.target.style.display = 'none'" />
                        <span v-else class="text-gray-500 text-xs">头像</span>
                      </div>
                      <div class="text-xs text-gray-700 text-center leading-tight max-w-[64px] truncate">{{ person.name }}</div>
                    </div>
                  </div>
                </div>

                <!-- 统筹 -->
                <div v-if="parseActorData(projectDetail.productionTeam?.coordinator).length > 0">
                  <h3 class="text-xl font-bold mb-4">统筹</h3>
                  <div class="flex flex-wrap gap-4">
                    <div v-for="person in parseActorData(projectDetail.productionTeam?.coordinator)"
                         :key="person.id || person.name"
                         class="flex flex-col items-center actor-avatar cursor-pointer">
                      <div class="w-16 h-16 mb-2 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                        <img v-if="person.avatarUrl || person.avatar"
                             :src="person.avatarUrl || person.avatar"
                             :alt="person.name"
                             class="w-full h-full object-cover"
                             @error="$event.target.style.display = 'none'" />
                        <span v-else class="text-gray-500 text-xs">头像</span>
                      </div>
                      <div class="text-xs text-gray-700 text-center leading-tight max-w-[64px] truncate">{{ person.name }}</div>
                    </div>
                  </div>
                </div>

              </div>

              <!-- 无团队信息提示 -->
              <div v-if="!hasTeamMembers && !hasCompanyInfo" class="text-center py-8">
                <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                <p class="text-gray-500">暂无创作团队信息</p>
              </div>
            </div>

            <!-- 制作排期模块 -->
            <div class="bg-white rounded-xl shadow-md p-6 mb-8">
              <h2 class="text-xl font-bold mb-4">制作排期</h2>

              <div class="relative">
                <!-- 时间轴线 -->
                <div class="absolute top-5 left-5 bottom-0 w-0.5 bg-gray-200"></div>

                <div class="space-y-8 relative">
                  <div class="flex items-start ml-2">
                    <div class="flex-shrink-0 bg-primary rounded-full w-8 h-8 flex items-center justify-center text-white z-10">1</div>
                    <div class="ml-4 pt-1">
                      <h4 class="font-bold">前期筹备</h4>
                      <p class="text-gray-600">{{ formatDateRange(projectDetail.schedule?.preProduction) }}</p>
                      <p class="text-sm mt-1">剧本定稿、选角、场地考察、预算规划</p>
                    </div>
                  </div>

                  <div class="flex items-start ml-2">
                    <div class="flex-shrink-0 bg-primary rounded-full w-8 h-8 flex items-center justify-center text-white z-10">2</div>
                    <div class="ml-4 pt-1">
                      <h4 class="font-bold">拍摄制作</h4>
                      <p class="text-gray-600">{{ formatDateRange(projectDetail.schedule?.filming) }}</p>
                      <p class="text-sm mt-1">现场拍摄、演员指导、素材收集、实时监控</p>
                    </div>
                  </div>

                  <div class="flex items-start ml-2">
                    <div class="flex-shrink-0 bg-primary rounded-full w-8 h-8 flex items-center justify-center text-white z-10">3</div>
                    <div class="ml-4 pt-1">
                      <h4 class="font-bold">后期制作</h4>
                      <p class="text-gray-600">{{ formatDateRange(projectDetail.schedule?.postProduction) }}</p>
                      <p class="text-sm mt-1">剪辑、特效、配乐、调色、字幕</p>
                    </div>
                  </div>

                  <div class="flex items-start ml-2">
                    <div class="flex-shrink-0 bg-primary rounded-full w-8 h-8 flex items-center justify-center text-white z-10">4</div>
                    <div class="ml-4 pt-1">
                      <h4 class="font-bold">发行上线</h4>
                      <p class="text-gray-600">{{ formatDateRange(projectDetail.schedule?.release) }}</p>
                      <p class="text-sm mt-1">平台发布、营销推广、用户数据分析、收益分配</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 右栏：募资信息和投资表单 -->
          <div class="w-full lg:w-3/10">
            <!-- 项目概览卡片 -->
            <div class="bg-white rounded-xl shadow-md p-6 mb-6">
              <h2 class="text-xl font-bold mb-4">项目概览</h2>

              <!-- 短剧封面和基本信息 -->
              <div class="flex gap-4 mb-4">
                <!-- 短剧封面 -->
                <div class="flex-shrink-0">
                  <img
                    :src="projectDetail.cover || 'https://via.placeholder.com/160x220?text=暂无封面'"
                    :alt="projectDetail.title"
                    class="w-32 h-44 object-cover rounded-lg border border-gray-200"
                    @error="$event.target.src = 'https://via.placeholder.com/160x220?text=暂无封面'"
                  />
                  <div class="text-xs text-center text-gray-500 mt-1">
                    {{ projectDetail.episodes }}集/共{{ projectDetail.episodes * (projectDetail.episodeLength || 5) }}分钟
                  </div>
                </div>

                <!-- 短剧信息 -->
                <div class="flex-1 min-w-0">
                  <h3 class="font-bold text-lg mb-2 line-clamp-2">{{ projectDetail.title }}</h3>

                  <!-- 标签 -->
                  <div class="flex flex-wrap gap-1 mb-3">
                    <span
                      v-for="tag in parseTagData(projectDetail.tags).slice(0, 4)"
                      :key="tag.id || tag.name"
                      class="px-2 py-1 text-xs rounded-full"
                      :style="{
                        backgroundColor: tag.backgroundColor || '#f3f4f6',
                        color: tag.fontColor || '#666666'
                      }"
                    >
                      {{ tag.name }}
                    </span>
                  </div>

                  <!-- 短剧描述 -->
                  <p class="text-sm text-gray-600 line-clamp-3 mb-3">
                    {{ projectDetail.description }}
                  </p>
                </div>
              </div>

              <!-- 目标平台 -->
              <div class="mb-4">
                <div class="flex items-center gap-4 mb-3">
                  <h4 class="text-xl font-bold text-gray-800">目标平台</h4>
                  <!-- 预计热度显示 -->
                  <span class="text-sm text-gray-600">
                    预计热度: <span class="font-medium text-orange-600">{{ formatHotness(projectDetail.projectedViews) }}</span>
                  </span>
                </div>

                <!-- 平台列表 - 横向排列，靠左显示，无卡片背景 -->
                <div class="flex flex-wrap gap-4">
                  <div
                    v-for="(platform, index) in getPlatformList().slice(0, 6)"
                    :key="index"
                    class="flex flex-col items-center platform-icon cursor-pointer"
                  >
                    <!-- 平台Logo - 与演员头像保持一致的尺寸 -->
                    <div class="w-16 h-16 mb-2 flex items-center justify-center">
                      <img
                        v-if="platform.platform_logo_url"
                        :src="platform.platform_logo_url"
                        :alt="platform.platform_name"
                        class="w-full h-full object-contain rounded-lg"
                        @error="$event.target.style.display = 'none'"
                      />
                      <div v-else class="w-full h-full bg-red-500 rounded-lg flex items-center justify-center">
                        <span class="text-white text-lg font-bold">红</span>
                      </div>
                    </div>
                    <!-- 平台名称 -->
                    <span class="text-xs text-gray-700 text-center leading-tight max-w-[60px] truncate">{{ platform.platform_name }}</span>
                  </div>
                </div>
              </div>

              <!-- 主演阵容 -->
              <div v-if="parseActorData(projectDetail.cast).length > 0">
                <h4 class="text-xl font-bold text-gray-800 mb-3">主演阵容</h4>
                <!-- 演员列表 - 横向排列，靠左显示，无卡片背景 -->
                <div class="flex flex-wrap gap-4">
                  <div
                    v-for="(actor, index) in parseActorData(projectDetail.cast).slice(0, 8)"
                    :key="actor.id || index"
                    class="flex flex-col items-center actor-avatar cursor-pointer"
                  >
                    <!-- 演员头像 - 更大尺寸 -->
                    <div class="w-16 h-16 mb-2 rounded-full overflow-hidden bg-gray-200 flex items-center justify-center">
                      <img
                        v-if="actor.avatarUrl || actor.avatar"
                        :src="actor.avatarUrl || actor.avatar"
                        :alt="actor.name"
                        class="w-full h-full object-cover"
                        @error="$event.target.style.display = 'none'"
                      />
                      <span v-else class="text-gray-500 text-xs">头像</span>
                    </div>
                    <!-- 演员姓名 -->
                    <div class="text-xs text-gray-700 text-center leading-tight max-w-[64px] truncate">{{ actor.name }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 募资状态卡片 -->
            <div class="bg-white rounded-xl shadow-md p-6 mb-6">
              <h2 class="text-xl font-bold mb-4">
                筹募状态
                <span class="text-sm font-normal text-gray-500 ml-2">(剩余{{ remainingFundingPercentage }}%份额)</span>
              </h2>
              
              <!-- 进度条 -->
              <div class="mb-4">
                <div class="flex justify-between text-sm mb-1">
                  <span class="text-gray-600 flex items-center">
                    已筹
                    <span class="mx-1 text-lg">🐚</span>
                    {{ formatCurrency(projectDetail.currentFunding) }}
                  </span>
                  <span class="font-medium">{{ fundingProgress }}%</span>
                </div>
                <div class="h-3 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    class="h-full rounded-full transition-all duration-500"
                    :class="progressColor"
                    :style="{width: `${fundingProgress}%`}"
                  ></div>
                </div>
                <div class="flex justify-between text-xs text-gray-500 mt-1">
                  <span class="flex items-center">
                    目标
                    <span class="mx-1 text-sm">🐚</span>
                    {{ formatCurrency(projectDetail.fundingGoal) }}
                  </span>
                  <span>剩余 {{ projectDetail.remainingDays }} 天</span>
                </div>
              </div>
              
              <!-- 投资人头像滚动条 -->
              <div class="bg-gray-50 rounded-lg p-3 mb-6 overflow-hidden">
                <div class="flex items-center">
                  <div class="flex -space-x-2 mr-3">
                    <!-- 显示前8个去重的投资人 -->
                    <div
                      v-for="(investor, index) in uniqueInvestors.slice(0, 8)"
                      :key="investor.id"
                      class="w-8 h-8 rounded-full flex items-center justify-center text-white text-xs"
                      :class="getAvatarBgColor(index)"
                    >
                      <span>{{ getInitials(investor.realName || investor.username) }}</span>
                    </div>
                    <!-- 显示剩余投资人数量 -->
                    <div
                      v-if="uniqueInvestors.length > 8"
                      class="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center text-white text-xs"
                    >
                      +{{ uniqueInvestors.length - 8 }}
                    </div>
                  </div>
                  <div class="text-sm">
                    <span class="font-medium">{{ uniqueInvestors.length }}人</span> 已投资此项目
                  </div>
                </div>
              </div>
              
              <!-- 投资金额选择 -->
              <div class="mb-6">
                <h3 class="font-bold mb-3">选择投资金额</h3>
                <div class="grid grid-cols-2 gap-2 mb-3">
                  <button
                    @click="setAmount(10000)"
                    class="py-2 px-3 border rounded-lg transition-colors text-center flex items-center justify-center"
                    :class="selectedAmount === 10000 ? 'bg-primary text-white border-primary' : 'border-gray-300 hover:border-primary'"
                  >
                    <span class="mr-1">🐚</span>
                    1万
                  </button>
                  <button
                    @click="setAmount(30000)"
                    class="py-2 px-3 border rounded-lg transition-colors text-center flex items-center justify-center"
                    :class="selectedAmount === 30000 ? 'bg-primary text-white border-primary' : 'border-gray-300 hover:border-primary'"
                  >
                    <span class="mr-1">🐚</span>
                    3万
                  </button>
                  <button
                    @click="setAmount(50000)"
                    class="py-2 px-3 border rounded-lg transition-colors text-center flex items-center justify-center"
                    :class="selectedAmount === 50000 ? 'bg-primary text-white border-primary' : 'border-gray-300 hover:border-primary'"
                  >
                    <span class="mr-1">🐚</span>
                    5万
                  </button>
                  <button
                    @click="setAmount(100000)"
                    class="py-2 px-3 border rounded-lg transition-colors text-center flex items-center justify-center"
                    :class="selectedAmount === 100000 ? 'bg-primary text-white border-primary' : 'border-gray-300 hover:border-primary'"
                  >
                    <span class="mr-1">🐚</span>
                    10万
                  </button>
                </div>
                
                <!-- 自定义金额 -->
                <div class="flex mb-3">
                  <input
                    v-model="customAmount"
                    type="number"
                    :min="projectDetail?.minInvestment || 50000"
                    step="10000"
                    :placeholder="`自定义金额（最低${(projectDetail?.minInvestment || 50000) >= 10000 ? Math.round((projectDetail?.minInvestment || 50000) / 10000) + '万' : (projectDetail?.minInvestment || 50000)}元）`"
                    class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                  />
                  <button
                    @click="setCustomAmount"
                    class="px-4 py-2 bg-gray-100 border border-gray-300 rounded-r-lg hover:bg-gray-200 transition-colors"
                  >
                    确定
                  </button>
                </div>
                
                <!-- 投资按钮 -->
                <button
                  @click="openInvestmentDialog"
                  class="w-full py-3 bg-primary hover:bg-primary-dark text-white font-bold rounded-lg transition-colors flex items-center justify-center"
                  :disabled="!selectedAmount"
                  :class="{'opacity-50 cursor-not-allowed': !selectedAmount}"
                >
                  <span class="mr-2 text-xl">🐚</span>
                  <span>{{ selectedAmount ? `参与众筹 ${formatCurrency(selectedAmount)}` : '请选择投资金额' }}</span>
                </button>

                <!-- 众筹协议勾选框 -->
                <div class="mt-3">
                  <div
                    ref="agreementContainer"
                    class="flex items-center justify-center space-x-2"
                    :class="{ 'shake-animation': showAgreementError }"
                  >
                    <input
                      type="checkbox"
                      id="agreeCrowdfundingTerms"
                      v-model="agreeCrowdfundingTerms"
                      class="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"
                      @change="handleAgreementChange"
                    >
                    <label for="agreeCrowdfundingTerms" class="text-sm text-gray-600 leading-relaxed cursor-pointer whitespace-nowrap">
                      我已阅读并同意<button
                        type="button"
                        @click="openAgreement('crowdfunding-agreement')"
                        class="text-primary hover:text-primary-dark hover:underline font-medium"
                      >《众筹协议》</button>并知晓风险
                    </label>
                  </div>

                  <!-- 协议错误提示 -->
                  <div v-if="showAgreementError" class="text-center mt-1">
                    <span class="text-red-500 text-xs">请先同意《众筹协议》并知晓风险</span>
                  </div>
                </div>

                <!-- 安全标识 -->
                <div class="flex items-center justify-center mt-2 text-xs text-gray-500">
                  <svg class="w-4 h-4 mr-1 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                  <span>参与众筹代表认购意向，认购成功撮合与承制商签署合同</span>
                </div>
              </div>
            </div>
            
            <!-- 投资阶梯表 -->
            <div v-if="projectDetail.investmentTiers && projectDetail.investmentTiers.length > 0" class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
              <h2 class="text-xl font-bold p-6 pb-4">投资权益</h2>
              
              <!-- 移动端滚动容器 -->
              <div class="overflow-x-auto">
                <table class="w-full border-collapse">
                  <thead>
                    <tr>
                      <th class="bg-primary text-white p-4 text-left whitespace-nowrap">投资额度</th>
                      <th class="bg-gray-100 p-4 text-left">权益</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(tier, index) in projectDetail.investmentTiers" :key="tier.id || index" :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-50'">
                      <td class="bg-primary text-white p-4 whitespace-nowrap">
                        <div class="font-medium">{{ tier.tierName }}</div>
                        <div class="text-sm opacity-90">
                          ¥{{ formatCurrency(tier.minAmount) }}
                          <span v-if="tier.maxAmount"> - ¥{{ formatCurrency(tier.maxAmount) }}</span>
                          <span v-else>+</span>
                        </div>
                      </td>
                      <td class="p-4">
                        <div class="space-y-1">
                          <div
                            v-for="(benefit, i) in getBenefitsList(tier.benefits)"
                            :key="i"
                            class="flex items-start"
                          >
                            <span class="text-primary mr-2 mt-0.5">•</span>
                            <span class="text-sm leading-relaxed">{{ benefit }}</span>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            
            <!-- 风险提示 -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-5">
              <h2 class="text-lg font-bold text-yellow-800 mb-3 flex items-center">
                <svg class="w-5 h-5 mr-2 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                风险提示
              </h2>
              
              <ul class="text-sm text-yellow-700 space-y-2">
                <li v-for="(risk, index) in projectDetail.riskManagement" :key="index" class="flex items-start">
                  <span class="text-yellow-600 mr-2">⚠️</span>
                  <span>{{ risk }}</span>
                </li>
              </ul>
              
              <!-- 企业资质验证 -->
              <div class="mt-4 pt-4 border-t border-yellow-200 flex items-center justify-between">
                <span class="text-sm text-yellow-700">企业资质验证</span>
                <button @click="viewBusinessLicense" class="text-sm text-blue-600 hover:underline flex items-center">
                  查看营业执照信息
                  <svg class="w-4 h-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 投资确认弹窗 -->
  <InvestmentConfirmDialog
    :visible="showInvestmentDialog"
    :project="projectDetail"
    :amount="selectedAmount || 0"
    @close="closeInvestmentDialog"
    @success="onInvestmentSuccess"
  />

  <!-- 营业执照预览弹窗 -->
  <div
    v-if="showBusinessLicense"
    class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
    @click="showBusinessLicense = false"
  >
    <div class="relative max-w-4xl max-h-full p-4">
      <button
        @click="showBusinessLicense = false"
        class="absolute top-2 right-2 text-white text-2xl hover:text-gray-300 z-10"
      >
        ×
      </button>
      <img
        :src="businessLicenseUrl"
        alt="营业执照"
        class="max-w-full max-h-full object-contain rounded-lg"
        @click.stop
      />
      <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white text-center">
        <p class="text-lg font-medium">{{ projectDetail?.businessLicense?.companyName || '出品公司' }}</p>
        <p class="text-sm opacity-75">营业执照</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 确保进度条的过渡效果流畅 */
.progress-bar-transition {
  transition: width 0.5s ease-in-out;
}

/* 自定义滚动条样式 */
.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #cdcdcd;
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 抖动动画 */
.shake-animation {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* 平台图标样式 */
.platform-icon {
  transition: transform 0.2s ease;
}

.platform-icon:hover {
  transform: scale(1.1);
}

/* 演员头像样式 */
.actor-avatar {
  transition: transform 0.2s ease;
}

.actor-avatar:hover {
  transform: scale(1.1);
}

/* 素材滚动条样式 */
.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #667eea;
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #5a67d8;
}

/* 素材卡片悬停效果 */
.hover\:transform:hover {
  transform: scale(1.02);
}

.hover\:scale-102:hover {
  transform: scale(1.02);
}
</style>