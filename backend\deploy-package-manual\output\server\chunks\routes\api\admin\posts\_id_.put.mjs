import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, f as createError, q as query, r as readBody } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__put = defineEventHandler(async (event) => {
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const postId = getRouterParam(event, "id");
    if (!postId || isNaN(Number(postId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u63A8\u6587ID"
      });
    }
    const existingPost = await query(
      "SELECT * FROM posts WHERE id = ?",
      [postId]
    );
    if (existingPost.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u63A8\u6587\u4E0D\u5B58\u5728"
      });
    }
    const body = await readBody(event);
    const {
      title,
      slug,
      content,
      author,
      status,
      isOnline,
      publishDate
    } = body;
    if (slug && slug !== existingPost[0].slug) {
      const duplicatePost = await query(
        "SELECT id FROM posts WHERE slug = ? AND id != ?",
        [slug, postId]
      );
      if (duplicatePost.length > 0) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u552F\u4E00\u6807\u8BC6\u5DF2\u5B58\u5728\uFF0C\u8BF7\u4F7F\u7528\u5176\u4ED6\u6807\u8BC6"
        });
      }
    }
    if (status) {
      const validStatuses = ["draft", "pending", "published", "archived"];
      if (!validStatuses.includes(status)) {
        throw createError({
          statusCode: 400,
          statusMessage: "\u65E0\u6548\u7684\u72B6\u6001\u503C"
        });
      }
    }
    const updateFields = [];
    const updateValues = [];
    if (title !== void 0) {
      updateFields.push("title = ?");
      updateValues.push(title);
    }
    if (slug !== void 0) {
      updateFields.push("slug = ?");
      updateValues.push(slug);
    }
    if (content !== void 0) {
      updateFields.push("content = ?");
      updateValues.push(content);
    }
    if (author !== void 0) {
      updateFields.push("author = ?");
      updateValues.push(author);
    }
    if (status !== void 0) {
      updateFields.push("status = ?");
      updateValues.push(status);
    }
    if (isOnline !== void 0) {
      updateFields.push("is_online = ?");
      updateValues.push(isOnline ? 1 : 0);
    }
    if (publishDate !== void 0) {
      updateFields.push("publish_date = ?");
      let formattedDate = null;
      if (publishDate) {
        try {
          const date = new Date(publishDate);
          formattedDate = date.toISOString().slice(0, 19).replace("T", " ");
        } catch (error) {
          console.error("\u65E5\u671F\u683C\u5F0F\u8F6C\u6362\u5931\u8D25:", error);
          formattedDate = null;
        }
      }
      updateValues.push(formattedDate);
    }
    updateFields.push("updated_at = NOW()");
    updateValues.push(postId);
    if (updateFields.length > 1) {
      const updateQuery = `UPDATE posts SET ${updateFields.join(", ")} WHERE id = ?`;
      await query(updateQuery, updateValues);
    }
    return {
      success: true,
      message: "\u63A8\u6587\u66F4\u65B0\u6210\u529F",
      data: {
        id: postId
      }
    };
  } catch (error) {
    console.error("\u66F4\u65B0\u63A8\u6587\u5931\u8D25:", error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.message || "\u66F4\u65B0\u63A8\u6587\u5931\u8D25"
    });
  }
});

export { _id__put as default };
//# sourceMappingURL=_id_.put.mjs.map
