# 依赖文件夹
node_modules/
*/node_modules/
**/node_modules/

# 构建输出
dist/
build/
.output/
.nitro/
.nuxt/
.next/

# 缓存文件
.cache/
.temp/
.tmp/

# 环境配置文件
.env.local
.env.development.local
.env.test.local

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo

# 操作系统文件
.DS_Store
Thumbs.db

# Git文件
.git/
.gitignore

# 文档文件
docs/
*.md
README*

# 日志文件
*.log
logs/

# 测试文件
test/
tests/
*.test.js
*.test.ts

# Docker文件本身
Dockerfile
docker-compose.yml
.dockerignore
