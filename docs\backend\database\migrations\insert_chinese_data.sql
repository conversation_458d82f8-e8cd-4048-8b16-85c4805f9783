-- 插入中文示例数据（正确的字符集设置）
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;
SET CHARACTER SET utf8mb4;

-- 先清理英文数据
DELETE FROM user_asset_transactions WHERE user_id = 3;
DELETE FROM user_returns WHERE user_id = 3;
DELETE FROM user_notifications WHERE user_id = 3;
DELETE FROM user_investments WHERE user_id = 3;

-- 插入中文投资记录
INSERT INTO user_investments (
    user_id, project_id, project_name, investment_amount, investment_date,
    expected_return_rate, expected_return_amount, actual_return_amount,
    project_status, investment_status, start_date, end_date, progress
) VALUES 
(3, 1, '都市情感系列', 500000, '2023-05-20', 18.5, 92500, 75000, 'active', 'active', '2023-05-20', '2024-05-19', 65),
(3, 2, '青春有你系列', 400000, '2023-07-10', 16.0, 64000, 48000, 'active', 'active', '2023-07-10', '2024-07-09', 40),
(3, 3, '奇幻世界系列', 300000, '2023-09-01', 20.0, 60000, 15000, 'active', 'active', '2023-09-01', '2024-08-31', 15);

-- 获取投资记录ID
SET @investment1 = (SELECT id FROM user_investments WHERE user_id = 3 AND project_name = '都市情感系列' LIMIT 1);
SET @investment2 = (SELECT id FROM user_investments WHERE user_id = 3 AND project_name = '青春有你系列' LIMIT 1);
SET @investment3 = (SELECT id FROM user_investments WHERE user_id = 3 AND project_name = '奇幻世界系列' LIMIT 1);

-- 插入收益记录
INSERT INTO user_returns (
    user_id, investment_id, return_type, return_amount, return_date,
    return_period, description, status
) VALUES 
(3, @investment1, 'monthly', 25000, '2023-10-01', '2023年9月', '都市情感系列9月份收益分红', 'paid'),
(3, @investment2, 'monthly', 16000, '2023-10-01', '2023年9月', '青春有你系列9月份收益分红', 'paid'),
(3, @investment3, 'monthly', 5000, '2023-10-01', '2023年9月', '奇幻世界系列9月份收益分红', 'paid'),
(3, @investment1, 'monthly', 25000, '2023-11-01', '2023年10月', '都市情感系列10月份收益分红', 'paid'),
(3, @investment2, 'monthly', 16000, '2023-11-01', '2023年10月', '青春有你系列10月份收益分红', 'paid'),
(3, @investment3, 'monthly', 5000, '2023-11-01', '2023年10月', '奇幻世界系列10月份收益分红', 'paid');

-- 插入中文通知
INSERT INTO user_notifications (
    user_id, type, title, content, is_read, priority
) VALUES 
(3, 'system', '第三季度财务报告已发布', '您可以在项目详情页查看最新的财务报告', 0, 'normal'),
(3, 'project', '都市情感第8集开始拍摄', '项目进展顺利，预计按时完成', 1, 'normal'),
(3, 'return', '9月份收益已发放至您的账户', '本月共获得46000钻石收益', 1, 'normal'),
(3, 'system', '投资者线上会议将于下周三举行', '请关注邮件通知，准时参加会议', 0, 'high'),
(3, 'investment', '新项目科幻未来开始募资', '预期年化收益率22%，限额投资', 0, 'normal');

-- 插入资产变动记录
INSERT INTO user_asset_transactions (
    user_id, transaction_type, amount, balance_before, balance_after,
    related_type, description, transaction_no, status
) VALUES 
(3, 'shells_out', 500000, 800000, 300000, 'investment', '投资都市情感系列', 'TXN202305200001', 'completed'),
(3, 'diamonds_in', 25000, 130000, 155000, 'return', '都市情感系列9月收益', 'TXN202310010001', 'completed'),
(3, 'shells_out', 400000, 300000, -100000, 'investment', '投资青春有你系列', 'TXN202307100001', 'completed'),
(3, 'diamonds_in', 16000, 155000, 171000, 'return', '青春有你系列9月收益', 'TXN202310010002', 'completed'),
(3, 'shells_out', 300000, -100000, -400000, 'investment', '投资奇幻世界系列', 'TXN202309010001', 'completed'),
(3, 'diamonds_in', 5000, 171000, 176000, 'return', '奇幻世界系列9月收益', 'TXN202310010003', 'completed');
