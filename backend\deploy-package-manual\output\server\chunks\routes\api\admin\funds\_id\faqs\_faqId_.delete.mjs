import { c as defineEvent<PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _faqId__delete = defineEventHandler(async (event) => {
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const fundId = getRouterParam(event, "id");
    const faqId = getRouterParam(event, "faqId");
    if (!fundId || isNaN(Number(fundId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u57FA\u91D1ID"
      });
    }
    if (!faqId || isNaN(Number(faqId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684FAQ ID"
      });
    }
    const existingFaq = await query(
      "SELECT id, fund_id, question FROM fund_faqs WHERE id = ? AND fund_id = ?",
      [faqId, fundId]
    );
    if (existingFaq.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u57FA\u91D1FAQ\u4E0D\u5B58\u5728"
      });
    }
    await query(
      "DELETE FROM fund_faqs WHERE id = ? AND fund_id = ?",
      [faqId, fundId]
    );
    await logAuditAction({
      action: "ADMIN_DELETE_FUND_FAQ",
      description: `\u7BA1\u7406\u5458\u5220\u9664\u57FA\u91D1FAQ: \u57FA\u91D1ID=${fundId}, FAQ ID=${faqId}`,
      userId: adminPayload.id,
      username: adminPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      metadata: {
        fundId: Number(fundId),
        faqId: Number(faqId),
        deletedQuestion: existingFaq[0].question
      }
    });
    return {
      success: true,
      message: "\u57FA\u91D1FAQ\u5220\u9664\u6210\u529F",
      data: {
        id: Number(faqId),
        fundId: Number(fundId)
      }
    };
  } catch (error) {
    logger.error("\u5220\u9664\u57FA\u91D1FAQ\u5931\u8D25", {
      error: error.message,
      fundId: getRouterParam(event, "id"),
      faqId: getRouterParam(event, "faqId")
    });
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "\u5220\u9664\u57FA\u91D1FAQ\u5931\u8D25"
    });
  }
});

export { _faqId__delete as default };
//# sourceMappingURL=_faqId_.delete.mjs.map
