import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getR<PERSON>er<PERSON><PERSON><PERSON>, f as createError, r as readBody, q as query } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const status_put = defineEventHandler(async (event) => {
  try {
    console.log("\u6295\u8D44\u8BB0\u5F55\u72B6\u6001\u66F4\u65B0API\u88AB\u8C03\u7528");
    const recordId = getRouterParam(event, "id");
    if (!recordId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6295\u8D44\u8BB0\u5F55ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const body = await readBody(event);
    const { status } = body;
    const validStatuses = ["pending", "completed", "failed", "cancelled"];
    if (!status || !validStatuses.includes(status)) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u72B6\u6001\u503C"
      });
    }
    console.log(`\u66F4\u65B0\u6295\u8D44\u8BB0\u5F55 ${recordId} \u72B6\u6001\u4E3A: ${status}`);
    const checkQuery = `
      SELECT id, status, user_id, amount, description
      FROM user_asset_transactions
      WHERE id = ? AND transaction_type = 'shells_out' AND related_type = 'investment'
    `;
    const existingRecords = await query(checkQuery, [recordId]);
    if (existingRecords.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u6295\u8D44\u8BB0\u5F55\u4E0D\u5B58\u5728"
      });
    }
    const existingRecord = existingRecords[0];
    console.log("\u73B0\u6709\u6295\u8D44\u8BB0\u5F55:", existingRecord);
    const updateQuery = `
      UPDATE user_asset_transactions
      SET status = ?, updated_at = NOW()
      WHERE id = ?
    `;
    await query(updateQuery, [status, recordId]);
    console.log(`\u6295\u8D44\u8BB0\u5F55 ${recordId} \u72B6\u6001\u66F4\u65B0\u6210\u529F`);
    return {
      success: true,
      message: "\u6295\u8D44\u8BB0\u5F55\u72B6\u6001\u66F4\u65B0\u6210\u529F"
    };
  } catch (error) {
    console.error("\u66F4\u65B0\u6295\u8D44\u8BB0\u5F55\u72B6\u6001\u5931\u8D25:", error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "\u66F4\u65B0\u6295\u8D44\u8BB0\u5F55\u72B6\u6001\u5931\u8D25"
    });
  }
});

export { status_put as default };
//# sourceMappingURL=status.put.mjs.map
