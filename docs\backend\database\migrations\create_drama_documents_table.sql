-- 创建短剧文档表
-- 基于基金文档表 fund_documents 的设计，为短剧管理系统创建文档管理功能

-- 创建短剧文档表
CREATE TABLE IF NOT EXISTS `drama_documents` (
  `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '文档ID',
  `drama_id` INT(11) NOT NULL COMMENT '关联的短剧ID',
  `name` VARCHAR(255) NOT NULL COMMENT '文档名称',
  `file_url` VARCHAR(500) NOT NULL COMMENT '文件URL地址',
  `file_type` VARCHAR(50) DEFAULT NULL COMMENT '文件类型（PDF、DOC、DOCX等）',
  `file_size` VARCHAR(50) DEFAULT NULL COMMENT '文件大小（格式化后的字符串）',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  PRIMARY KEY (`id`),
  INDEX `idx_drama_id` (`drama_id`),
  INDEX `idx_drama_name` (`drama_id`, `name`(100)),  -- 限制name字段索引长度为100字符
  INDEX `idx_created_at` (`created_at`),

  -- 外键约束：关联到短剧表
  CONSTRAINT `fk_drama_documents_drama_id`
    FOREIGN KEY (`drama_id`)
    REFERENCES `drama_series` (`id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短剧文档表';

-- 验证表创建结果
SELECT 
  TABLE_NAME,
  TABLE_COMMENT,
  ENGINE,
  TABLE_COLLATION
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'drama_documents';

-- 验证表结构
DESCRIBE drama_documents;

-- 验证索引
SHOW INDEX FROM drama_documents;

-- 验证外键约束
SELECT 
  CONSTRAINT_NAME,
  TABLE_NAME,
  COLUMN_NAME,
  REFERENCED_TABLE_NAME,
  REFERENCED_COLUMN_NAME,
  DELETE_RULE,
  UPDATE_RULE
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'drama_documents' 
  AND REFERENCED_TABLE_NAME IS NOT NULL;
