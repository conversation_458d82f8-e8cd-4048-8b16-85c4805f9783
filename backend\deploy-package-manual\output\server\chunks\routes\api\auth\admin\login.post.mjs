import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, r as readBody, f as createError, C as findAdminByCredentials, h as logAuditAction, i as getHeader, e as getClientIP, D as verifyPassword, E as generateAdminAccessToken, F as generateAdminRefreshToken, G as updateAdminLastLogin, l as logger } from '../../../../_/nitro.mjs';
import { s as setRefreshTokenCookie } from '../../../../_/cookie-utils.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const login_post = defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { username, password } = body;
    if (!username || !password) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u7528\u6237\u540D\u548C\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const admin = await findAdminByCredentials(username);
    if (!admin) {
      await logAuditAction({
        action: "ADMIN_LOGIN_FAILED",
        description: `\u7BA1\u7406\u5458\u767B\u5F55\u5931\u8D25\uFF1A\u7528\u6237\u4E0D\u5B58\u5728 (${username})`,
        ip: getClientIP(event),
        userAgent: getHeader(event, "user-agent") || "",
        details: { username }
      });
      throw createError({
        statusCode: 401,
        statusMessage: "\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF"
      });
    }
    const isValidPassword = await verifyPassword(password, admin.password);
    if (!isValidPassword) {
      await logAuditAction({
        action: "ADMIN_LOGIN_FAILED",
        description: `\u7BA1\u7406\u5458\u767B\u5F55\u5931\u8D25\uFF1A\u5BC6\u7801\u9519\u8BEF`,
        ip: getClientIP(event),
        userAgent: getHeader(event, "user-agent") || "",
        userId: admin.id,
        username: admin.username,
        details: { reason: "wrong_password" }
      });
      throw createError({
        statusCode: 401,
        statusMessage: "\u7528\u6237\u540D\u6216\u5BC6\u7801\u9519\u8BEF"
      });
    }
    const accessToken = await generateAdminAccessToken(admin);
    const refreshToken = await generateAdminRefreshToken(admin);
    await updateAdminLastLogin(admin.id);
    setRefreshTokenCookie(event, refreshToken);
    await logAuditAction({
      action: "ADMIN_LOGIN_SUCCESS",
      description: "\u7BA1\u7406\u5458\u767B\u5F55\u6210\u529F",
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      userId: admin.id,
      username: admin.username
    });
    logger.info("\u7BA1\u7406\u5458\u767B\u5F55\u6210\u529F", {
      adminId: admin.id,
      username: admin.username,
      ip: getClientIP(event)
    });
    return {
      success: true,
      message: "\u767B\u5F55\u6210\u529F",
      data: {
        accessToken,
        admin: {
          id: admin.id,
          username: admin.username,
          email: admin.email,
          real_name: admin.real_name,
          avatar: admin.avatar,
          home_path: admin.home_path
        }
      }
    };
  } catch (error) {
    logger.error("\u7BA1\u7406\u5458\u767B\u5F55\u5931\u8D25", {
      error: error.message,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { login_post as default };
//# sourceMappingURL=login.post.mjs.map
