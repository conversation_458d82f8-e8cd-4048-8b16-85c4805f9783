<template>
  <VbenDrawer
    v-model:open="drawerVisible"
    :title="drawerTitle"
    :width="600"
    @close="handleClose"
  >
    <Form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
      @finish="handleSubmit"
    >
      <FormItem label="短剧标题" name="title">
        <Input v-model:value="formData.title" placeholder="请输入短剧标题" />
      </FormItem>

      <FormItem label="封面图片" name="cover">
        <Input v-model:value="formData.cover" placeholder="请输入封面图片URL" />
      </FormItem>

      <FormItem label="短剧描述" name="description">
        <Textarea
          v-model:value="formData.description"
          :rows="4"
          placeholder="请输入短剧描述"
        />
      </FormItem>

      <Row :gutter="16">
        <Col :span="12">
          <FormItem label="募资目标(元)" name="fundingGoal">
            <InputNumber
              v-model:value="formData.fundingGoal"
              :min="1"
              :precision="2"
              style="width: 100%"
              placeholder="请输入募资目标"
            />
          </FormItem>
        </Col>
        <Col :span="12">
          <FormItem label="最小投资(元)" name="minInvestment">
            <InputNumber
              v-model:value="formData.minInvestment"
              :min="1"
              :precision="2"
              style="width: 100%"
              placeholder="请输入最小投资金额"
            />
          </FormItem>
        </Col>
      </Row>

      <Row :gutter="16">
        <Col :span="12">
          <FormItem label="集数" name="episodes">
            <InputNumber
              v-model:value="formData.episodes"
              :min="1"
              style="width: 100%"
              placeholder="请输入集数"
            />
          </FormItem>
        </Col>
        <Col :span="12">
          <FormItem label="单集时长(分钟)" name="episodeLength">
            <InputNumber
              v-model:value="formData.episodeLength"
              :min="1"
              style="width: 100%"
              placeholder="请输入单集时长"
            />
          </FormItem>
        </Col>
      </Row>

      <Row :gutter="16">
        <Col :span="12">
          <FormItem label="导演" name="director">
            <Input v-model:value="formData.director" placeholder="请输入导演姓名" />
          </FormItem>
        </Col>
        <Col :span="12">
          <FormItem label="制片人" name="producer">
            <Input v-model:value="formData.producer" placeholder="请输入制片人姓名" />
          </FormItem>
        </Col>
      </Row>

      <Row :gutter="16">
        <Col :span="12">
          <FormItem label="状态" name="status">
            <Select v-model:value="formData.status" placeholder="请选择状态">
              <SelectOption
                v-for="option in DRAMA_STATUS_OPTIONS"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </SelectOption>
            </Select>
          </FormItem>
        </Col>
        <Col :span="12">
          <FormItem label="剩余天数" name="remainingDays">
            <InputNumber
              v-model:value="formData.remainingDays"
              :min="1"
              style="width: 100%"
              placeholder="请输入剩余天数"
            />
          </FormItem>
        </Col>
      </Row>

      <FormItem label="标签" name="tags">
        <Select
          v-model:value="formData.tags"
          mode="tags"
          placeholder="请输入标签，按回车添加"
          style="width: 100%"
        />
      </FormItem>

      <FormItem label="演员列表" name="cast">
        <Select
          v-model:value="formData.cast"
          mode="tags"
          placeholder="请输入演员姓名，按回车添加"
          style="width: 100%"
        />
      </FormItem>

      <FormItem label="目标平台" name="targetPlatform">
        <Select
          v-model:value="formData.targetPlatform"
          mode="multiple"
          placeholder="请选择目标平台"
          style="width: 100%"
        >
          <SelectOption
            v-for="option in TARGET_PLATFORM_OPTIONS"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </SelectOption>
        </Select>
      </FormItem>

      <Row :gutter="16">
        <Col :span="12">
          <FormItem label="预期上映日期" name="expectedReleaseDate">
            <DatePicker
              v-model:value="formData.expectedReleaseDate"
              style="width: 100%"
              placeholder="请选择预期上映日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </FormItem>
        </Col>
        <Col :span="12">
          <FormItem label="预期收益" name="expectedReturn">
            <Input v-model:value="formData.expectedReturn" placeholder="请输入预期收益" />
          </FormItem>
        </Col>
      </Row>
    </Form>

    <template #footer>
      <Space>
        <Button @click="handleClose">取消</Button>
        <Button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </Button>
      </Space>
    </template>
  </VbenDrawer>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue';
import { message } from 'ant-design-vue';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  InputNumber,
  Select,
  SelectOption,
  DatePicker,
  Row,
  Col,
  Button,
  Space,
} from 'ant-design-vue';
import { VbenDrawer } from '@vben/common-ui';

import {
  createDrama,
  updateDrama,
  type DramaManagementApi,
  type Drama,
  type DramaFormData,
} from '#/api/drama-management';
import {
  DRAMA_STATUS_OPTIONS,
  TARGET_PLATFORM_OPTIONS,
} from '#/api/drama-management/types';

// 组件状态
const drawerVisible = ref(false);
const loading = ref(false);
const formRef = ref();
const editingData = ref<Drama | null>(null);

// 计算属性
const isEdit = computed(() => !!editingData.value);
const drawerTitle = computed(() => (isEdit.value ? '编辑短剧' : '新建短剧'));

// 表单数据
const formData = reactive<DramaFormData>({
  title: '',
  cover: '',
  tags: [],
  fundingGoal: 0,
  remainingDays: 30,
  description: '',
  status: 'draft',
  director: '',
  producer: '',
  cast: [],
  episodes: 12,
  episodeLength: 5,
  targetPlatform: [],
  expectedReleaseDate: undefined,
  expectedReturn: '',
  minInvestment: 50000,
});

// 表单验证规则
const formRules = {
  title: [{ required: true, message: '请输入短剧标题' }],
  cover: [{ required: true, message: '请输入封面图片URL' }],
  description: [{ required: true, message: '请输入短剧描述' }],
  fundingGoal: [{ required: true, message: '请输入募资目标' }],
  minInvestment: [{ required: true, message: '请输入最小投资金额' }],
  episodes: [{ required: true, message: '请输入集数' }],
  episodeLength: [{ required: true, message: '请输入单集时长' }],
  status: [{ required: true, message: '请选择状态' }],
};

// 事件定义
const emit = defineEmits<{
  success: [];
}>();

// 打开抽屉
function open() {
  drawerVisible.value = true;
}

// 设置数据
function setData(data?: Drama | null) {
  editingData.value = data || null;

  if (data) {
    // 编辑模式，填充数据
    Object.assign(formData, {
      title: data.title,
      cover: data.cover,
      tags: data.tags,
      fundingGoal: data.fundingGoal,
      remainingDays: data.remainingDays,
      description: data.description,
      status: data.status,
      director: data.director,
      producer: data.producer,
      cast: data.cast,
      episodes: data.episodes,
      episodeLength: data.episodeLength,
      targetPlatform: data.targetPlatform,
      expectedReleaseDate: data.expectedReleaseDate,
      expectedReturn: data.expectedReturn,
      minInvestment: data.minInvestment,
    });
  } else {
    // 新建模式，重置表单
    resetForm();
  }

  return { open };
}

// 关闭抽屉
function handleClose() {
  drawerVisible.value = false;
  resetForm();
}

// 重置表单
function resetForm() {
  Object.assign(formData, {
    title: '',
    cover: '',
    tags: [],
    fundingGoal: 0,
    remainingDays: 30,
    description: '',
    status: 'draft',
    director: '',
    producer: '',
    cast: [],
    episodes: 12,
    episodeLength: 5,
    targetPlatform: [],
    expectedReleaseDate: undefined,
    expectedReturn: '',
    minInvestment: 50000,
  });
  formRef.value?.resetFields();
}

// 提交表单
async function handleSubmit() {
  try {
    await formRef.value?.validate();
    loading.value = true;

    if (isEdit.value) {
      await updateDrama(editingData.value!.id, formData);
      message.success('更新成功');
    } else {
      await createDrama(formData);
      message.success('创建成功');
    }

    emit('success');
    handleClose();
  } catch (error) {
    message.error(isEdit.value ? '更新失败' : '创建失败');
  } finally {
    loading.value = false;
  }
}

// 暴露方法
defineExpose({
  open,
  setData,
});
</script>
