/**
 * PM2 生产环境配置文件
 * 用于管理剧投投后端服务进程
 */

module.exports = {
  apps: [
    {
      // 应用基本信息
      name: 'mengtu-backend',
      script: '.output/server/index.mjs',
      
      // 运行环境
      env: {
        NODE_ENV: 'development',
        PORT: 3001
      },
      
      // 生产环境配置
      env_production: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      
      // 进程管理
      instances: 'max', // 使用所有CPU核心
      exec_mode: 'cluster', // 集群模式
      
      // 自动重启配置
      autorestart: true,
      watch: false, // 生产环境不监听文件变化
      max_memory_restart: '1G', // 内存超过1G时重启
      
      // 日志配置
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // 进程重启策略
      min_uptime: '10s', // 最小运行时间
      max_restarts: 10, // 最大重启次数
      restart_delay: 4000, // 重启延迟
      
      // 健康检查
      health_check_grace_period: 3000,
      
      // 环境变量文件
      env_file: '.env.production',
      
      // 其他配置
      kill_timeout: 5000, // 强制杀死进程的超时时间
      listen_timeout: 3000, // 监听超时时间
      
      // 进程标识
      pid_file: './logs/mengtu-backend.pid',
      
      // 忽略的监听目录（当watch为true时）
      ignore_watch: [
        'node_modules',
        'logs',
        'uploads',
        '.git'
      ],
      
      // 实例变量
      instance_var: 'INSTANCE_ID',
      
      // 源码映射支持
      source_map_support: true,
      
      // 进程间通信
      disable_source_map_support: false
    }
  ],
  
  // 部署配置
  deploy: {
    production: {
      user: 'root',
      host: ['your-server-ip'], // 替换为实际服务器IP
      ref: 'origin/main',
      repo: 'https://github.com/your-repo/mengtu-backend.git', // 替换为实际仓库地址
      path: '/www/wwwroot/mengtu-backend',
      'post-deploy': 'pnpm install && pnpm build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'apt update && apt install git -y'
    }
  }
};
