module.exports = {
  apps: [{
    name: 'fundAdmin-backend',
    script: '.output/server/index.mjs',
    cwd: '/www/wwwroot/api.qinghee.com.cn',
    instances: 1,
    exec_mode: 'fork',
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    
    // 环境变量配置
    env: {
      NODE_ENV: 'production',
      PORT: 3001,
      DB_HOST: 'localhost',
      DB_PORT: 3306,
      DB_USER: 'mengtu',
      DB_PASSWORD: 'cimu2025...',
      DB_NAME: 'mengtu'
    },
    
    // 日志配置
    log_file: '/www/wwwroot/api.qinghee.com.cn/logs/combined.log',
    out_file: '/www/wwwroot/api.qinghee.com.cn/logs/out.log',
    error_file: '/www/wwwroot/api.qinghee.com.cn/logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    
    // 重启策略
    min_uptime: '10s',
    max_restarts: 10,
    restart_delay: 4000,
    
    // 环境变量文件路径
    env_file: '/www/wwwroot/api.qinghee.com.cn/.env'
  }]
};
