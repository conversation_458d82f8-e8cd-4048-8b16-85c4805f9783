import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, n as readMultipartFormData, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import { b as getFileExtension, v as validateFileType, f as formatFileSize, g as generateUniqueFilename, a as getUploadPath, d as deleteFile } from '../../../../_/file-utils.mjs';
import { u as uploadToCOS } from '../../../../_/cos-uploader.mjs';
import { writeFile } from 'fs/promises';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';
import 'fs';
import 'path';
import 'crypto';
import 'cos-nodejs-sdk-v5';

const dramaMaterial_post = defineEventHandler(async (event) => {
  var _a, _b;
  console.log("\u{1F680} [\u540E\u7AEF-\u7D20\u6750\u4E0A\u4F20] API\u8C03\u7528\u5F00\u59CB");
  console.log("\u{1F310} [\u540E\u7AEF-\u7D20\u6750\u4E0A\u4F20] \u8BF7\u6C42URL:", event.node.req.url);
  console.log("\u{1F4E1} [\u540E\u7AEF-\u7D20\u6750\u4E0A\u4F20] \u8BF7\u6C42\u65B9\u6CD5:", event.node.req.method);
  console.log("\u{1F511} [\u540E\u7AEF-\u7D20\u6750\u4E0A\u4F20] \u8BF7\u6C42\u5934:", event.node.req.headers);
  try {
    const admin = event.context.admin;
    console.log("\u{1F464} [\u540E\u7AEF-\u7D20\u6750\u4E0A\u4F20] \u7BA1\u7406\u5458\u4FE1\u606F:", admin ? { id: admin.id, username: admin.username } : "null");
    if (!admin) {
      console.error("\u274C [\u540E\u7AEF-\u7D20\u6750\u4E0A\u4F20] \u672A\u627E\u5230\u7BA1\u7406\u5458\u4FE1\u606F");
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    console.log("\u{1F510} [\u540E\u7AEF-\u7D20\u6750\u4E0A\u4F20] \u5F00\u59CB\u6743\u9650\u68C0\u67E5...");
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.CONTENT_MANAGE);
    console.log("\u{1F510} [\u540E\u7AEF-\u7D20\u6750\u4E0A\u4F20] \u6743\u9650\u68C0\u67E5\u7ED3\u679C:", hasPermission);
    if (!hasPermission) {
      console.error("\u274C [\u540E\u7AEF-\u7D20\u6750\u4E0A\u4F20] \u6743\u9650\u4E0D\u8DB3");
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u4E0A\u4F20\u77ED\u5267\u7D20\u6750"
      });
    }
    const formData = await readMultipartFormData(event);
    if (!formData || formData.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u672A\u9009\u62E9\u6587\u4EF6"
      });
    }
    const fileData = formData.find((item) => item.name === "file");
    if (!fileData || !fileData.data || !fileData.filename) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6587\u4EF6\u6570\u636E\u65E0\u6548"
      });
    }
    const typeData = formData.find((item) => item.name === "type");
    const fileType = ((_a = typeData == null ? void 0 : typeData.data) == null ? void 0 : _a.toString()) || "image";
    let allowedTypes;
    let maxSize;
    let destFolder;
    if (fileType === "image") {
      allowedTypes = [".jpg", ".jpeg", ".png", ".gif", ".webp"];
      maxSize = 10 * 1024 * 1024;
      destFolder = "drama-images";
    } else if (fileType === "video") {
      allowedTypes = [".mp4", ".webm", ".ogg", ".mov", ".avi"];
      maxSize = 100 * 1024 * 1024;
      destFolder = "drama-videos";
    } else {
      throw createError({
        statusCode: 400,
        statusMessage: "\u4E0D\u652F\u6301\u7684\u6587\u4EF6\u7C7B\u578B"
      });
    }
    const fileExt = getFileExtension(fileData.filename);
    if (!validateFileType(fileData.filename, allowedTypes)) {
      throw createError({
        statusCode: 400,
        statusMessage: `${fileType === "image" ? "\u56FE\u7247" : "\u89C6\u9891"}\u53EA\u652F\u6301\u683C\u5F0F: ${allowedTypes.join(", ")}`
      });
    }
    if (fileData.data.length > maxSize) {
      throw createError({
        statusCode: 400,
        statusMessage: `\u6587\u4EF6\u5927\u5C0F\u8D85\u8FC7\u9650\u5236\uFF0C\u6700\u5927\u5141\u8BB8 ${formatFileSize(maxSize)}`
      });
    }
    const uniqueFilename = generateUniqueFilename(fileData.filename);
    const tempPath = await getUploadPath(uniqueFilename, "temp");
    await writeFile(tempPath, fileData.data);
    try {
      const destPath = `mengtutv/${destFolder}/${uniqueFilename}`;
      const uploadResult = await uploadToCOS({
        path: tempPath,
        originalname: fileData.filename,
        mimetype: fileData.type || (fileType === "image" ? "image/jpeg" : "video/mp4")
      }, destPath);
      await deleteFile(tempPath);
      await logAuditAction({
        action: "ADMIN_UPLOAD_DRAMA_MATERIAL",
        description: `\u7BA1\u7406\u5458\u4E0A\u4F20\u77ED\u5267\u7D20\u6750: ${fileData.filename}`,
        userId: admin.id,
        username: admin.username,
        ip: getClientIP(event),
        userAgent: getHeader(event, "user-agent") || "",
        details: {
          originalName: fileData.filename,
          fileName: uniqueFilename,
          fileSize: fileData.data.length,
          fileType,
          url: uploadResult.url
        }
      });
      logger.info("\u7BA1\u7406\u5458\u4E0A\u4F20\u77ED\u5267\u7D20\u6750\u6210\u529F", {
        adminId: admin.id,
        adminUsername: admin.username,
        originalName: fileData.filename,
        fileType,
        url: uploadResult.url
      });
      return {
        success: true,
        message: "\u77ED\u5267\u7D20\u6750\u4E0A\u4F20\u6210\u529F",
        data: {
          url: uploadResult.url,
          filename: uniqueFilename,
          originalName: fileData.filename,
          size: fileData.data.length,
          type: fileType
        }
      };
    } catch (uploadError) {
      await deleteFile(tempPath);
      logger.error("\u77ED\u5267\u7D20\u6750\u4E0A\u4F20\u5230COS\u5931\u8D25", {
        error: uploadError.message,
        adminId: admin.id,
        filename: fileData.filename
      });
      throw createError({
        statusCode: 500,
        statusMessage: `\u77ED\u5267\u7D20\u6750\u4E0A\u4F20\u5931\u8D25: ${uploadError.message}`
      });
    }
  } catch (error) {
    logger.error("\u7BA1\u7406\u5458\u4E0A\u4F20\u77ED\u5267\u7D20\u6750\u5931\u8D25", {
      error: error.message,
      adminId: (_b = event.context.admin) == null ? void 0 : _b.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { dramaMaterial_post as default };
//# sourceMappingURL=drama-material.post.mjs.map
