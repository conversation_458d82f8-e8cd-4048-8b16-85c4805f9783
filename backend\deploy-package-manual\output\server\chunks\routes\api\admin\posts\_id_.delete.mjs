import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, j as getRouter<PERSON>aram, f as createError, q as query } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__delete = defineEventHandler(async (event) => {
  try {
    const admin = event.context.admin || { id: 1, username: "test" };
    const postId = getRouterParam(event, "id");
    if (!postId || isNaN(Number(postId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u63A8\u6587ID"
      });
    }
    const existingPost = await query(
      "SELECT id, title FROM posts WHERE id = ?",
      [postId]
    );
    if (existingPost.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u63A8\u6587\u4E0D\u5B58\u5728"
      });
    }
    await query("START TRANSACTION");
    try {
      const associatedTags = await query(
        "SELECT tag_id FROM post_tags WHERE post_id = ?",
        [postId]
      );
      for (const tag of associatedTags) {
        await query(
          "UPDATE post_tag_definitions SET usage_count = GREATEST(usage_count - 1, 0) WHERE id = ?",
          [tag.tag_id]
        );
      }
      await query("DELETE FROM post_tags WHERE post_id = ?", [postId]);
      await query("DELETE FROM posts WHERE id = ?", [postId]);
      await query("COMMIT");
      return {
        success: true,
        message: "\u63A8\u6587\u5220\u9664\u6210\u529F",
        data: {
          id: postId
        }
      };
    } catch (error) {
      await query("ROLLBACK");
      throw error;
    }
  } catch (error) {
    console.error("\u5220\u9664\u63A8\u6587\u5931\u8D25:", error);
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.message || "\u5220\u9664\u63A8\u6587\u5931\u8D25"
    });
  }
});

export { _id__delete as default };
//# sourceMappingURL=_id_.delete.mjs.map
