/**
 * 测试短信设置接口
 * POST /api/admin/settings/sms/test
 */
export default defineEventHandler(async (event) => {
  try {
    // 从中间件获取管理员信息
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: '未授权访问'
      });
    }

    // 检查权限
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: '权限不足，无法测试短信设置'
      });
    }

    // 获取请求体
    const body = await readBody(event);
    const { phone, message } = body;

    // 验证必要字段
    if (!phone || !message) {
      throw createError({
        statusCode: 400,
        statusMessage: '请提供手机号和短信内容'
      });
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(phone)) {
      throw createError({
        statusCode: 400,
        statusMessage: '请提供有效的手机号'
      });
    }

    // 获取短信设置
    const result = await query(
      'SELECT setting_value FROM system_settings WHERE setting_key = ?',
      ['sms_settings']
    );

    if (result.length === 0 || !result[0].setting_value) {
      throw createError({
        statusCode: 400,
        statusMessage: '短信设置未配置，请先配置短信设置'
      });
    }

    let smsSettings;
    try {
      smsSettings = JSON.parse(result[0].setting_value);
    } catch (error) {
      throw createError({
        statusCode: 400,
        statusMessage: '短信设置格式错误'
      });
    }

    // 验证短信设置完整性
    if (!smsSettings.provider || !smsSettings.accessKey || !smsSettings.accessSecret || !smsSettings.signName) {
      throw createError({
        statusCode: 400,
        statusMessage: '短信设置不完整，请检查服务商、密钥和签名'
      });
    }

    // 这里应该实际发送短信，但为了演示，我们只是模拟成功
    // 在实际项目中，你需要使用对应服务商的SDK
    
    // 模拟发送延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 记录审计日志
    await logAuditAction({
      action: 'ADMIN_TEST_SMS_SETTINGS',
      description: '管理员测试短信设置',
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, 'user-agent') || '',
      details: { phone, messageLength: message.length }
    });

    return {
      success: true,
      message: '测试短信发送成功'
    };

  } catch (error: any) {
    logger.error('测试短信设置失败', {
      error: error.message,
      adminId: event.context.admin?.id,
      ip: getClientIP(event)
    });

    // 如果是已知错误，直接抛出
    if (error.statusCode) {
      throw error;
    }

    // 未知错误
    throw createError({
      statusCode: 500,
      statusMessage: '服务器内部错误'
    });
  }
});
