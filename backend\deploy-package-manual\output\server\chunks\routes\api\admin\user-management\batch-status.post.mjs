import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, r as readBody, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const batchStatus_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.USER_EDIT);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u4FEE\u6539\u7528\u6237\u72B6\u6001"
      });
    }
    const body = await readBody(event);
    const { ids, status } = body;
    if (!Array.isArray(ids) || ids.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u8BF7\u63D0\u4F9B\u8981\u4FEE\u6539\u7684\u7528\u6237ID\u5217\u8868"
      });
    }
    if (status !== "active" && status !== "inactive") {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u72B6\u6001\u503C\uFF0C\u53EA\u80FD\u662F active \u6216 inactive"
      });
    }
    const validIds = ids.filter((id) => Number.isInteger(id) && id > 0);
    if (validIds.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u7528\u6237ID\u5217\u8868"
      });
    }
    const placeholders = validIds.map(() => "?").join(",");
    const users = await query(
      `SELECT id, username, status FROM users WHERE id IN (${placeholders})`,
      validIds
    );
    if (users.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u672A\u627E\u5230\u8981\u4FEE\u6539\u7684\u7528\u6237"
      });
    }
    const statusValue = status === "active" ? 1 : 0;
    await query(
      `UPDATE users SET status = ?, updated_at = NOW() WHERE id IN (${placeholders})`,
      [statusValue, ...validIds]
    );
    await logAuditAction({
      action: "ADMIN_BATCH_UPDATE_USER_STATUS",
      description: `\u7BA1\u7406\u5458\u6279\u91CF\u4FEE\u6539\u7528\u6237\u72B6\u6001: ${users.map((u) => u.username).join(", ")} -> ${status}`,
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: {
        targetUserIds: validIds,
        targetUsers: users.map((u) => ({
          id: u.id,
          username: u.username,
          oldStatus: u.status === 1 ? "active" : "inactive"
        })),
        newStatus: status
      }
    });
    logger.info("\u7BA1\u7406\u5458\u6279\u91CF\u4FEE\u6539\u7528\u6237\u72B6\u6001\u6210\u529F", {
      adminId: admin.id,
      targetUserIds: validIds,
      updatedCount: users.length,
      newStatus: status,
      ip: getClientIP(event)
    });
    return {
      success: true,
      message: `\u6210\u529F\u4FEE\u6539 ${users.length} \u4E2A\u7528\u6237\u72B6\u6001\u4E3A${status === "active" ? "\u542F\u7528" : "\u7981\u7528"}`,
      data: {
        updatedCount: users.length,
        updatedUsers: users.map((u) => u.username),
        newStatus: status
      }
    };
  } catch (error) {
    logger.error("\u6279\u91CF\u4FEE\u6539\u7528\u6237\u72B6\u6001\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    throw error;
  }
});

export { batchStatus_post as default };
//# sourceMappingURL=batch-status.post.mjs.map
