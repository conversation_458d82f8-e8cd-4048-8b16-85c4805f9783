import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, f as createError, r as readBody, q as query, l as logger, h as logAuditAction, i as getHeader, e as getClientIP } from '../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const security_post = defineEventHandler(async (event) => {
  var _a;
  try {
    const admin = event.context.admin;
    if (!admin) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(admin.id, PERMISSIONS.BASIC_ACCESS);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u4FEE\u6539\u5B89\u5168\u8BBE\u7F6E"
      });
    }
    const body = await readBody(event);
    const {
      jwtSecret,
      jwtExpiresIn,
      jwtAdminExpiresIn,
      accessTokenSecret,
      refreshTokenSecret,
      passwordMinLength,
      enableCaptcha,
      maxLoginAttempts,
      lockTime
    } = body;
    if (!jwtExpiresIn || !jwtAdminExpiresIn || !passwordMinLength || !maxLoginAttempts || !lockTime) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u8BF7\u63D0\u4F9B\u5B8C\u6574\u7684\u5B89\u5168\u8BBE\u7F6E\u53C2\u6570"
      });
    }
    const timeRegex = /^(\d+)([hdms])$/;
    if (!timeRegex.test(jwtExpiresIn) || !timeRegex.test(jwtAdminExpiresIn)) {
      throw createError({
        statusCode: 400,
        statusMessage: "JWT\u8FC7\u671F\u65F6\u95F4\u683C\u5F0F\u9519\u8BEF\uFF0C\u8BF7\u4F7F\u7528\u5982\uFF1A1h, 24h, 7d, 30m \u7B49\u683C\u5F0F"
      });
    }
    if (passwordMinLength < 6 || passwordMinLength > 20) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u5BC6\u7801\u6700\u5C0F\u957F\u5EA6\u5FC5\u987B\u57286-20\u4F4D\u4E4B\u95F4"
      });
    }
    if (maxLoginAttempts < 3 || maxLoginAttempts > 10) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u6700\u5927\u767B\u5F55\u5C1D\u8BD5\u6B21\u6570\u5FC5\u987B\u57283-10\u6B21\u4E4B\u95F4"
      });
    }
    if (lockTime < 5 || lockTime > 1440) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u8D26\u6237\u9501\u5B9A\u65F6\u95F4\u5FC5\u987B\u57285-1440\u5206\u949F\u4E4B\u95F4"
      });
    }
    let existingSettings = {};
    try {
      const result = await query(
        "SELECT setting_value FROM system_settings WHERE setting_key = ?",
        ["security_settings"]
      );
      if (result.length > 0 && result[0].setting_value) {
        existingSettings = JSON.parse(result[0].setting_value);
      }
    } catch (error) {
      logger.warn("\u83B7\u53D6\u73B0\u6709\u5B89\u5168\u8BBE\u7F6E\u5931\u8D25", { error: error.message });
    }
    const securitySettings = {
      jwtSecret: jwtSecret && jwtSecret !== "******" ? jwtSecret : existingSettings.jwtSecret || "default-secret-key",
      jwtExpiresIn: jwtExpiresIn || existingSettings.jwtExpiresIn || "24h",
      jwtAdminExpiresIn: jwtAdminExpiresIn || existingSettings.jwtAdminExpiresIn || "12h",
      accessTokenSecret: accessTokenSecret && accessTokenSecret !== "******" ? accessTokenSecret : existingSettings.accessTokenSecret || "default-access-token-secret",
      refreshTokenSecret: refreshTokenSecret && refreshTokenSecret !== "******" ? refreshTokenSecret : existingSettings.refreshTokenSecret || "default-refresh-secret",
      passwordMinLength: Number(passwordMinLength),
      enableCaptcha: Boolean(enableCaptcha),
      maxLoginAttempts: Number(maxLoginAttempts),
      lockTime: Number(lockTime)
    };
    const settingsJson = JSON.stringify(securitySettings);
    const existingSettingsResult = await query(
      "SELECT id FROM system_settings WHERE setting_key = ?",
      ["security_settings"]
    );
    if (existingSettingsResult.length > 0) {
      await query(
        "UPDATE system_settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?",
        [settingsJson, "security_settings"]
      );
    } else {
      await query(
        "INSERT INTO system_settings (setting_key, setting_value, created_at, updated_at) VALUES (?, ?, NOW(), NOW())",
        ["security_settings", settingsJson]
      );
    }
    await logAuditAction({
      action: "ADMIN_UPDATE_SECURITY_SETTINGS",
      description: "\u7BA1\u7406\u5458\u66F4\u65B0\u5B89\u5168\u8BBE\u7F6E",
      userId: admin.id,
      username: admin.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      details: { jwtExpiresIn, jwtAdminExpiresIn, passwordMinLength, enableCaptcha, maxLoginAttempts, lockTime }
    });
    return {
      success: true,
      message: "\u5B89\u5168\u8BBE\u7F6E\u66F4\u65B0\u6210\u529F"
    };
  } catch (error) {
    logger.error("\u66F4\u65B0\u5B89\u5168\u8BBE\u7F6E\u5931\u8D25", {
      error: error.message,
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { security_post as default };
//# sourceMappingURL=security.post.mjs.map
