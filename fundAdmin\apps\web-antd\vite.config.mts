import { defineConfig } from '@vben/vite-config';

export default defineConfig(async () => {
  return {
    application: {},
    vite: {
      // 移除代理配置，直接使用绝对URL访问后端API
      server: {
        // 如果需要代理，可以配置如下：
        // proxy: {
        //   '/api': {
        //     changeOrigin: true,
        //     target: 'http://localhost:3001',
        //     ws: true,
        //   },
        // },
      },
      build: {
        rollupOptions: {
          external: ['@tanstack/vue-query'],
        },
      },
    },
  };
});
