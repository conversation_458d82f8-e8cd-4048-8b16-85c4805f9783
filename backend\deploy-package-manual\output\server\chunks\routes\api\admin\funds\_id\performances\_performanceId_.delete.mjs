import { c as defineE<PERSON><PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, q as query, h as logAuditAction, i as getHeader, e as getClientIP, l as logger } from '../../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _performanceId__delete = defineEventHandler(async (event) => {
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const fundId = getRouterParam(event, "id");
    const performanceId = getRouterParam(event, "performanceId");
    if (!fundId || isNaN(Number(fundId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u57FA\u91D1ID"
      });
    }
    if (!performanceId || isNaN(Number(performanceId))) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u65E0\u6548\u7684\u4E1A\u7EE9ID"
      });
    }
    const existingPerformance = await query(
      "SELECT * FROM fund_performances WHERE id = ? AND fund_id = ?",
      [performanceId, fundId]
    );
    if (existingPerformance.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u4E1A\u7EE9\u8BB0\u5F55\u4E0D\u5B58\u5728"
      });
    }
    await query(
      "DELETE FROM fund_performances WHERE id = ? AND fund_id = ?",
      [performanceId, fundId]
    );
    await logAuditAction({
      action: "ADMIN_DELETE_FUND_PERFORMANCE",
      description: `\u7BA1\u7406\u5458\u5220\u9664\u57FA\u91D1\u5386\u53F2\u4E1A\u7EE9: \u57FA\u91D1ID=${fundId}, \u4E1A\u7EE9ID=${performanceId}`,
      userId: adminPayload.id,
      username: adminPayload.username,
      ip: getClientIP(event),
      userAgent: getHeader(event, "user-agent") || "",
      metadata: {
        fundId: Number(fundId),
        performanceId: Number(performanceId),
        deletedYear: existingPerformance[0].year,
        deletedReturn: existingPerformance[0].return,
        deletedAverage: existingPerformance[0].average
      }
    });
    logger.info("\u57FA\u91D1\u5386\u53F2\u4E1A\u7EE9\u5220\u9664\u6210\u529F", {
      adminId: adminPayload.id,
      adminUsername: adminPayload.username,
      fundId: Number(fundId),
      performanceId: Number(performanceId),
      deletedYear: existingPerformance[0].year
    });
    return {
      success: true,
      message: "\u57FA\u91D1\u5386\u53F2\u4E1A\u7EE9\u5220\u9664\u6210\u529F",
      data: {
        id: Number(performanceId),
        fundId: Number(fundId)
      }
    };
  } catch (error) {
    logger.error("\u5220\u9664\u57FA\u91D1\u5386\u53F2\u4E1A\u7EE9\u5931\u8D25", {
      error: error.message,
      fundId: getRouterParam(event, "id"),
      performanceId: getRouterParam(event, "performanceId")
    });
    throw createError({
      statusCode: error.statusCode || 500,
      statusMessage: error.statusMessage || "\u5220\u9664\u57FA\u91D1\u5386\u53F2\u4E1A\u7EE9\u5931\u8D25"
    });
  }
});

export { _performanceId__delete as default };
//# sourceMappingURL=_performanceId_.delete.mjs.map
