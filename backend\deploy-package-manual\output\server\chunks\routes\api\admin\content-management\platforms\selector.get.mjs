import { c as defineEvent<PERSON><PERSON><PERSON>, q as query, f as createError } from '../../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const selector_get = defineEventHandler(async (event) => {
  try {
    const platforms = await query(`
      SELECT id, platform_name, platform_logo_url, platform_type
      FROM drama_platforms
      WHERE is_active = 1
      ORDER BY platform_name ASC
    `);
    return {
      success: true,
      message: "\u83B7\u53D6\u5E73\u53F0\u9009\u62E9\u5668\u6570\u636E\u6210\u529F",
      data: {
        result: platforms
      }
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u5E73\u53F0\u9009\u62E9\u5668\u6570\u636E\u5931\u8D25:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "\u83B7\u53D6\u5E73\u53F0\u9009\u62E9\u5668\u6570\u636E\u5931\u8D25"
    });
  }
});

export { selector_get as default };
//# sourceMappingURL=selector.get.mjs.map
