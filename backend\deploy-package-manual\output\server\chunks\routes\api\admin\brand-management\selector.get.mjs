import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, q as query, f as createError } from '../../../../_/nitro.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const selector_get = defineEventHandler(async (event) => {
  try {
    const brands = await query(`
      SELECT id, brand_name, company_name
      FROM brands
      WHERE status = 'active'
      ORDER BY company_name ASC
    `);
    const formattedBrands = brands.map((brand) => ({
      id: brand.id,
      label: brand.company_name,
      value: brand.id,
      brandName: brand.brand_name,
      companyName: brand.company_name
    }));
    return {
      success: true,
      message: "\u83B7\u53D6\u5382\u724C\u9009\u62E9\u5668\u6570\u636E\u6210\u529F",
      data: {
        result: formattedBrands
      }
    };
  } catch (error) {
    console.error("\u83B7\u53D6\u5382\u724C\u9009\u62E9\u5668\u6570\u636E\u5931\u8D25:", error);
    throw createError({
      statusCode: 500,
      statusMessage: "\u83B7\u53D6\u5382\u724C\u9009\u62E9\u5668\u6570\u636E\u5931\u8D25"
    });
  }
});

export { selector_get as default };
//# sourceMappingURL=selector.get.mjs.map
