#!/bin/bash

# 剧投投后端服务部署脚本
# 使用方法: ./deploy.sh [环境] [操作]
# 环境: dev | prod
# 操作: build | start | stop | restart | logs

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
PROJECT_NAME="mengtu-backend"
CONTAINER_NAME="mengtu-backend-container"
IMAGE_NAME="mengtu-backend"
PORT=3001

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    log_info "Docker 已安装"
}

# 检查环境变量文件
check_env_file() {
    local env=$1
    local env_file=".env"
    
    if [ "$env" = "prod" ]; then
        env_file=".env.production"
    fi
    
    if [ ! -f "$env_file" ]; then
        log_error "环境变量文件 $env_file 不存在"
        exit 1
    fi
    
    log_info "使用环境变量文件: $env_file"
}

# 构建Docker镜像
build_image() {
    local env=$1
    log_info "开始构建 Docker 镜像..."
    
    # 复制对应的环境变量文件
    if [ "$env" = "prod" ]; then
        cp .env.production .env
        log_info "使用生产环境配置"
    else
        log_info "使用开发环境配置"
    fi
    
    docker build -t $IMAGE_NAME:latest .
    
    if [ $? -eq 0 ]; then
        log_success "Docker 镜像构建成功"
    else
        log_error "Docker 镜像构建失败"
        exit 1
    fi
}

# 停止并删除现有容器
stop_container() {
    log_info "停止现有容器..."
    
    if docker ps -q -f name=$CONTAINER_NAME | grep -q .; then
        docker stop $CONTAINER_NAME
        log_info "容器已停止"
    fi
    
    if docker ps -aq -f name=$CONTAINER_NAME | grep -q .; then
        docker rm $CONTAINER_NAME
        log_info "容器已删除"
    fi
}

# 启动容器
start_container() {
    local env=$1
    log_info "启动新容器..."
    
    # 创建必要的目录
    mkdir -p uploads logs
    
    # 启动容器
    docker run -d \
        --name $CONTAINER_NAME \
        -p $PORT:$PORT \
        -v $(pwd)/uploads:/app/uploads \
        -v $(pwd)/logs:/app/logs \
        --restart unless-stopped \
        $IMAGE_NAME:latest
    
    if [ $? -eq 0 ]; then
        log_success "容器启动成功"
        log_info "服务运行在端口: $PORT"
        log_info "容器名称: $CONTAINER_NAME"
    else
        log_error "容器启动失败"
        exit 1
    fi
}

# 查看日志
show_logs() {
    log_info "显示容器日志..."
    docker logs -f $CONTAINER_NAME
}

# 检查服务状态
check_status() {
    log_info "检查服务状态..."
    
    if docker ps -q -f name=$CONTAINER_NAME | grep -q .; then
        log_success "服务正在运行"
        docker ps -f name=$CONTAINER_NAME
        
        # 检查健康状态
        log_info "检查API健康状态..."
        sleep 3
        if curl -f http://localhost:$PORT/api/health > /dev/null 2>&1; then
            log_success "API 健康检查通过"
        else
            log_warning "API 健康检查失败，请检查日志"
        fi
    else
        log_error "服务未运行"
    fi
}

# 显示帮助信息
show_help() {
    echo "剧投投后端服务部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [环境] [操作]"
    echo ""
    echo "环境:"
    echo "  dev   - 开发环境"
    echo "  prod  - 生产环境"
    echo ""
    echo "操作:"
    echo "  build   - 构建Docker镜像"
    echo "  start   - 启动服务"
    echo "  stop    - 停止服务"
    echo "  restart - 重启服务"
    echo "  logs    - 查看日志"
    echo "  status  - 检查状态"
    echo ""
    echo "示例:"
    echo "  $0 prod build   # 构建生产环境镜像"
    echo "  $0 prod start   # 启动生产环境服务"
    echo "  $0 prod restart # 重启生产环境服务"
    echo "  $0 prod logs    # 查看生产环境日志"
}

# 主函数
main() {
    local env=${1:-dev}
    local action=${2:-help}
    
    # 检查参数
    if [ "$env" != "dev" ] && [ "$env" != "prod" ]; then
        log_error "无效的环境参数: $env"
        show_help
        exit 1
    fi
    
    # 检查Docker
    check_docker
    
    # 检查环境变量文件
    check_env_file $env
    
    case $action in
        build)
            build_image $env
            ;;
        start)
            build_image $env
            stop_container
            start_container $env
            check_status
            ;;
        stop)
            stop_container
            ;;
        restart)
            stop_container
            build_image $env
            start_container $env
            check_status
            ;;
        logs)
            show_logs
            ;;
        status)
            check_status
            ;;
        help|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
