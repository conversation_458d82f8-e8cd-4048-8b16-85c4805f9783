import { c as define<PERSON><PERSON><PERSON><PERSON><PERSON>, v as verifyAdminAccessToken, f as createError, j as getRouterParam, q as query, l as logger, e as getClientIP } from '../../../../../_/nitro.mjs';
import { c as checkAdminPermission, P as PERMISSIONS } from '../../../../../_/permission.mjs';
import 'node:http';
import 'node:https';
import 'node:events';
import 'node:buffer';
import 'jsonwebtoken';
import 'bcryptjs';
import 'mysql2/promise';
import 'node:fs';
import 'node:url';
import 'node:path';
import 'node:crypto';

const _id__get = defineEventHandler(async (event) => {
  var _a;
  try {
    const adminPayload = verifyAdminAccessToken(event);
    if (!adminPayload) {
      throw createError({
        statusCode: 401,
        statusMessage: "\u672A\u6388\u6743\u8BBF\u95EE"
      });
    }
    const hasPermission = await checkAdminPermission(adminPayload.id, PERMISSIONS.SYSTEM_MENU_LIST);
    if (!hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: "\u6743\u9650\u4E0D\u8DB3\uFF0C\u65E0\u6CD5\u67E5\u770B\u83DC\u5355\u8BE6\u60C5"
      });
    }
    const menuId = getRouterParam(event, "id");
    if (!menuId) {
      throw createError({
        statusCode: 400,
        statusMessage: "\u83DC\u5355ID\u4E0D\u80FD\u4E3A\u7A7A"
      });
    }
    const menuResult = await query(`
      SELECT
        id,
        pid,
        name,
        path,
        component,
        type,
        status,
        auth_code as authCode,
        icon,
        meta,
        sort_order as sortOrder,
        created_at,
        updated_at
      FROM admin_menus
      WHERE id = ?
    `, [menuId]);
    if (menuResult.length === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: "\u83DC\u5355\u4E0D\u5B58\u5728"
      });
    }
    const menu = menuResult[0];
    if (menu.meta) {
      try {
        menu.meta = JSON.parse(menu.meta);
      } catch (error) {
        console.warn("\u83DC\u5355meta\u5B57\u6BB5\u89E3\u6790\u5931\u8D25:", error);
        menu.meta = {};
      }
    }
    if (menu.pid) {
      const parentResult = await query(`
        SELECT id, name FROM admin_menus WHERE id = ?
      `, [menu.pid]);
      if (parentResult.length > 0) {
        menu.parentMenu = parentResult[0];
      }
    }
    const childrenCount = await query(`
      SELECT COUNT(*) as count FROM admin_menus WHERE pid = ?
    `, [menuId]);
    menu.childrenCount = childrenCount[0].count;
    logger.info("\u83B7\u53D6\u83DC\u5355\u8BE6\u60C5\u6210\u529F", {
      adminId: adminPayload.id,
      menuId,
      menuName: menu.name,
      ip: getClientIP(event)
    });
    return {
      success: true,
      data: menu
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u83DC\u5355\u8BE6\u60C5\u5931\u8D25", {
      error: error.message,
      menuId: getRouterParam(event, "id"),
      adminId: (_a = event.context.admin) == null ? void 0 : _a.id,
      ip: getClientIP(event)
    });
    if (error.statusCode) {
      throw error;
    }
    throw createError({
      statusCode: 500,
      statusMessage: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF"
    });
  }
});

export { _id__get as default };
//# sourceMappingURL=_id_.get.mjs.map
