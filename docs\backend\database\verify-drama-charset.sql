-- 验证短剧相关表的字符编码设置
-- 确保所有drama表都正确使用utf8mb4字符集

-- 设置连接字符集
SET NAMES utf8mb4;
SET CHARACTER_SET_CLIENT = utf8mb4;
SET CHARACTER_SET_CONNECTION = utf8mb4;
SET CHARACTER_SET_RESULTS = utf8mb4;

-- ============================================================================
-- 1. 检查表级别的字符集设置
-- ============================================================================

SELECT 
    '=== 表级别字符集检查 ===' as check_type,
    '' as table_name,
    '' as table_collation,
    '' as status;

SELECT 
    '表字符集检查' as check_type,
    TABLE_NAME as table_name, 
    TABLE_COLLATION as table_collation,
    CASE 
        WHEN TABLE_COLLATION = 'utf8mb4_unicode_ci' THEN '✓ 正确'
        ELSE '✗ 需要修复'
    END as status
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'mengtu' AND TABLE_NAME LIKE 'drama_%' 
ORDER BY TABLE_NAME;

-- ============================================================================
-- 2. 检查字段级别的字符集设置
-- ============================================================================

SELECT 
    '=== 字段级别字符集检查 ===' as check_type,
    '' as table_name,
    '' as column_name,
    '' as character_set_name,
    '' as collation_name,
    '' as status;

SELECT 
    '字段字符集检查' as check_type,
    TABLE_NAME as table_name,
    COLUMN_NAME as column_name,
    CHARACTER_SET_NAME as character_set_name,
    COLLATION_NAME as collation_name,
    CASE 
        WHEN CHARACTER_SET_NAME = 'utf8mb4' AND COLLATION_NAME = 'utf8mb4_unicode_ci' THEN '✓ 正确'
        WHEN CHARACTER_SET_NAME = 'utf8mb4' THEN '⚠ 字符集正确但排序规则需要检查'
        ELSE '✗ 需要修复'
    END as status
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'mengtu' 
  AND TABLE_NAME LIKE 'drama_%' 
  AND DATA_TYPE IN ('varchar', 'text', 'longtext', 'mediumtext', 'tinytext') 
ORDER BY TABLE_NAME, COLUMN_NAME;

-- ============================================================================
-- 3. 测试中文数据存储和显示
-- ============================================================================

SELECT 
    '=== 中文数据测试 ===' as test_type,
    '' as test_content;

-- 测试中文字符显示
SELECT 
    '中文显示测试' as test_type,
    '测试中文字符：你好世界！🎬📺' as test_content;

-- 测试实际数据中的中文
SELECT 
    '实际数据测试' as test_type,
    CONCAT('短剧标题：', title) as test_content
FROM drama_series 
WHERE title IS NOT NULL 
LIMIT 3;

SELECT 
    '制作团队数据测试' as test_type,
    CONCAT('导演：', director, '，制片人：', producer) as test_content
FROM drama_production_team 
WHERE director IS NOT NULL OR producer IS NOT NULL
LIMIT 3;

-- ============================================================================
-- 4. 检查可能的乱码数据
-- ============================================================================

SELECT 
    '=== 乱码数据检查 ===' as check_type,
    '' as table_name,
    '' as issue_description;

-- 检查是否有问号或其他乱码字符
SELECT 
    '乱码检查' as check_type,
    'drama_series' as table_name,
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('发现 ', COUNT(*), ' 条可能的乱码数据')
        ELSE '未发现乱码数据'
    END as issue_description
FROM drama_series 
WHERE title LIKE '%?%' OR description LIKE '%?%' OR cast LIKE '%?%';

SELECT 
    '乱码检查' as check_type,
    'drama_production_team' as table_name,
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('发现 ', COUNT(*), ' 条可能的乱码数据')
        ELSE '未发现乱码数据'
    END as issue_description
FROM drama_production_team 
WHERE director LIKE '%?%' OR producer LIKE '%?%' OR production_company LIKE '%?%';

-- ============================================================================
-- 5. 数据统计和完整性检查
-- ============================================================================

SELECT 
    '=== 数据完整性检查 ===' as check_type,
    '' as table_name,
    0 as record_count;

SELECT 
    '数据统计' as check_type,
    TABLE_NAME as table_name,
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES t2 WHERE t2.TABLE_NAME = t1.TABLE_NAME AND t2.TABLE_SCHEMA = 'mengtu') as record_count
FROM INFORMATION_SCHEMA.TABLES t1
WHERE t1.TABLE_SCHEMA = 'mengtu' AND t1.TABLE_NAME LIKE 'drama_%'
ORDER BY t1.TABLE_NAME;

-- 实际记录数统计
SELECT 'drama_series' as table_name, COUNT(*) as record_count FROM drama_series
UNION ALL
SELECT 'drama_production_team' as table_name, COUNT(*) as record_count FROM drama_production_team
UNION ALL
SELECT 'drama_production_schedule' as table_name, COUNT(*) as record_count FROM drama_production_schedule
UNION ALL
SELECT 'drama_funding_info' as table_name, COUNT(*) as record_count FROM drama_funding_info
UNION ALL
SELECT 'drama_additional_info' as table_name, COUNT(*) as record_count FROM drama_additional_info
UNION ALL
SELECT 'drama_investment_tiers' as table_name, COUNT(*) as record_count FROM drama_investment_tiers;

-- ============================================================================
-- 6. 修复建议
-- ============================================================================

SELECT 
    '=== 修复建议 ===' as suggestion_type,
    '' as suggestion;

SELECT 
    '连接建议' as suggestion_type,
    '请确保客户端连接时使用 --default-character-set=utf8mb4 参数' as suggestion
UNION ALL
SELECT 
    '应用建议' as suggestion_type,
    '应用程序连接数据库时应设置 charset=utf8mb4' as suggestion
UNION ALL
SELECT 
    '配置建议' as suggestion_type,
    '建议在 my.cnf 中设置 default-character-set=utf8mb4' as suggestion;
