import path from 'path';
import { g as generateUniqueFilename } from './file-utils.mjs';
import { l as logger, q as query } from './nitro.mjs';

async function getCOSConfigFromDB() {
  try {
    const result = await query(
      "SELECT setting_value FROM system_settings WHERE setting_key = ?",
      ["cos_settings"]
    );
    if (!result || result.length === 0) {
      logger.warn("\u672A\u627E\u5230\u5BF9\u8C61\u5B58\u50A8\u914D\u7F6E");
      return null;
    }
    const settings = JSON.parse(result[0].setting_value);
    if (!settings.isEnabled) {
      logger.warn("\u5BF9\u8C61\u5B58\u50A8\u529F\u80FD\u672A\u542F\u7528");
      return null;
    }
    if (!settings.provider || !settings.bucket || !settings.region) {
      logger.error("\u5BF9\u8C61\u5B58\u50A8\u914D\u7F6E\u4E0D\u5B8C\u6574", { settings });
      return null;
    }
    return {
      provider: settings.provider,
      secretId: settings.secretId || "",
      secretKey: settings.secretKey || "",
      region: settings.region,
      bucket: settings.bucket,
      domain: settings.domain || "",
      directory: settings.directory || "uploads",
      isEnabled: settings.isEnabled
    };
  } catch (error) {
    logger.error("\u83B7\u53D6\u5BF9\u8C61\u5B58\u50A8\u914D\u7F6E\u5931\u8D25", { error: error.message });
    return null;
  }
}
async function uploadToTencentCOSWithSDK(cosConfig, fileInfo, objectKey) {
  try {
    const COS = (await import('cos-nodejs-sdk-v5')).default;
    const cos = new COS({
      SecretId: cosConfig.secretId,
      SecretKey: cosConfig.secretKey,
      FileParallelLimit: 3,
      ChunkParallelLimit: 8,
      ChunkSize: 1024 * 1024 * 8
      // 8MB
    });
    const { readFile } = await import('fs/promises');
    const fileContent = await readFile(fileInfo.path);
    logger.info("\u5F00\u59CB\u4E0A\u4F20\u6587\u4EF6\u5230\u817E\u8BAF\u4E91COS", {
      originalName: fileInfo.originalname,
      objectKey,
      bucket: cosConfig.bucket,
      region: cosConfig.region
    });
    const result = await cos.putObject({
      Bucket: cosConfig.bucket,
      Region: cosConfig.region,
      Key: objectKey,
      Body: fileContent,
      ContentType: fileInfo.mimetype,
      onProgress: (progressData) => {
        logger.debug("\u4E0A\u4F20\u8FDB\u5EA6", {
          percent: Math.round(progressData.percent * 100),
          speed: progressData.speed
        });
      }
    });
    const defaultUrl = `https://${cosConfig.bucket}.cos.${cosConfig.region}.myqcloud.com/${objectKey}`;
    const accessUrl = cosConfig.domain ? cosConfig.domain.startsWith("http") ? `${cosConfig.domain}/${objectKey}` : `https://${cosConfig.domain}/${objectKey}` : defaultUrl;
    logger.info("\u817E\u8BAF\u4E91COS\u4E0A\u4F20\u6210\u529F", {
      originalName: fileInfo.originalname,
      objectKey,
      url: accessUrl,
      etag: result.ETag
    });
    return {
      url: accessUrl,
      key: objectKey,
      provider: "tencent"
    };
  } catch (error) {
    logger.error("\u817E\u8BAF\u4E91COS\u4E0A\u4F20\u5931\u8D25", {
      error: error.message,
      objectKey
    });
    throw new Error(`\u817E\u8BAF\u4E91COS\u4E0A\u4F20\u5931\u8D25: ${error.message}`);
  }
}
async function uploadToObjectStorage(fileInfo, destPath) {
  const cosConfig = await getCOSConfigFromDB();
  if (!cosConfig) {
    throw new Error("\u5BF9\u8C61\u5B58\u50A8\u672A\u914D\u7F6E\u6216\u672A\u542F\u7528");
  }
  let objectKey;
  const baseDirectory = cosConfig.directory || "uploads";
  if (destPath) {
    const normalizedDestPath = destPath.replace(/\\/g, "/");
    objectKey = path.posix.join(baseDirectory, normalizedDestPath);
  } else {
    const filename = generateUniqueFilename(fileInfo.originalname);
    objectKey = path.posix.join(baseDirectory, filename);
  }
  logger.info("\u5F00\u59CB\u4E0A\u4F20\u6587\u4EF6\u5230\u5BF9\u8C61\u5B58\u50A8", {
    provider: cosConfig.provider,
    originalName: fileInfo.originalname,
    objectKey,
    bucket: cosConfig.bucket,
    baseDirectory,
    providedDestPath: destPath,
    finalObjectKey: objectKey,
    cosConfigDirectory: cosConfig.directory,
    fullCosConfig: cosConfig
  });
  switch (cosConfig.provider) {
    case "tencent":
      return await uploadToTencentCOSWithSDK(cosConfig, fileInfo, objectKey);
    case "aliyun":
      throw new Error("\u963F\u91CC\u4E91OSS\u6682\u672A\u5B9E\u73B0\uFF0C\u8BF7\u4F7F\u7528\u817E\u8BAF\u4E91COS");
    case "qiniu":
      throw new Error("\u4E03\u725B\u4E91\u6682\u672A\u5B9E\u73B0\uFF0C\u8BF7\u4F7F\u7528\u817E\u8BAF\u4E91COS");
    default:
      throw new Error(`\u4E0D\u652F\u6301\u7684\u5BF9\u8C61\u5B58\u50A8\u670D\u52A1\u5546: ${cosConfig.provider}`);
  }
}
async function isObjectStorageAvailable() {
  const cosConfig = await getCOSConfigFromDB();
  return cosConfig !== null && cosConfig.isEnabled;
}

export { isObjectStorageAvailable as i, uploadToObjectStorage as u };
//# sourceMappingURL=cos-http-uploader.mjs.map
