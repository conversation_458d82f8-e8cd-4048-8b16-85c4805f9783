{"name": "cookie-es", "version": "2.0.0", "repository": "unjs/cookie-es", "license": "MIT", "sideEffects": false, "type": "module", "exports": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "types": "./dist/index.d.mts", "files": ["dist"], "scripts": {"build": "unbuild", "dev": "vitest --coverage", "lint": "eslint --cache . && prettier -c src test", "lint:fix": "automd && eslint --cache . --fix && prettier -c src test -w", "release": "pnpm test && pnpm build && changelogen --release --push && npm publish", "test": "pnpm lint && vitest run --coverage"}, "devDependencies": {"@types/node": "^22.13.5", "@vitest/coverage-v8": "^3.0.7", "automd": "^0.4.0", "changelogen": "^0.6.0", "eslint": "^9.21.0", "eslint-config-unjs": "^0.4.2", "prettier": "^3.5.2", "typescript": "^5.7.3", "unbuild": "^3.5.0", "vitest": "^3.0.7"}, "packageManager": "pnpm@10.5.2"}